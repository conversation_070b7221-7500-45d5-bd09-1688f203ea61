# Miscellaneous
*.class
*.log
*.pyc
*.swp
.DS_Store
.atom/
.build/
.buildlog/
.history
.svn/
.swiftpm/
.untranslated_messages.txt
migrate_working_dir/

# IntelliJ related
*.iml
*.ipr
*.iws
.idea/
.cxx/
.kotlin/

# The .vscode folder contains launch configuration and tasks you configure in
# VS Code which you may wish to be included in version control, so this line
# is commented out by default.
#.vscode/

# Flutter/Dart/Pub related
**/doc/api/
**/ios/Flutter/.last_build_id
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.pub-cache/
.pub/
/build/

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

# Android Studio will place build artifacts here
/android/app/debug
/android/app/profile
/android/app/release
pubspec.lock
ios/Podfile.lock
web/splash/img/dark-1x.png
web/splash/img/dark-2x.png
web/splash/img/dark-3x.png
web/splash/img/dark-4x.png
web/splash/img/light-1x.png
web/splash/img/light-2x.png
web/splash/img/light-3x.png
web/splash/img/light-4x.png
