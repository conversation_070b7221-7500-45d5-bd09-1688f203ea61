{"buildCommand": {"command": "prepareForIndexing", "enableIndexBuildArena": false, "targets": null}, "configuredTargets": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217"}, {"guid": "bfdfe7dc352907fc980b868725387e98312b4bc59bbbe2c06c205bf4da6737f5"}, {"guid": "bfdfe7dc352907fc980b868725387e98483832d3c820398e9d40e1a6904b03fe"}, {"guid": "bfdfe7dc352907fc980b868725387e98975ca575d1289bef32c9cad79a370b1b"}, {"guid": "bfdfe7dc352907fc980b868725387e9886480246dff0990da24465d140ad1874"}, {"guid": "bfdfe7dc352907fc980b868725387e98264017999ce710ef881da2f318a90524"}, {"guid": "bfdfe7dc352907fc980b868725387e9828cab1f188854e0a973e6ff6905c5ffe"}, {"guid": "bfdfe7dc352907fc980b868725387e98e0be3b0d5ad56f1985578b1f97431765"}, {"guid": "bfdfe7dc352907fc980b868725387e983786431ce548989b846bbf1a7384f58e"}, {"guid": "bfdfe7dc352907fc980b868725387e9894b6f514aa32ee4cfdd7fc11c1ff5321"}], "containerPath": "/Users/<USER>/Desktop/workspace/flutterworkspace/wukongimfluttersdk/example/ios/Pods/Pods.xcodeproj", "continueBuildingAfterErrors": true, "dependencyScope": "workspace", "enableIndexBuildArena": false, "hideShellScriptEnvironment": false, "parameters": {"action": "build", "arenaInfo": {"buildIntermediatesPath": "", "buildProductsPath": "", "derivedDataPath": "/Users/<USER>/Library/Developer/Xcode/DerivedData", "indexDataStoreFolderPath": "/Users/<USER>/Library/Developer/Xcode/DerivedData/Pods-bagnqmkkkgfdmnhkfbhokutyfxul/Index.noindex/DataStore", "indexEnableDataStore": true, "indexPCHPath": "/Users/<USER>/Library/Developer/Xcode/DerivedData/Pods-bagnqmkkkgfdmnhkfbhokutyfxul/Index.noindex/PrecompiledHeaders", "pchPath": ""}, "configurationName": "Release", "overrides": {"synthesized": {"table": {"ENABLE_PREVIEWS": "NO", "ENABLE_XOJIT_PREVIEWS": "NO", "ONLY_ACTIVE_ARCH": "YES"}}}}, "qos": "default", "schemeCommand": "launch", "showNonLoggedProgress": true, "useDryRun": true, "useImplicitDependencies": false, "useLegacyBuildLocations": false, "useParallelTargets": false}