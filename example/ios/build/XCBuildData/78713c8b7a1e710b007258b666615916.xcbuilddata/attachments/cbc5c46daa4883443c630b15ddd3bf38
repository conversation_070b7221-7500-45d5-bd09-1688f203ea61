{"case-sensitive": "false", "roots": [{"contents": [{"external-contents": "/Users/<USER>/Desktop/workspace/flutterworkspace/wukongimfluttersdk/example/ios/Pods/Target Support Files/Pods-Runner/Pods-Runner-umbrella.h", "name": "Pods-Runner-umbrella.h", "type": "file"}], "name": "/Users/<USER>/Desktop/workspace/flutterworkspace/wukongimfluttersdk/example/ios/build/Release-iphoneos/Pods_Runner.framework/Headers", "type": "directory"}, {"contents": [{"external-contents": "/Users/<USER>/Desktop/workspace/flutterworkspace/wukongimfluttersdk/example/ios/build/Pods.build/Release-iphoneos/Pods-Runner.build/module.modulemap", "name": "module.modulemap", "type": "file"}], "name": "/Users/<USER>/Desktop/workspace/flutterworkspace/wukongimfluttersdk/example/ios/build/Release-iphoneos/Pods_Runner.framework/Modules", "type": "directory"}, {"contents": [{"external-contents": "/Users/<USER>/Desktop/workspace/flutterworkspace/wukongimfluttersdk/example/ios/Pods/Target Support Files/Pods-RunnerTests/Pods-RunnerTests-umbrella.h", "name": "Pods-RunnerTests-umbrella.h", "type": "file"}], "name": "/Users/<USER>/Desktop/workspace/flutterworkspace/wukongimfluttersdk/example/ios/build/Release-iphoneos/Pods_RunnerTests.framework/Headers", "type": "directory"}, {"contents": [{"external-contents": "/Users/<USER>/Desktop/workspace/flutterworkspace/wukongimfluttersdk/example/ios/build/Pods.build/Release-iphoneos/Pods-RunnerTests.build/module.modulemap", "name": "module.modulemap", "type": "file"}], "name": "/Users/<USER>/Desktop/workspace/flutterworkspace/wukongimfluttersdk/example/ios/build/Release-iphoneos/Pods_RunnerTests.framework/Modules", "type": "directory"}, {"contents": [{"external-contents": "/Users/<USER>/Desktop/workspace/flutterworkspace/wukongimfluttersdk/example/ios/Pods/Target Support Files/Reachability/Reachability-umbrella.h", "name": "Reachability-umbrella.h", "type": "file"}, {"external-contents": "/Users/<USER>/Desktop/workspace/flutterworkspace/wukongimfluttersdk/example/ios/Pods/Reachability/Reachability.h", "name": "Reachability.h", "type": "file"}], "name": "/Users/<USER>/Desktop/workspace/flutterworkspace/wukongimfluttersdk/example/ios/build/Release-iphoneos/Reachability/Reachability.framework/Headers", "type": "directory"}, {"contents": [{"external-contents": "/Users/<USER>/Desktop/workspace/flutterworkspace/wukongimfluttersdk/example/ios/build/Pods.build/Release-iphoneos/Reachability.build/module.modulemap", "name": "module.modulemap", "type": "file"}], "name": "/Users/<USER>/Desktop/workspace/flutterworkspace/wukongimfluttersdk/example/ios/build/Release-iphoneos/Reachability/Reachability.framework/Modules", "type": "directory"}, {"contents": [{"external-contents": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/connectivity-3.0.6/ios/Classes/FLTConnectivityPlugin.h", "name": "FLTConnectivityPlugin.h", "type": "file"}, {"external-contents": "/Users/<USER>/Desktop/workspace/flutterworkspace/wukongimfluttersdk/example/ios/Pods/Target Support Files/connectivity/connectivity-umbrella.h", "name": "connectivity-umbrella.h", "type": "file"}], "name": "/Users/<USER>/Desktop/workspace/flutterworkspace/wukongimfluttersdk/example/ios/build/Release-iphoneos/connectivity/connectivity.framework/Headers", "type": "directory"}, {"contents": [{"external-contents": "/Users/<USER>/Desktop/workspace/flutterworkspace/wukongimfluttersdk/example/ios/build/Pods.build/Release-iphoneos/connectivity.build/module.modulemap", "name": "module.modulemap", "type": "file"}], "name": "/Users/<USER>/Desktop/workspace/flutterworkspace/wukongimfluttersdk/example/ios/build/Release-iphoneos/connectivity/connectivity.framework/Modules", "type": "directory"}, {"contents": [{"external-contents": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/encryptions-1.1.0+1/ios/Classes/EncryptionsPlugin.h", "name": "EncryptionsPlugin.h", "type": "file"}, {"external-contents": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/encryptions-1.1.0+1/ios/Classes/argon2/argon2.h", "name": "argon2.h", "type": "file"}, {"external-contents": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/encryptions-1.1.0+1/ios/Classes/argon2/blake2-impl.h", "name": "blake2-impl.h", "type": "file"}, {"external-contents": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/encryptions-1.1.0+1/ios/Classes/argon2/blake2.h", "name": "blake2.h", "type": "file"}, {"external-contents": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/encryptions-1.1.0+1/ios/Classes/argon2/blamka-round-ref.h", "name": "blamka-round-ref.h", "type": "file"}, {"external-contents": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/encryptions-1.1.0+1/ios/Classes/argon2/core.h", "name": "core.h", "type": "file"}, {"external-contents": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/encryptions-1.1.0+1/ios/Classes/argon2/encoding.h", "name": "encoding.h", "type": "file"}, {"external-contents": "/Users/<USER>/Desktop/workspace/flutterworkspace/wukongimfluttersdk/example/ios/build/Release-iphoneos/encryptions/encryptions.framework/Headers/encryptions-Swift.h", "name": "encryptions-Swift.h", "type": "file"}, {"external-contents": "/Users/<USER>/Desktop/workspace/flutterworkspace/wukongimfluttersdk/example/ios/Pods/Target Support Files/encryptions/encryptions-umbrella.h", "name": "encryptions-umbrella.h", "type": "file"}, {"external-contents": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/encryptions-1.1.0+1/ios/Classes/argon2/thread.h", "name": "thread.h", "type": "file"}], "name": "/Users/<USER>/Desktop/workspace/flutterworkspace/wukongimfluttersdk/example/ios/build/Release-iphoneos/encryptions/encryptions.framework/Headers", "type": "directory"}, {"contents": [{"external-contents": "/Users/<USER>/Desktop/workspace/flutterworkspace/wukongimfluttersdk/example/ios/build/Pods.build/Release-iphoneos/encryptions.build/module.modulemap", "name": "module.modulemap", "type": "file"}], "name": "/Users/<USER>/Desktop/workspace/flutterworkspace/wukongimfluttersdk/example/ios/build/Release-iphoneos/encryptions/encryptions.framework/Modules", "type": "directory"}, {"contents": [{"external-contents": "/Users/<USER>/Desktop/workspace/flutterworkspace/wukongimfluttersdk/example/ios/build/Release-iphoneos/shared_preferences_foundation/shared_preferences_foundation.framework/Headers/shared_preferences_foundation-Swift.h", "name": "shared_preferences_foundation-Swift.h", "type": "file"}, {"external-contents": "/Users/<USER>/Desktop/workspace/flutterworkspace/wukongimfluttersdk/example/ios/Pods/Target Support Files/shared_preferences_foundation/shared_preferences_foundation-umbrella.h", "name": "shared_preferences_foundation-umbrella.h", "type": "file"}], "name": "/Users/<USER>/Desktop/workspace/flutterworkspace/wukongimfluttersdk/example/ios/build/Release-iphoneos/shared_preferences_foundation/shared_preferences_foundation.framework/Headers", "type": "directory"}, {"contents": [{"external-contents": "/Users/<USER>/Desktop/workspace/flutterworkspace/wukongimfluttersdk/example/ios/build/Pods.build/Release-iphoneos/shared_preferences_foundation.build/module.modulemap", "name": "module.modulemap", "type": "file"}], "name": "/Users/<USER>/Desktop/workspace/flutterworkspace/wukongimfluttersdk/example/ios/build/Release-iphoneos/shared_preferences_foundation/shared_preferences_foundation.framework/Modules", "type": "directory"}, {"contents": [{"external-contents": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/sqflite-2.3.3+1/darwin/Classes/SqfliteCursor.h", "name": "SqfliteCursor.h", "type": "file"}, {"external-contents": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/sqflite-2.3.3+1/darwin/Classes/SqfliteDarwinDB.h", "name": "SqfliteDarwinDB.h", "type": "file"}, {"external-contents": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/sqflite-2.3.3+1/darwin/Classes/SqfliteDarwinDatabase.h", "name": "SqfliteDarwinDatabase.h", "type": "file"}, {"external-contents": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/sqflite-2.3.3+1/darwin/Classes/SqfliteDarwinDatabaseAdditions.h", "name": "SqfliteDarwinDatabaseAdditions.h", "type": "file"}, {"external-contents": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/sqflite-2.3.3+1/darwin/Classes/SqfliteDarwinDatabaseQueue.h", "name": "SqfliteDarwinDatabaseQueue.h", "type": "file"}, {"external-contents": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/sqflite-2.3.3+1/darwin/Classes/SqfliteDarwinImport.h", "name": "SqfliteDarwinImport.h", "type": "file"}, {"external-contents": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/sqflite-2.3.3+1/darwin/Classes/SqfliteDarwinResultSet.h", "name": "SqfliteDarwinResultSet.h", "type": "file"}, {"external-contents": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/sqflite-2.3.3+1/darwin/Classes/SqfliteDatabase.h", "name": "SqfliteDatabase.h", "type": "file"}, {"external-contents": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/sqflite-2.3.3+1/darwin/Classes/SqfliteImport.h", "name": "SqfliteImport.h", "type": "file"}, {"external-contents": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/sqflite-2.3.3+1/darwin/Classes/SqfliteOperation.h", "name": "SqfliteOperation.h", "type": "file"}, {"external-contents": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/sqflite-2.3.3+1/darwin/Classes/SqflitePlugin.h", "name": "SqflitePlugin.h", "type": "file"}, {"external-contents": "/Users/<USER>/Desktop/workspace/flutterworkspace/wukongimfluttersdk/example/ios/Pods/Target Support Files/sqflite/sqflite-umbrella.h", "name": "sqflite-umbrella.h", "type": "file"}], "name": "/Users/<USER>/Desktop/workspace/flutterworkspace/wukongimfluttersdk/example/ios/build/Release-iphoneos/sqflite/sqflite.framework/Headers", "type": "directory"}, {"contents": [{"external-contents": "/Users/<USER>/Desktop/workspace/flutterworkspace/wukongimfluttersdk/example/ios/build/Pods.build/Release-iphoneos/sqflite.build/module.modulemap", "name": "module.modulemap", "type": "file"}], "name": "/Users/<USER>/Desktop/workspace/flutterworkspace/wukongimfluttersdk/example/ios/build/Release-iphoneos/sqflite/sqflite.framework/Modules", "type": "directory"}], "version": 0}