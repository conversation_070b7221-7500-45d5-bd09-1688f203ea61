-target arm64-apple-ios12.0 '-std=gnu11' -fmodules -gmodules '-fmodules-cache-path=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex' '-fmodule-name=encryptions' -fpascal-strings -Os -fno-common '-DPOD_CONFIGURATION_RELEASE=1' '-DCOCOAPODS=1' -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.1.sdk -g -iquote /Users/<USER>/Desktop/workspace/flutterworkspace/wukongimfluttersdk/example/ios/build/Pods.build/Release-iphoneos/encryptions.build/encryptions-generated-files.hmap -I/Users/<USER>/Desktop/workspace/flutterworkspace/wukongimfluttersdk/example/ios/build/Pods.build/Release-iphoneos/encryptions.build/encryptions-own-target-headers.hmap -I/Users/<USER>/Desktop/workspace/flutterworkspace/wukongimfluttersdk/example/ios/build/Pods.build/Release-iphoneos/encryptions.build/encryptions-all-non-framework-target-headers.hmap -ivfsoverlay /Users/<USER>/Desktop/workspace/flutterworkspace/wukongimfluttersdk/example/ios/build/Pods.build/Release-iphoneos/Pods-bfdfe7dc352907fc980b868725387e98-VFS-iphoneos/all-product-headers.yaml -iquote /Users/<USER>/Desktop/workspace/flutterworkspace/wukongimfluttersdk/example/ios/build/Pods.build/Release-iphoneos/encryptions.build/encryptions-project-headers.hmap -I/Users/<USER>/Desktop/workspace/flutterworkspace/wukongimfluttersdk/example/ios/build/Release-iphoneos/encryptions/include -I/Users/<USER>/Desktop/workspace/flutterworkspace/wukongimfluttersdk/example/ios/build/Pods.build/Release-iphoneos/encryptions.build/DerivedSources-normal/arm64 -I/Users/<USER>/Desktop/workspace/flutterworkspace/wukongimfluttersdk/example/ios/build/Pods.build/Release-iphoneos/encryptions.build/DerivedSources/arm64 -I/Users/<USER>/Desktop/workspace/flutterworkspace/wukongimfluttersdk/example/ios/build/Pods.build/Release-iphoneos/encryptions.build/DerivedSources -F/Users/<USER>/Desktop/workspace/flutterworkspace/wukongimfluttersdk/example/ios/build/Release-iphoneos/encryptions -F/Users/<USER>/Desktop/workspace/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64