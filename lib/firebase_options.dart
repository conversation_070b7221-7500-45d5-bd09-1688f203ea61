// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyDkFtrlW6fFKPsSuRQrMld-WVCK3r_fca4',
    appId: '1:81394891195:web:8b9d9e575184ee729e2c9e',
    messagingSenderId: '81394891195',
    projectId: 'gleezy-test-01',
    authDomain: 'gleezy-test-01.firebaseapp.com',
    storageBucket: 'gleezy-test-01.firebasestorage.app',
    measurementId: 'G-PHBDFWL6ZD',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyAY5Wfqe_i1Vu1mMSN6CXk4vqZO5shV3-o',
    appId: '1:278469936736:android:69a94416f7d895750aa505',
    messagingSenderId: '278469936736',
    projectId: 'gleezy-f7b69',
    storageBucket: 'gleezy-f7b69.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyDUzMBnfE4YSakmdW0pPbLWlX0zoKkm8MA',
    appId: '1:81394891195:ios:f58d29b1ceb1dbff9e2c9e',
    messagingSenderId: '81394891195',
    projectId: 'gleezy-test-01',
    storageBucket: 'gleezy-test-01.firebasestorage.app',
    iosBundleId: 'com.gg.gleezy',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyDUzMBnfE4YSakmdW0pPbLWlX0zoKkm8MA',
    appId: '1:81394891195:ios:6472073193e1075a9e2c9e',
    messagingSenderId: '81394891195',
    projectId: 'gleezy-test-01',
    storageBucket: 'gleezy-test-01.firebasestorage.app',
    iosBundleId: 'com.xin.gleezy.and',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyDkFtrlW6fFKPsSuRQrMld-WVCK3r_fca4',
    appId: '1:81394891195:web:37e811613cbc4ea19e2c9e',
    messagingSenderId: '81394891195',
    projectId: 'gleezy-test-01',
    authDomain: 'gleezy-test-01.firebaseapp.com',
    storageBucket: 'gleezy-test-01.firebasestorage.app',
    measurementId: 'G-EHXXHDX7C2',
  );
}
