import 'package:and/http/http_config.dart';
import 'package:and/model/country.dart';
import 'package:and/model/request/send_mail_request.dart';
import 'package:and/model/request/send_sms_request.dart';
import 'package:and/model/response/common_response.dart';
import 'package:and/model/response/send_sms_response.dart';
import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';

part 'share.g.dart';

@RestApi(baseUrl: HttpConfig.baseApiURL)
abstract class ShareApi {
  factory ShareApi(Dio dio, {String? baseUrl}) = _ShareApi;

  /// 发送邮件验证码
  @POST("/share/send_mail")
  Future<CommonResponse> sendMail(@Body() SendMailRequest body);
}
