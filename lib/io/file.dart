import 'dart:io';

import 'package:and/http/http_config.dart';
import 'package:and/model/response/common_response.dart';
import 'package:and/model/upload_file.dart';
import 'package:and/model/upload_url.dart';
import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';

part 'file.g.dart';

@RestApi(baseUrl: HttpConfig.baseApiURL)
abstract class FileApi {
  factory FileApi(Dio dio, {String? baseUrl}) = _FileApi;

  /// 获取文件上传路径
  @GET("/file/upload")
  Future<UploadUrl> getUploadUrl({
    @Query("path") String? path,
    @Query("type") String? type,
    @Query("isLarge") bool? isLarge,
    @Query("width") int? width,
    @Query("height") int? height,
  });

  /// 上传文件
  @POST("{url}")
  @MultiPart()
  Future<UploadFile> upload({
    @Path() String? url,
    @Part() File? file,
  });

  /// 上传头像
  @POST("/users/{uid}/avatar")
  @MultiPart()
  Future<CommonResponse> uploadAvatar({
    @Path('uid') required String uid,
    @Part(contentType:"image/png") required File file,
  });
}
