import 'package:and/http/http_config.dart';
import 'package:and/model/channel_msg_sync.dart';
import 'package:and/model/extension/reactions_request.dart';
import 'package:and/model/message.dart';
import 'package:and/model/message_extra.dart';
import 'package:and/model/reminder.dart';
import 'package:and/model/request/delete_msg_request.dart';
import 'package:and/model/request/message_extra_sync_request.dart';
import 'package:and/model/request/message_pinned_request.dart';
import 'package:and/model/request/message_readed_request.dart';
import 'package:and/model/request/offset_msg_request.dart';
import 'package:and/model/request/sync_channel_msg_request.dart';
import 'package:and/model/request/sync_msg_request.dart';
import 'package:and/model/request/sync_reaction_request.dart';
import 'package:and/model/request/sync_reminder_request.dart';
import 'package:and/model/request/sync_unread_count_request.dart';
import 'package:and/model/response/common_response.dart';
import 'package:and/model/sync_pinned_message.dart';
import 'package:and/model/sync_reaction.dart';
import 'package:and/model/sync_unread_count.dart';
import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';

part 'message.g.dart';

@RestApi(baseUrl: HttpConfig.baseApiURL)
abstract class MessageApi {
  factory MessageApi(Dio dio, {String? baseUrl}) = _MessageApi;

  /// 同步消息扩展
  @POST("/message/extra/sync")
  @Extra({"showErrorMsg": true})
  Future<List<MessageExtra>> syncExtraMsg(
      {@Body() MessageExtraSyncRequest? body});

  /// 同步频道消息
  @POST("/message/channel/sync")
  Future<ChannelMsgSync> syncChannelMsg(@Body() SyncChannelMsgRequest body);

  /// 撤回消息
  @POST("/message/revoke")
  Future<void> revokeMsg({
    @Query("message_id") required String messageId,
    @Query("client_msg_no") required String clientMsgNo,
    @Query("channel_id") String? channelId,
    @Query("channel_type") int? channelType,
  });

  /// 删除消息
  @DELETE("/message")
  Future<void> deleteMsg(@Body() List<DeleteMsgRequest> body);

  /// 可选消息双向删除
  @DELETE("/message/all")
  Future<void> deleteMsgAll(@Body() List<DeleteMsgRequest> body);

  /// 消息置顶
  @POST("/message/pinned")
  Future<void> pinMessage(@Body() MessagePinnedRequest body);

  /// 获取置顶消息
  @POST("/message/pinned/sync")
  Future<SyncPinnedMessage> syncPinnedMessages(@Body() MessagePinnedRequest body);

  /// 取消置顶消息
  @POST("/message/pinned/clear")
  Future<void> unpinMessage(@Body() MessagePinnedRequest body);

  /// 清除频道消息
  @POST("/message/offset")
  Future<CommonResponse> offsetMsg(@Body() OffsetMsgRequest body);

  /// 双向清除某频道消息
  @POST("/message/offset/all")
  Future<CommonResponse> offsetMsgBoth(@Body() OffsetMsgRequest body);

  /// 同步提醒消息
  @POST("/message/reminder/sync")
  Future<List<Reminder>> syncReminder(@Body() SyncReminderRequest body);

  /// 完成提醒消息
  @POST("/message/reminder/done")
  Future<CommonResponse> doneReminder(@Body() List<String> ids);

  /// 标记消息已读
  @POST("/message/readed")
  Future<CommonResponse> readed(@Body() MessageReadedRequest body);

  ///同步cmd消息
  @POST("/message/sync")
  Future<List<Message>> syncMsg(@Body() SyncMsgRequest body);

  /// 同步离线最近会话回执
  @POST("/message/syncack/{last_message_seq}")
  Future<CommonResponse> ackMsg(@Path("last_message_seq") int lastMessageSeq);

  /// 添加或取消回应
  @POST("/reactions")
  Future<CommonResponse> reactions(@Body() ReactionsRequest body);

  ///同步回应
  @POST("/reaction/sync")
  Future<List<SyncReaction>> syncReaction(@Body() SyncReactionRequest body);

  ///同步回应
  @POST("/message/channel/sync/unreaded_num")
  Future<SyncUnreadCount> syncUnreadCount(@Body() SyncUnreadCountRequest body);
}
