import 'package:and/http/http_config.dart';
import 'package:and/model/group.dart';
import 'package:and/model/group_detail.dart';
import 'package:and/model/group_member.dart';
import 'package:and/model/group_qr.dart';
import 'package:and/model/request/group_member_request.dart';
import 'package:and/model/request/create_group_request.dart';
import 'package:and/model/response/common_response.dart';
import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';

part 'group.g.dart';

@RestApi(baseUrl: HttpConfig.baseApiURL)
abstract class GroupApi {
  factory GroupApi(Dio dio, {String? baseUrl}) = _GroupApi;

  /// 获取群组信息
  @GET("/groups/{groupNo}")
  Future<Group> getGroupInfo(@Path("groupNo") String groupNo);

  /// 获取群组详情
  @GET("/groups/{groupNo}/detail")
  Future<GroupDetail> getGroupDetail(@Path("groupNo") String groupNo);

  /// 创建群组
  @POST("/group/create")
  @Extra({"showErrorMsg": true})
  Future<Group> createGroup(@Body() CreateGroupRequest body);

  /// 更新群组信息
  @PUT("/groups/{groupNo}/setting")
  @Extra({"showErrorMsg": true})
  Future<void> updateGroupSetting(
      @Path("groupNo") String groupNo, @Body() Map<String, dynamic> body);

  /// 更新群组信息
  @PUT("/groups/{groupNo}")
  @Extra({"showErrorMsg": true})
  Future<CommonResponse> updateGroupInfo(
      @Path("groupNo") String groupNo, @Body() Map<String, dynamic> body);

  @GET("/groups/{groupNo}/membersync")
  Future<List<GroupMember>> syncGroupMembers(@Path("groupNo") String groupNo,
      @Query("limit") int limit, @Query("version") int version);

  @POST("/groups/{groupNo}/members")
  @Extra({"showErrorMsg": true})
  Future<CommonResponse> addGroupMembers(
      @Path("groupNo") String groupNo, @Body() GroupMemberRequest body);

  @POST("/groups/{groupNo}/member/invite")
  @Extra({"showErrorMsg": true})
  Future<CommonResponse> inviteGroupMembers(
      @Path("groupNo") String groupNo, @Body() GroupMemberRequest body);

  @DELETE("/groups/{groupNo}/members")
  @Extra({"showErrorMsg": true})
  Future<CommonResponse> deleteGroupMembers(
      @Path("groupNo") String groupNo, @Body() GroupMemberRequest body);

  @PUT("/groups/{groupNo}/members/{uid}")
  @Extra({"showErrorMsg": true})
  Future<CommonResponse> updateGroupMemberInfo(@Path("groupNo") String groupNo,
      @Path("uid") String uid, @Body() Map<String, dynamic> body);

  @GET("/groups/{groupNo}/qrcode")
  Future<GroupQr> getGroupQr(@Path("groupNo") String groupNo);

  @GET("/group/my")
  Future<List<Group>> getMyGroups();

  @POST("/groups/{groupNo}/exit")
  @Extra({"showErrorMsg": true})
  Future<CommonResponse> exitGroup(@Path("groupNo") String groupNo);

  /// 扫码加入群组
  @GET("/groups/{groupNo}/scanjoin")
  Future<CommonResponse> scanJoin(
      @Path("groupNo") String groupNo, @Query("auth_code") String authCode);

  /// 群全员禁言
  @POST("/groups/{groupNo}/forbidden/{on}")
  Future<CommonResponse> groupForbidden(
      @Path("groupNo") String groupNo, @Path("on") int on);

  /// 添加群管理员
  @POST("/groups/{groupNo}/managers")
  Future<CommonResponse> addGroupManagers(
      @Path("groupNo") String groupNo, @Body() String body);

  /// 删除群管理员
  @DELETE("/groups/{groupNo}/managers")
  Future<CommonResponse> deleteGroupManagers(
      @Path("groupNo") String groupNo, @Body() String body);
}
