import 'package:and/http/http_config.dart';
import 'package:and/model/conversation_extra.dart';
import 'package:and/model/conversation_sync.dart';
import 'package:and/model/country.dart';
import 'package:and/model/sync_conver_extra.dart';
import 'package:and/model/request/clear_unread_request.dart';
import 'package:and/model/request/conversation_extra_request.dart';
import 'package:and/model/request/sync_conversation_extra_request.dart';
import 'package:and/model/request/sync_conversation_request.dart';
import 'package:and/model/request/syncack_request.dart';
import 'package:and/model/response/common_response.dart';
import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';

part 'conversation.g.dart';

@RestApi(baseUrl: HttpConfig.baseApiURL)
abstract class ConversationApi {
  factory ConversationApi(Dio dio, {String? baseUrl}) = _ConversationApi;

  /// 同步会话
  @POST("/conversation/sync")
  Future<ConversationSync> syncConversation(
      @Body() SyncConversationRequest body);

  /// 同步会话扩展
  @POST("/conversation/extra/sync")
  Future<List<ConversationExtra>> syncCoverExtra(
      @Body() SyncConversationExtraRequest body);

  /// 同步会话扩展
  @POST("/conversations/{channel_id}/{channel_type}/extra")
  Future<CommonResponse> updateCoverExtra(@Path("channel_id") String channelID,
      @Path("channel_type") int channelType, @Body() ConversationExtraRequest body);

  /// 清空未读消息
  @PUT("/conversation/clearUnread")
  Future<void> clearUnread(@Body() ClearUnreadRequest body);

  /// 同步离线最近会话回执
  @POST("/conversation/syncack")
  Future<CommonResponse> ackCoverMsg(@Body() SyncackRequest body);

}
