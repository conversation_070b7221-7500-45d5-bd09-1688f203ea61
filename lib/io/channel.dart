import 'package:and/http/http_config.dart';
import 'package:and/model/call_response.dart';
import 'package:and/model/channel_info.dart';
import 'package:and/model/channel_state.dart';
import 'package:and/model/country.dart';
import 'package:and/model/request/call_request.dart';
import 'package:and/model/response/common_response.dart';
import 'package:and/model/scan_result.dart';
import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';

part 'channel.g.dart';

@RestApi(baseUrl: HttpConfig.baseApiURL)
abstract class ChannelApi {
  factory ChannelApi(Dio dio, {String? baseUrl}) = _ChannelApi;

  /// 频道状态
  @GET("/channel/state")
  Future<ChannelState> getChannelState(
    @Query("channel_id") String channelID,
    @Query("channel_type") int channelType,
  );

  /// 频道信息
  @GET("/channels/{channelID}/{channelType}")
  Future<ChannelInfo> getChannel(
      {@Path("channelID") required String channelID,
      @Path("channelType") required int channelType});

  /// 发起单聊通话 callType:1:语音通话 2视频通话
  @POST("/channels/{channelID}/singlecall/{callType}")
  Future<CallResponse> startSingleCall(
      {@Path("channelID") required String channelID,
      @Path("callType") required int callType});

  /// 发起群聊通话
  @POST("/channels/{channelID}/groupcall")
  Future<CallResponse> startGroupCall(
      {@Path("channelID") required String channelID});

  /// 获取群聊token
  @POST("/channels/{channelID}/grouptoken")
  Future<String> getGroupCallToken(
      {@Path("channelID") required String channelID});

  /// 加入通话
  @POST("/channels/{channelID}/{channelType}/joincall")
  Future<CommonResponse> joinCall(
      {@Path("channelID") required String channelID,
      @Path("channelType") required int channelType,
      @Body() required CallRequest body});

  /// 拒绝通话
  @POST("/channels/{channelID}/{channelType}/rejectcall")
  Future<CommonResponse> rejectCall(
      {@Path("channelID") required String channelID,
      @Path("channelType") required int channelType,
      @Body() required CallRequest body});

  /// 挂断通话
  @POST("/channels/{channelID}/{channelType}/onlyhandupcall")
  Future<CommonResponse> hangupCall(
      {@Path("channelID") required String channelID,
      @Path("channelType") required int channelType,
      @Body() required CallRequest body});

  /// 取消通话邀请
  @POST("/channels/{channelID}/{channelType}/onlycancelcall")
  Future<CommonResponse> cancelCall(
      {@Path("channelID") required String channelID,
        @Path("channelType") required int channelType,
        @Body() required CallRequest body});

  /// 获取对方设置（是否允许通话av_chat_disabled: 0允许，1不允许）
  @GET("/channels/{channelID}/{channelType}/counterpart/setting")
  Future<ChannelInfo> getCounterpartSetting(
      {@Path("channelID") required String channelID,
      @Path("channelType") required int channelType});
}
