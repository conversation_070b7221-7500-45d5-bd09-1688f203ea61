import 'dart:io';

import 'package:and/http/http_config.dart';
import 'package:and/model/friend_apply.dart';
import 'package:and/model/request/apply_friend_request.dart';
import 'package:and/model/request/sure_friend_request.dart';
import 'package:and/model/request/update_remark_request.dart';
import 'package:and/model/response/common_response.dart';
import 'package:and/model/upload_file.dart';
import 'package:and/model/upload_url.dart';
import 'package:and/model/user_info.dart';
import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';

part 'friend.g.dart';

@RestApi(baseUrl: HttpConfig.baseApiURL)
abstract class FriendApi {
  factory FriendApi(Dio dio, {String? baseUrl}) = _FriendApi;

  /// 申请加好友
  @POST("/friend/apply")
  Future<CommonResponse> applyAddFriend(@Body() ApplyFriendRequest body);

  /// 确认加好友
  @POST("/friend/sure")
  Future<CommonResponse> agreeFriendApply(@Body() SureFriendRequest body);

  /// 同步好友
  @GET("/friend/sync")
  Future<List<UserInfo>> syncFriends(
    @Query("version") int version,
    @Query("limit") int limit,
    @Query("api_version") int apiVersion,
  );

  /// 更新好友备注
  @PUT("/friend/remark")
  Future<CommonResponse> updateFriendRemark(@Body() UpdateRemarkRequest body);

  /// 删除好友
  @DELETE("/friends/{uid}")
  Future<CommonResponse> deleteFriend(@Path("uid") String uid);

  /// 申请加好友列表
  @GET("/friend/apply")
  Future<List<FriendApply>> friendApplyList(
      @Query("page_index") int page, @Query("page_size") int pageSize);
}
