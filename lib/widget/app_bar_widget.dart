import 'package:and/common/res/text_styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

PreferredSizeWidget AppBarWidget(BuildContext context, String title,
    {bool showBack = true,
    bool centerTitle = true,
    double? elevation,
    Color? backgroundColor = Colors.white,
    List<Widget>? actions,
    Function()? onBack}) {
  return AppBar(
      centerTitle: centerTitle,
      elevation: elevation,
      systemOverlayStyle:
          const SystemUiOverlayStyle(statusBarIconBrightness: Brightness.dark),
      backgroundColor: backgroundColor,
      automaticallyImplyLeading: showBack,
      leading: showBack
          ? IconButton(
              icon: BackButton(),
              onPressed: onBack ??
                  () {
                    FocusScope.of(context).requestFocus(FocusNode()); //失去焦点
                    Navigator.pop(context);
                  },
            )
          : null,
      title: Text(title),
      actions: actions);
}

Widget DialogBarWidget(String title,
    {Function()? onClose, bool showClose = true}) {
  return Padding(
      padding: const EdgeInsets.symmetric(vertical: 10),
      child: Stack(
        children: [
          Center(child: Text(title, style: TextStyles.fontSize16Bold)),
          Positioned(
              top: 0,
              bottom: 0,
              right: 0,
              child: Visibility(
                  visible: showClose,
                  child: IconButton(
                      onPressed: () {
                        onClose?.call() ?? Get.back();
                      },
                      icon: CloseButton())))
        ],
      ));
}
