import 'package:flutter/material.dart';

class CommonCard extends StatelessWidget {
  final Widget child;
  final double elevation;
  final Color color;
  final Color sideColor;
  final Color? shadowColor;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final double? borderRadius;

  const CommonCard(
      {super.key,
      required this.child,
      this.color = Colors.white,
      this.sideColor = Colors.transparent,
      this.shadowColor,
      this.elevation = 5,
      this.padding,
      this.margin,
      this.borderRadius});

  @override
  Widget build(BuildContext context) {
    return Card(
      color: color,
      semanticContainer: false,
      margin: margin ?? const EdgeInsets.all(5),
      shape: RoundedRectangleBorder(
        side: BorderSide(color: sideColor, width: 1.0),
        borderRadius: BorderRadius.all(Radius.circular(borderRadius ?? 6)),
      ),
      shadowColor: shadowColor ?? Colors.black.withOpacity(0.16),
      // 使用 shadowColor，如果为空则使用默认值
      elevation: elevation,
      clipBehavior: Clip.antiAlias,
      child: Container(
        width: double.infinity,
        padding: padding ?? const EdgeInsets.all(15),
        child: child,
      ),
    );
  }
}
