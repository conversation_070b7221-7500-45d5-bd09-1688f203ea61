import 'package:and/common/res/colours.dart';
import 'package:and/common/res/text_styles.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/utils/image_path.dart';
import 'package:and/widget/submit_button.dart';
import 'package:flutter/material.dart';

sealed class FitLoadStatus {
  const FitLoadStatus();
}

class FitLoading extends FitLoadStatus {
  const FitLoading();

  @override
  String toString() => 'Loading';
}

class FitLoadSuccess<T> extends FitLoadStatus {
  final bool hasMore;

  const FitLoadSuccess(this.hasMore);

  @override
  String toString() => 'LoadSuccess(hasMore: $hasMore)';
}

class FitLoadError extends FitLoadStatus {
  final String? errorMessage;

  const FitLoadError({this.errorMessage});

  @override
  String toString() => 'LoadError(errorMessage: $errorMessage)';
}

class FitLoadErrorWidget extends StatelessWidget {
  final String? icon;
  final String? msg;
  final String? tip;
  final String? btnStr;
  final Color? backgroundColor;
  final EdgeInsetsGeometry? margin;
  final VoidCallback? onPressedRefresh;

  const FitLoadErrorWidget(
      {Key? key,
      this.icon,
      this.msg,
      this.tip,
      this.btnStr,
      this.margin,
      this.backgroundColor,
      this.onPressedRefresh})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 10),
      color: backgroundColor ?? Colors.transparent,
      margin: margin != null ? margin! : EdgeInsets.zero,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            Image.asset(icon!, width: 140, height: 100),
            Container(
                margin: EdgeInsets.only(top: 18),
                child: Text(
                  msg ?? "",
                  style: TextStyle(color: DColor.hintText, fontSize: 14),
                )),
            Container(
                margin: EdgeInsets.only(top: 1),
                child: Text(
                  tip ?? "",
                  style: TextStyle(fontSize: 12, color: DColor.hintText),
                )),
            Container(
                margin: EdgeInsets.only(top: 20),
                width: 128,
                child: SubmitPrimaryButton(
                  text: btnStr ?? context.l10n.refresh,
                  onPressed: () {
                    onPressedRefresh?.call();
                  },
                ))
          ],
        ),
      ),
    );
  }
}

class FitLoadEmpty extends StatelessWidget {
  final String? tip;
  final String image;
  final Widget? tipWidget;
  final EdgeInsetsGeometry? margin;
  final double imageWidth;
  final double imageHeight;
  final double padTop;
  final bool visibleImage;
  final Color? backgroundColor;

  const FitLoadEmpty({
    super.key,
    this.image = ImagePath.bg_empty_record,
    this.tip,
    this.margin,
    this.tipWidget,
    this.imageWidth = 140,
    this.imageHeight = 100,
    this.padTop = 15,
    this.visibleImage = true,
    this.backgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 30),
      width: double.infinity,
      margin: margin,
      color: backgroundColor ?? DColor.background,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          Visibility(
            visible: visibleImage,
            child: Image(
              image: AssetImage(image),
              width: imageWidth,
              height: imageHeight,
            ),
          ),
          Container(
            margin: EdgeInsets.only(top: padTop),
            child: tipWidget != null
                ? tipWidget!
                : Text(
                    tip!,
                    style: TextStyles.fontSize15Normal
                        .copyWith(color: DColor.secondaryTextColor),
                  ),
          ),
        ],
      ),
    );
  }
}
