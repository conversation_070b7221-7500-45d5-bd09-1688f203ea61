import 'dart:async';

import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'load_state.dart';

abstract class RefreshState<T extends StatefulWidget> extends State<T>
    with AutomaticKeepAliveClientMixin {
  /// 刷新控件的 Controller
  EasyRefreshController refreshController = EasyRefreshController(
    controlFinishRefresh: true,
    controlFinishLoad: true,
  );

  late StreamSubscription _refreshStream;

  Rx<FitLoadStatus> getLoadState();

  @override
  void initState() {
    super.initState();

    _refreshStream = getLoadState().listen((value) {
      if (value is FitLoadSuccess || value is FitLoadError) {
        var isRefreshing =
            refreshController.headerState?.mode == IndicatorMode.processing;
        var isLoading =
            refreshController.footerState?.mode == IndicatorMode.processing;

        var hasMore = true;
        if (value is FitLoadSuccess<bool>) {
          hasMore = value.hasMore;
        }
        if (isRefreshing) {
          finishRefresh();
        } else if (isLoading) {
          finishLoad(hasMore);
        }
      }
    });
  }

  void finishRefresh() {
    refreshController.finishRefresh();
    refreshController.resetFooter();
  }

  void finishLoad(bool hasMore) {
    refreshController
        .finishLoad(hasMore ? IndicatorResult.success : IndicatorResult.noMore);
  }

  @override
  void dispose() {
    _refreshStream.cancel();
    refreshController.dispose();
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;
}
