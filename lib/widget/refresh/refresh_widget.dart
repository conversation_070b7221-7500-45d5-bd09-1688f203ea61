import 'package:and/app.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/utils/image_path.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';

import 'load_state.dart';

Widget buildEmptyWidget(
    {required EasyRefreshController refreshController,
    required FitLoadStatus loadStatus,
    ScrollPhysics? physics}) {
  return CustomScrollView(physics: physics, slivers: [
    SliverFillRemaining(
        child: buildLoadStateWidget(loadStatus, () {
      refreshController.callRefresh();
    }))
  ]);
}

Widget buildRefreshWidget(
    {required Widget Function(ScrollPhysics? physics) builder,
    required EasyRefreshController refreshController,
    required FitLoadStatus loadStatus,
    VoidCallback? onRefresh,
    VoidCallback? onLoad,
    bool hasData = false,
    bool showNoMoreData = true,
    bool refreshOnStart = true,
    bool? reverse,
    Header? header,
    Footer? footer}) {
  return EasyRefresh.builder(
      refreshOnStart: refreshOnStart,
      controller: refreshController,
      header: header ?? const MaterialHeader(),
      footer: footer ?? const MaterialFooter(),
      onRefresh: onRefresh,
      onLoad: onLoad,
      childBuilder: (context, physics) {
        if (hasData) {
          return builder(physics);
        }
        return buildEmptyWidget(
            refreshController: refreshController,
            physics: physics,
            loadStatus: loadStatus);
      });
}

Widget buildLoadStateWidget(FitLoadStatus loadStatus, VoidCallback onRefresh,
    {String? emptyTip,
    String? emptyIcon,
    bool visibleImage = true,
    Widget? headWidget,
    Widget? tipWidget,
    Color? backgroundColor}) {
  Widget statusWidget = const SizedBox();
  if (loadStatus is FitLoadSuccess) {
    statusWidget = FitLoadEmpty(
        backgroundColor: backgroundColor ?? Colors.transparent,
        tip: emptyTip ?? globalContext?.l10n.emptyDataMessage ?? 'No data',
        tipWidget: tipWidget,
        visibleImage: visibleImage,
        image: emptyIcon ?? ImagePath.bg_empty_record);
  } else if (loadStatus is FitLoadError) {
    statusWidget = FitLoadErrorWidget(
        icon: ImagePath.img_network_error,
        msg: globalContext?.l10n.noNetwork ?? "Something wrong",
        tip: " ",
        backgroundColor: backgroundColor,
        onPressedRefresh: () {
          onRefresh();
        });
  }
  if (headWidget != null) {
    return Column(children: [headWidget, Expanded(child: statusWidget)]);
  }

  return statusWidget;
}
