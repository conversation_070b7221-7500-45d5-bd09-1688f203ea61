import 'package:and/common/res/colours.dart';
import 'package:and/common/res/text_styles.dart';
import 'package:flutter/material.dart';

class SimpleSettingItemWidget extends StatelessWidget {
  final String title;
  final String? value;
  final Widget? leftWidget;
  final Widget? rightWidget;
  final Widget? bottomWidget;
  final String? icon;
  final Function()? onTap;

  const SimpleSettingItemWidget(this.title,
      {super.key,
      this.value,
      this.leftWidget,
      this.rightWidget,
      this.bottomWidget,
      this.icon,
      this.onTap});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 15, horizontal: 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                if (leftWidget != null)
                  Padding(
                      padding: EdgeInsets.only(right: 10), child: leftWidget),
                if (icon != null && icon!.isNotEmpty)
                  Padding(
                      padding: EdgeInsets.only(right: 10),
                      child: Image.asset(
                        icon!,
                        width: 40,
                        height: 40,
                        fit: BoxFit.contain,
                      )),
                (value?.isEmpty ?? true)
                    ? Expanded(
                        child: Text(
                        title,
                        style: TextStyles.fontSize16Normal,
                      ))
                    : Text(
                        title,
                        style: TextStyles.fontSize16Normal,
                      ),
                (value?.isNotEmpty ?? false)
                    ? Expanded(
                        child: Container(
                            padding: EdgeInsets.only(left: 10),
                            child: Text(
                              value!,
                              style: TextStyles.fontSize16Normal
                                  .copyWith(color: DColor.secondaryTextColor),
                              overflow: TextOverflow.ellipsis,
                              maxLines: 2,
                              textAlign: TextAlign.right, // 右对齐
                            )))
                    : Container(),
                if (rightWidget != null)
                  Padding(
                    padding: EdgeInsets.only(left: 10),
                    child: rightWidget!,
                  ),
                if (onTap != null)
                  Padding(
                    padding: EdgeInsets.only(left: 10),
                    child: Icon(
                      Icons.arrow_forward_ios,
                      size: 16,
                      color: DColor.secondaryTextColor,
                    ),
                  ),
              ],
            ),
            if (bottomWidget != null)
              Padding(
                padding: EdgeInsets.only(top: 10),
                child: bottomWidget,
              ),
          ],
        ),
      ),
    );
  }
}
