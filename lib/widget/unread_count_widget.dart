import 'package:and/common/res/colours.dart';
import 'package:and/common/res/text_styles.dart';
import 'package:and/utils/format_utils.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';

class UnreadCountWidget extends StatelessWidget {
  final int unreadCount;
  final Color? backgroundColor;

  const UnreadCountWidget(
    this.unreadCount, {
    super.key,
    this.backgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    if (unreadCount == 0) {
      return const SizedBox();
    }
    return Container(
        decoration: BoxDecoration(
          color: backgroundColor ?? Colors.red,
          borderRadius: BorderRadius.circular(10),
        ),
        padding: const EdgeInsets.symmetric(vertical: 0, horizontal: 5),
        child: Text(
          style: TextStyles.fontSize13Normal.copyWith(color: Colors.white),
          FormatUtils.formatUnreadCount(unreadCount),
          textAlign: TextAlign.right,
        ));
  }
}
