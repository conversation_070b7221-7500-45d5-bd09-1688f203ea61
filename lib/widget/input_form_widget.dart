import 'package:and/common/res/colours.dart';
import 'package:and/common/res/text_styles.dart';
import 'package:and/utils/image_path.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class InputSimpleForm extends StatelessWidget {
  final String title;
  final bool isNeed;
  final Widget child;
  final Widget? suffixChild;

  const InputSimpleForm({
    super.key,
    required this.title,
    required this.child,
    this.suffixChild,
    this.isNeed = true,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(children: [
            RichText(
              textScaler: MediaQuery.of(context).textScaler,
              text: TextSpan(style: TextStyles.fontSize16Normal, children: [
                TextSpan(
                    text: isNeed ? "*" : "",
                    style: TextStyles.fontSize16Normal
                        .copyWith(color: DColor.primaryColor)),
                TextSpan(
                  text: title,
                ),
              ]),
            ),
            suffixChild ?? const SizedBox()
          ]),
          const SizedBox(height: 8),
          child
        ],
      ),
    );
  }
}

class InputTextForm extends StatefulWidget {
  final String? hint;
  final String? value;
  final int minLines;
  final int? maxLines;
  final int? maxLength;
  final bool enabled;
  final bool readOnly;
  final bool obscureText;
  final bool? autofocus;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final Color? backgroundColor;
  final Color? borderColor;
  final double? borderRadius;
  final double? fontSize;
  final Color? textColor;
  final EdgeInsetsGeometry? contentPadding;
  final TextInputType? keyboardType;
  final TextAlign? textAlign;
  final TextEditingController controller;
  final ValueChanged<String>? onChanged;
  final List<TextInputFormatter>? inputFormatters;
  final GestureTapCallback? onTap;
  final Key? formKey;
  final FormFieldValidator<String>? validator;
  final TextInputAction? textInputAction;
  final ValueChanged<String>? onFieldSubmitted;
  final bool showClear;

  const InputTextForm(
      {super.key,
      this.hint,
      this.value,
      this.minLines = 1,
      this.maxLines,
      this.maxLength,
      this.enabled = true,
      this.obscureText = false,
      this.autofocus = true,
      this.readOnly = false,
      this.prefixIcon,
      this.suffixIcon,
      this.keyboardType,
      this.textAlign,
      this.onChanged,
      this.contentPadding,
      this.backgroundColor,
      this.borderColor,
      this.borderRadius,
      this.fontSize,
      this.textColor,
      this.inputFormatters,
      this.onTap,
      this.formKey,
      this.validator,
      this.textInputAction,
      this.onFieldSubmitted,
      this.showClear = true,
      required this.controller});

  @override
  State<InputTextForm> createState() => _InputTextFormState();
}

class _InputTextFormState extends State<InputTextForm>
    with WidgetsBindingObserver {
  final FocusNode _focusNode = FocusNode();
  bool _showPassword = false;
  bool _isKeyboardVisible = false;

  @override
  void initState() {
    super.initState();
    widget.controller.text = widget.value ?? "";
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeMetrics() {
    super.didChangeMetrics();
    final bottomInset = View.of(context).viewInsets.bottom;
    final isKeyboardVisible = bottomInset > 0.0;
    if (isKeyboardVisible != _isKeyboardVisible) {
      if (mounted) {
        setState(() {
          _isKeyboardVisible = isKeyboardVisible;
        });
      }

      if (!_isKeyboardVisible) {
        //键盘收起来的时候，清除 focus，避免返回页面的时候，键盘又重新弹出
        FocusManager.instance.primaryFocus?.unfocus();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Form(
        key: widget.formKey,
        child: TextFormField(
          onTap: widget.onTap,
          readOnly: widget.readOnly || widget.onTap != null,
          inputFormatters: widget.inputFormatters ?? [],
          controller: widget.controller,
          focusNode: _focusNode,
          enabled: widget.enabled,
          keyboardType: widget.keyboardType ?? TextInputType.text,
          cursorWidth: 2,
          obscureText: widget.obscureText && !_showPassword,
          cursorColor: DColor.primaryColor,
          cursorRadius: const Radius.circular(1),
          minLines: widget.minLines,
          maxLines: widget.obscureText ? 1 : widget.maxLines,
          maxLength: widget.maxLength,
          autofocus: widget.autofocus ?? true,
          validator: widget.validator,
          textAlign: widget.textAlign ?? TextAlign.start,
          autovalidateMode: AutovalidateMode.onUserInteraction,
          style: TextStyle(
              fontSize: widget.fontSize ?? 14,
              color: widget.textColor ??
                  (widget.readOnly
                      ? DColor.secondaryTextColor
                      : DColor.primaryTextColor)),
          textInputAction: widget.textInputAction,
          onFieldSubmitted: widget.onFieldSubmitted,
          decoration: InputDecoration(
              filled: true,
              fillColor: widget.backgroundColor ?? const Color(0xFFF7F7F7),
              focusedBorder: OutlineInputBorder(
                  borderRadius:
                      BorderRadius.circular(widget.borderRadius ?? 40),
                  borderSide: BorderSide(
                      color: widget.readOnly
                          ? DColor.borderColor
                          : (widget.borderColor ?? DColor.borderActiveColor),
                      width: 0.5)),
              focusedErrorBorder: OutlineInputBorder(
                  borderRadius:
                      BorderRadius.circular(widget.borderRadius ?? 40),
                  borderSide: BorderSide(
                      color: widget.readOnly
                          ? DColor.borderColor
                          : (widget.borderColor ?? DColor.borderActiveColor),
                      width: 0.5)),
              errorBorder: OutlineInputBorder(
                  borderRadius:
                      BorderRadius.circular(widget.borderRadius ?? 40),
                  borderSide: BorderSide(
                      color: widget.borderColor ?? DColor.borderActiveColor,
                      width: 0.5)),
              enabledBorder: OutlineInputBorder(
                  borderRadius:
                      BorderRadius.circular(widget.borderRadius ?? 40),
                  borderSide: BorderSide(
                      color: widget.borderColor ?? DColor.borderColor,
                      width: 0.5)),
              disabledBorder: OutlineInputBorder(
                  borderRadius:
                      BorderRadius.circular(widget.borderRadius ?? 40),
                  borderSide: BorderSide(
                      color: widget.borderColor ?? DColor.borderColor,
                      width: 0.5)),
              errorStyle:
                  const TextStyle(fontSize: 14, color: DColor.primaryColor),
              prefixIcon: widget.prefixIcon,
              prefixIconConstraints: const BoxConstraints(),
              suffixIcon: _buildSuffixIcon(),
              suffixIconConstraints: const BoxConstraints(),
              counterText: "",
              hintText: widget.hint,
              hintStyle: TextStyle(
                  fontSize: widget.fontSize ?? 14,
                  color: DColor.hintText.withOpacity(0.2)),
              isDense: true,
              contentPadding:
                  widget.contentPadding ?? const EdgeInsets.all(15)),
          onChanged: (text) {
            widget.onChanged?.call(text);
          },
          onTapOutside: (event) {
            //解决点击空白区域，键盘不收起的问题
            FocusScopeNode currentFocus = FocusScope.of(context);
            currentFocus.focusedChild?.unfocus();
          },
        ));
  }

  Widget _buildSuffixIcon() {
    var suffixIcon = widget.suffixIcon ??
        ((widget.onTap != null) ? _buildDropDownIcon() : null);
    return Padding(
      padding: const EdgeInsets.only(right: 10),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTap: () {
              if (mounted) {
                setState(() {
                  widget.controller.clear();
                  //手动触发 onchange
                  widget.onChanged?.call("");
                });
              }
            },
            child: Visibility(
                visible: widget.controller.text.isNotEmpty &&
                    (widget.showClear) &&
                    !(widget.readOnly),
                child: const Padding(
                    padding: EdgeInsets.symmetric(horizontal: 5),
                    child: Icon(Icons.clear))),
          ),
          Visibility(
              visible: (suffixIcon != null),
              child: (suffixIcon != null)
                  ? Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 5),
                      child: suffixIcon)
                  : const SizedBox()),
          GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTap: () {
              setState(() {
                _showPassword = !_showPassword;
              });
            },
            child: Visibility(
                visible:
                    widget.controller.text.isNotEmpty && widget.obscureText,
                child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 5),
                    child: Icon(_showPassword
                        ? Icons.visibility_off
                        : Icons.visibility))),
          ),
        ],
      ),
    );
  }

  Widget _buildDropDownIcon() {
    return Image.asset(
      ImagePath.ic_arrow_drop_down,
      width: 14,
      fit: BoxFit.fitWidth,
    );
  }
}

class IdSelectedForm extends StatelessWidget {
  final String hint;
  final String? content;
  final Widget? suffixIcon;
  final Function onPressed;

  const IdSelectedForm(
      {super.key,
      this.suffixIcon,
      required this.hint,
      required this.content,
      required this.onPressed});

  @override
  Widget build(BuildContext context) {
    bool isContentNotEmpty = content?.isNotEmpty ?? false;
    return GestureDetector(
        onTap: () {
          onPressed();
        },
        child: Container(
          padding: const EdgeInsets.only(left: 10),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Expanded(
                  child: Text(
                isContentNotEmpty ? content ?? "" : hint,
                style: TextStyle(
                    color: isContentNotEmpty
                        ? DColor.primaryTextColor
                        : DColor.hintText,
                    fontSize: 15),
              )),
              const SizedBox(
                width: 2,
              ),
              suffixIcon ??
                  const Icon(
                    size: 14,
                    Icons.arrow_drop_down,
                    color: Colors.black,
                  )
            ],
          ),
        ));
  }
}
