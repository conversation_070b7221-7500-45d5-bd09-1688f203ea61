import 'package:custom_text/custom_text.dart';
import 'package:flutter/material.dart';

class HighlightedText extends StatelessWidget {
  final String text;
  final String? keyword;
  final Color highlightColor;
  final TextStyle? textStyle;
  final TextStyle? highlightStyle;
  final int? maxLine;

  const HighlightedText({
    super.key,
    required this.text,
    this.keyword,
    this.highlightColor = Colors.blue,
    this.maxLine,
    this.textStyle,
    this.highlightStyle,
  });

  @override
  Widget build(BuildContext context) {
    return CustomText(
      style: textStyle,
      parserOptions: ParserOptions(
       caseSensitive: false,
      ),
      textScaler: MediaQuery.of(context).textScaler,
      text,
      definitions: [
        TextDefinition(
          matcher:
              PatternMatcher(keyword??''),
          matchStyle: highlightStyle ??
              TextStyle(
                color: highlightColor,
                fontWeight: FontWeight.bold,
              ),
        ),
      ],
    );
  }
}
