import 'package:and/common/res/colours.dart';
import 'package:flutter/material.dart';

class ReminderAnimationWidget extends StatefulWidget {
  final int count; // 闪烁次数
  final Color? highlightColor; // 高亮颜色
  final Duration duration; // 动画持续时间
  final double? height; // 容器高度
  final Widget? child; // 子组件

  const ReminderAnimationWidget({
    super.key,
    required this.count,
    this.highlightColor,
    this.duration = const Duration(milliseconds: 800),
    this.height,
    this.child,
  });

  @override
  State<ReminderAnimationWidget> createState() =>
      _ReminderAnimationWidgetState();
}

class _ReminderAnimationWidgetState extends State<ReminderAnimationWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<Color?> _colorAnimation;
  late Color _highlightColor;
  int _currentCount = 0; // 记录当前动画执行次数

  @override
  void initState() {
    super.initState();

    // 初始化颜色 - 使用更淡的高亮颜色
    _highlightColor =
        widget.highlightColor ?? DColor.primaryColor.withOpacity(0.3);

    // 初始化动画控制器
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    );

    _colorAnimation = ColorTween(
      begin: Colors.transparent,
      end: _highlightColor,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));

    _controller.addStatusListener(_handleAnimationStatus);

    // 开始动画
    _controller.forward();
  }

  // 处理动画状态变化
  void _handleAnimationStatus(AnimationStatus status) {
    if (status == AnimationStatus.completed) {
      if (_currentCount < widget.count - 1) {
        _controller.reverse(); // 反向动画
        _currentCount++;
      } else {
        // 最后一次动画完成后，恢复到原始颜色
        Future.delayed(const Duration(milliseconds: 100), () {
          if (mounted) {
            _controller.reverse();
            // 确保动画完全结束后不再继续
            _currentCount = widget.count;
          }
        });
      }
    } else if (status == AnimationStatus.dismissed) {
      if (_currentCount < widget.count) {
        _controller.forward();
      }
    }
  }

  @override
  void didUpdateWidget(ReminderAnimationWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    // 如果关键属性发生变化，更新动画
    if (oldWidget.count != widget.count ||
        oldWidget.highlightColor != widget.highlightColor ||
        oldWidget.duration != widget.duration) {
      // 更新颜色 - 使用更淡的高亮颜色
      _highlightColor =
          widget.highlightColor ?? DColor.primaryColor.withOpacity(0.3);

      _currentCount = 0;
      _colorAnimation = ColorTween(
        begin: Colors.transparent,
        end: _highlightColor,
      ).animate(CurvedAnimation(
        parent: _controller,
        curve: Curves.easeInOut,
      ));

      // 重新开始动画
      if (_controller.status == AnimationStatus.dismissed) {
        _controller.forward();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _colorAnimation,
      builder: (context, child) {
        return Container(
          height: widget.height,
          color: _colorAnimation.value,
          child: widget.child,
        );
      },
    );
  }

  @override
  void dispose() {
    _controller.removeStatusListener(_handleAnimationStatus);
    _controller.dispose();
    super.dispose();
  }
}
