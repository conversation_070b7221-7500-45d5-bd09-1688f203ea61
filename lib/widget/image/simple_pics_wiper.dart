import 'dart:io';

import 'package:and/common/res/text_styles.dart';
import 'package:and/module/file/widget/file_menu_widget.dart';
import 'package:and/utils/image_path.dart';
import 'package:and/utils/transparent_page_route.dart';
import 'package:extended_image/extended_image.dart';
import 'package:flutter/material.dart';

import 'hero.dart';

void previewImage(BuildContext context, String imageUrl,
    {bool isShowAsDialog = false, Object? tag, bool enableHero = true}) {
  previewImages(context, imageUrl, [imageUrl],
      isShowAsDialog: isShowAsDialog, tag: tag, enableHero: enableHero);
}

void previewImages(BuildContext context, String imageUrl, List<String> images,
    {bool isShowAsDialog = false,
    ValueChanged<int>? onPageChanged,
    Object? tag,
    bool enableHero = true}) async {
  await openTransparent(context, builder: (context) {
    return SimplePicsWiper(
      url: imageUrl,
      tag: tag,
      images: images,
      onPageChanged: onPageChanged,
      backgroundHandler: isShowAsDialog
          ? (Offset offset, Size size) {
              return Colors.grey.withOpacity(0.5);
            }
          : null,
    );
  });
}

class SimplePicsWiper extends StatefulWidget {
  final String url;
  final List<String> images;
  final Object? tag;
  final SlidePageBackgroundHandler? backgroundHandler;
  final ValueChanged<int>? onPageChanged;

  const SimplePicsWiper(
      {super.key,
      required this.url,
      required this.images,
      this.backgroundHandler,
      this.onPageChanged,
      this.tag});

  @override
  State createState() => _SimplePicsWiperState();
}

class _SimplePicsWiperState extends State<SimplePicsWiper> {
  GlobalKey<ExtendedImageSlidePageState> slidePagekey =
      GlobalKey<ExtendedImageSlidePageState>();

  final List<int> _cachedIndexes = <int>[];

  int _current = 0;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final int index = widget.images.indexOf(widget.url);
    _current = index;
    _preloadImage(index - 1);
    _preloadImage(index + 1);
  }

  void _preloadImage(int index) {
    if (_cachedIndexes.contains(index)) {
      return;
    }
    if (0 <= index && index < widget.images.length) {
      final String url = widget.images[index];
      if (url.startsWith('https:')) {
        precacheImage(ExtendedNetworkImageProvider(url, cache: true), context);
      }

      _cachedIndexes.add(index);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          automaticallyImplyLeading: false,
          leading: CloseButton(),
          actions: [_buildAction()],
        ),
        backgroundColor: Colors.transparent,
        body: SafeArea(
            child: Stack(children: [
          Positioned.fill(child: _buildContent()),
          Positioned(right: 15, bottom: 15, child: _buildPageNumberIndicator()),
        ])));
  }

  Widget _buildAction() {
    var url = widget.images[_current];  
    return Padding(
      padding: EdgeInsets.only(right: 10),
      child: FileMenuWidget(uri: Uri.parse(url)),
    );
  }

  Widget _buildContent() {
    if (widget.images.length == 1) {
      return _buildSinglePage();
    }
    return _buildSlidePage();
  }

  Widget _buildSinglePage() {
    return GestureDetector(
        child: ExtendedImageSlidePage(
          key: slidePagekey,
          slidePageBackgroundHandler: widget.backgroundHandler,
          slideAxis: SlideAxis.both,
          slideType: SlideType.wholePage,
          child: _buildImageItem(widget.url, inPageView: false),
        ),
        onTap: () {
          Navigator.pop(context);
        });
  }

  Widget _buildSlidePage() {
    return ExtendedImageSlidePage(
      key: slidePagekey,
      slidePageBackgroundHandler: widget.backgroundHandler,
      slideAxis: SlideAxis.both,
      slideType: SlideType.wholePage,
      child: GestureDetector(
        child: ExtendedImageGesturePageView.builder(
          controller: ExtendedPageController(
            initialPage: widget.images.indexOf(widget.url),
            pageSpacing: 50,
            shouldIgnorePointerWhenScrolling: false,
          ),
          itemCount: widget.images.length,
          onPageChanged: (int page) {
            _preloadImage(page - 1);
            _preloadImage(page + 1);
            setState(() {
              _current = page;
            });
            widget.onPageChanged?.call(page);
          },
          itemBuilder: (BuildContext context, int index) {
            final String url = widget.images[index];

            return _buildImageItem(url);
          },
        ),
        onTap: () {
          Navigator.pop(context);
        },
      ),
    );
  }

  Widget _buildImageItem(String url, {bool inPageView = true}) {
    Object tag;
    if (widget.url == url) {
      tag = widget.tag ?? url;
    } else {
      tag = url;
    }
    return HeroWidget(
      tag: tag,
      slideType: SlideType.wholePage,
      slidePagekey: slidePagekey,
      child: _buildImage(url, inPageView: inPageView),
    );
  }

  Widget _buildPageNumberIndicator() {
    return Hero(
        tag: "page_number",
        child: Container(
          padding: const EdgeInsets.only(left: 8, right: 8, top: 2, bottom: 2),
          decoration: BoxDecoration(
              color: Colors.white,
              border: Border.all(color: const Color(0xFFCDCDCFD), width: 1),
              borderRadius: BorderRadius.circular(4)),
          child: Text("${_current + 1} / ${(widget.images.length ?? 0)}",
              style: TextStyles.fontSize13Normal),
        ));
  }

  Widget _buildImage(String url, {bool inPageView = true}) {
    if (url.startsWith("http")) {
      return ExtendedImage.network(
        url,
        enableSlideOutPage: true,
        fit: BoxFit.contain,
        mode: ExtendedImageMode.gesture,
        initGestureConfigHandler: (ExtendedImageState state) {
          return GestureConfig(
            //you must set inPageView true if you want to use ExtendedImageGesturePageView
            inPageView: inPageView,
            initialScale: 1.0,
            maxScale: 5.0,
            animationMaxScale: 6.0,
            initialAlignment: InitialAlignment.center,
          );
        },
      );
    }

    if (!File(url).existsSync()) {
      return Container(
        width: 100,
        height: 100,
        decoration: BoxDecoration(
          color: Colors.grey[200],
          borderRadius: BorderRadius.circular(8),
        ),
      );
    }
    return ExtendedImage.file(
      File(url),
      enableSlideOutPage: true,
      fit: BoxFit.contain,
      mode: ExtendedImageMode.gesture,
      initGestureConfigHandler: (ExtendedImageState state) {
        return GestureConfig(
          //you must set inPageView true if you want to use ExtendedImageGesturePageView
          inPageView: inPageView,
          initialScale: 1.0,
          maxScale: 5.0,
          animationMaxScale: 6.0,
          initialAlignment: InitialAlignment.center,
        );
      },
    );
  }
}
