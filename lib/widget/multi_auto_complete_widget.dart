import 'package:flutter/material.dart';

class MultiAutoCompleteView extends StatefulWidget {
  final List<String> suggestions;

  const MultiAutoCompleteView({super.key, required this.suggestions});

  @override
  State<MultiAutoCompleteView> createState() => _MultiAutoCompleteViewState();
}

class _MultiAutoCompleteViewState extends State<MultiAutoCompleteView> {
  final TextEditingController _controller = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  final List<String> _selectedItems = [];

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // 已选中的标签展示
        Wrap(
          spacing: 4.0,
          children: _selectedItems.map((item) => Chip(
            label: Text(item),
            onDeleted: () => _removeItem(item),
          )).toList(),
        ),
      ],
    );
  }

  void _addItem(String item) {
    if (item.isNotEmpty && !_selectedItems.contains(item)) {
      if (mounted) {
        setState(() => _selectedItems.add(item));
      }
      _controller.clear(); // 清空输入框
    }
  }

  void _removeItem(String item) {
    if (mounted) {
      setState(() => _selectedItems.remove(item));
    }
  }
}