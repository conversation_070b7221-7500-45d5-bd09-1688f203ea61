import 'dart:async';

import 'package:and/common/res/colours.dart';
import 'package:and/common/res/text_styles.dart';
import 'package:flutter/material.dart';

Widget SubmitRoundedButton({
  required String text,
  required GestureTapCallback onPressed,
  final EdgeInsetsGeometry? padding,
  final MainAxisSize? mainAxisSize,
  final double? minWidth,
  final double? minHeight,
  final String? tag,
  bool enable = true,
  final Widget? rightChild,
  final Widget? bottomChild,
}) {
  return SubmitButton(
    enable: enable,
    onPressed: onPressed,
    text: text,
    elevation: 0,
    backgroundColor: Colors.white,
    textColor: DColor.primaryColor,
    borderColor: DColor.primaryColor,
    padding: padding,
    mainAxisSize: mainAxisSize,
    minWidth: minWidth,
    minHeight: minHeight,
    tag: tag,
    rightChild: rightChild,
    bottomChild: bottomChild,
  );
}

Widget SubmitPrimaryButton({
  required String text,
  required GestureTapCallback onPressed,
  final EdgeInsetsGeometry? padding,
  final MainAxisSize? mainAxisSize,
  final double? minWidth,
  final double? minHeight,
  bool enable = true,
  final double? textSize,
  Color startColor = DColor.submitPrimaryColor,
  Color endColor = DColor.submitSecondaryColor,
  final String? tag,
  final Widget? rightChild,
  final Widget? bottomChild,
}) {
  return SubmitButton(
    enable: enable,
    onPressed: onPressed,
    text: text,
    backgroundColor: startColor,
    secondaryColor: endColor,
    padding: padding,
    minWidth: minWidth,
    minHeight: minHeight,
    mainAxisSize: mainAxisSize,
    textSize: textSize ?? 14,
    tag: tag,
    rightChild: rightChild,
    bottomChild: bottomChild,
  );
}

class SubmitButton extends StatefulWidget {
  final bool enable;
  final String text;
  final Color textColor;
  final double textSize;
  final Color backgroundColor;
  final Color? secondaryColor;
  final Color borderColor;
  final double? borderWidth;
  final double borderRadius;
  final double? minWidth;
  final double? minHeight;
  final VoidCallback? onPressed;
  final EdgeInsetsGeometry? padding;
  final MainAxisSize? mainAxisSize;
  final double? elevation;
  final String? tag;
  final Widget? rightChild;
  final Widget? bottomChild;
  /// 防抖时间间隔，默认为1000毫秒
  final Duration debounceTime;

  const SubmitButton({super.key,
    required this.onPressed,
    required this.text,
    this.textColor = Colors.white,
    this.textSize = 14,
    this.rightChild,
    this.backgroundColor = DColor.submitPrimaryColor,
    this.secondaryColor,
    this.borderColor = Colors.transparent,
    this.borderRadius = 40,
    this.borderWidth,
    this.padding,
    this.minWidth,
    this.minHeight,
    this.mainAxisSize,
    this.elevation,
    this.enable = true,
    this.tag,
    this.bottomChild,
    this.debounceTime = const Duration(milliseconds: 500)});

  @override
  State<SubmitButton> createState() => _SubmitButtonState();
}

class _SubmitButtonState extends State<SubmitButton> {
  Timer? _debounceTimer;
  bool _isProcessing = false;

  @override
  void dispose() {
    _debounceTimer?.cancel();
    super.dispose();
  }

  void _handlePressed() {
    if (_isProcessing) return;

    if (mounted) {
      setState(() {
        _isProcessing = true;
      });
    }

    widget.onPressed?.call();

    _debounceTimer?.cancel();
    _debounceTimer = Timer(widget.debounceTime, () {
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    var isEnabled = widget.enable && widget.onPressed != null && !_isProcessing;
    return Container(
        constraints:
        BoxConstraints(minWidth: widget.minWidth ?? 80, minHeight: widget.minHeight ?? 0),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              widget.backgroundColor.withOpacity(isEnabled ? 1 : 0.5),
              (widget.secondaryColor ?? widget.backgroundColor)
                  .withOpacity(isEnabled ? 1 : 0.5)
            ],
            // 渐变色数组
            begin: Alignment.centerLeft,
            end: Alignment.centerRight,
          ),
          borderRadius: BorderRadius.circular(widget.borderRadius), // 圆角
        ),
        child: MaterialButton(
          shape: RoundedRectangleBorder(
            //边框颜色
            side: BorderSide(
              color: widget.borderColor,
              width: widget.borderWidth ?? 1,
            ),
            borderRadius: BorderRadius.circular(widget.borderRadius),
          ),
          padding: widget.padding ??
              const EdgeInsets.symmetric(vertical: 10, horizontal: 15),
          elevation: widget.elevation ?? 0,
          onPressed: widget.enable && !_isProcessing
              ? _handlePressed
              : null,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                mainAxisSize: widget.mainAxisSize ?? MainAxisSize.max,
                children: [
                  Text(
                    widget.text,
                    style: TextStyles.fontSize15Normal.copyWith(
                      color: widget.textColor,
                      fontSize: widget.textSize,
                    ),
                  ),
                  widget.rightChild ?? const SizedBox()
                ],
              ),
              widget.bottomChild ?? const SizedBox()
            ],
          ),
        ));
  }
}

class BoxShadowContent extends StatelessWidget {
  final EdgeInsetsGeometry? padding;
  final double? elevation;
  final Widget child;

  const BoxShadowContent(
      {super.key, required this.child, this.elevation, this.padding});

  @override
  Widget build(BuildContext context) {
    var blurRadius = elevation ?? 4.0;
    return Container(
      width: MediaQuery
          .of(context)
          .size
          .width,
      padding: padding ??
          const EdgeInsets.only(left: 40, right: 40, top: 15, bottom: 15),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: blurRadius > 0
                ? Colors.grey.withOpacity(0.2)
                : Colors.transparent,
            // Adjust opacity for desired shadow intensity
            spreadRadius: 0,
            // Adjust spreadRadius for blur effect
            blurRadius: blurRadius,
            // Adjust blurRadius for shadow softness
            offset: const Offset(0, -2), // Offset the shadow slightly upwards
          ),
        ],
      ),
      child: child,
    );
  }
}
