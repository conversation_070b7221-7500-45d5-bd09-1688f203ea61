import 'package:and/common/res/text_styles.dart';
import 'package:flutter/material.dart';

///类似于Android中的PopupWindow
class PopupWindow extends StatefulWidget {
  static void showPopWindow(context, GlobalKey popKey,
      {PopDirection popDirection = PopDirection.bottom,
      required Widget popWidget,
      double offset = 0}) {
    Navigator.push(
        context,
        CustomTransitionRoute(
            child: PopupWindow(
          popKey: popKey,
          popDirection: popDirection,
          popWidget: popWidget,
          offset: offset,
        )));
  }

  final GlobalKey popKey;
  final PopDirection popDirection;
  final Widget popWidget; //自定义widget
  final double offset; //popupWindow偏移量

  const PopupWindow(
      {super.key,
      required this.popWidget,
      required this.popKey,
      this.popDirection = PopDirection.bottom,
      required this.offset});

  @override
  State<StatefulWidget> createState() {
    return _PopupWindowState();
  }
}

class _PopupWindowState extends State<PopupWindow> {
  GlobalKey buttonKey = GlobalKey();
  double left = 0;
  double top = 0;
  bool isPositioned = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      RenderBox renderBox =
          widget.popKey.currentContext?.findRenderObject() as RenderBox;
      Offset localToGlobal =
          renderBox.localToGlobal(Offset.zero); //targetWidget的坐标位置
      Size size = renderBox.size; //targetWidget的size

      RenderBox buttonBox =
          buttonKey.currentContext?.findRenderObject() as RenderBox;
      var buttonSize = buttonBox.size; //button的size
      switch (widget.popDirection) {
        case PopDirection.left:
          left = localToGlobal.dx - buttonSize.width - widget.offset;
          top = localToGlobal.dy + size.height / 2 - buttonSize.height / 2;
          break;
        case PopDirection.top:
          left = localToGlobal.dx + size.width / 2 - buttonSize.width / 2;
          top = localToGlobal.dy - buttonSize.height - widget.offset;
          fixPosition(buttonSize);
          break;
        case PopDirection.right:
          left = localToGlobal.dx + size.width + widget.offset;
          top = localToGlobal.dy + size.height / 2 - buttonSize.height / 2;
          break;
        case PopDirection.bottom:
          left = localToGlobal.dx + size.width / 2 - buttonSize.width / 2;
          top = localToGlobal.dy + size.height + widget.offset;
          fixPosition(buttonSize);
          break;
      }
      setState(() {
        isPositioned = true;
      });
    });
  }

  void fixPosition(Size buttonSize) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;

    // 处理左右边界
    if (left < 0) {
      left = 0;
    }
    if (left + buttonSize.width > screenWidth) {
      left = screenWidth - buttonSize.width;
    }

    // 处理上下边界
    if (top < 0) {
      top = 0;
    }
    double keyboardHeight = MediaQuery.of(context).viewInsets.bottom;
    double remainHeight = screenHeight - keyboardHeight;

    if (top + buttonSize.height > remainHeight) {
      top = remainHeight - buttonSize.height;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: GestureDetector(
        child: Stack(
          children: <Widget>[
            Container(
              width: MediaQuery.of(context).size.width,
              height: MediaQuery.of(context).size.height,
              color: Colors.transparent,
            ),
            Positioned(
              left: left,
              top: top,
              child: Opacity(
                  opacity: isPositioned ? 1.0 : 0.0,
                  child: _buildCustomWidget(widget.popWidget)),
            )
          ],
        ),
        onTap: () {
          Navigator.pop(context);
        },
      ),
    );
  }

  Widget _buildCustomWidget(Widget child) => Container(
        key: buttonKey,
        child: SafeArea(top: false, child: child),
      );
}

//popwindow的方向
enum PopDirection { left, top, right, bottom }

// 自定义 TransitionRoute
class CustomTransitionRoute<T> extends TransitionRoute<T> {
  final Widget child;
  final Duration _duration = Duration(milliseconds: 200);

  CustomTransitionRoute({required this.child});

  @override
  Duration get transitionDuration => _duration;

  @override
  Iterable<OverlayEntry> createOverlayEntries() {
    // 创建一个 OverlayEntry 并返回
    final overlayEntry = OverlayEntry(
      builder: (context) {
        return child;
      },
    );
    return [overlayEntry];
  }

  @override
  bool get opaque => false;

  @override
  bool get popGestureEnabled => true;
}
