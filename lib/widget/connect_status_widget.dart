import 'dart:async';

import 'package:and/common/res/text_styles.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/widget/submit_button.dart';
import 'package:flutter/material.dart';
import 'package:wukongimfluttersdk/type/const.dart';
import 'package:wukongimfluttersdk/wkim.dart';

class ConnectStatusWidget extends StatefulWidget {
  final int? status;

  const ConnectStatusWidget(this.status, {super.key});

  @override
  State<ConnectStatusWidget> createState() => _ConnectStatusWidgetState();
}

class _ConnectStatusWidgetState extends State<ConnectStatusWidget> {
  Timer? _debounceTimer;
  int? _displayedStatus;

  @override
  void initState() {
    super.initState();
    _displayedStatus = widget.status;
  }

  @override
  void didUpdateWidget(ConnectStatusWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.status != oldWidget.status) {
      if (widget.status == WKConnectStatus.fail ||
          widget.status == WKConnectStatus.noNetwork) {
        var delayTime = 2;
        _debounceTimer?.cancel();
        _debounceTimer = Timer(Duration(seconds: delayTime), () {
          if (mounted) {
            setState(() {
              _displayedStatus = widget.status;
            });
          }
        });
      } else {
        setState(() {
          _displayedStatus = widget.status;
        });
      }
    }
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    var isConnectFail = _displayedStatus == WKConnectStatus.fail ||
        _displayedStatus == WKConnectStatus.noNetwork;
    if (!isConnectFail) {
      return Container();
    }
    var text = "";
    if (_displayedStatus == WKConnectStatus.fail) {
      text = context.l10n.connectFailed;
    } else if (_displayedStatus == WKConnectStatus.noNetwork) {
      text = context.l10n.connectNoNetwork;
    }
    return Container(
      color: Colors.red,
      padding: const EdgeInsets.all(10),
      child: Row(
        children: [
          Text(text,
              style: TextStyles.fontSize15Normal.copyWith(color: Colors.white)),
          if (_displayedStatus == WKConnectStatus.fail)
            Container(
                margin: const EdgeInsets.only(left: 10),
                constraints: BoxConstraints(minWidth: 80),
                child: SubmitButton(
                    padding: EdgeInsets.symmetric(horizontal: 4, vertical: 4),
                    onPressed: () {
                      WKIM.shared.connectionManager.connect();
                    },
                    text: context.l10n.reconnect))
        ],
      ),
    );
  }
}
