import 'dart:convert';

import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

class Cache {
  static final Cache _instance = Cache._();

  static Cache get instance => _instance;

  Cache._();

  late SharedPreferences _prefs;

  late FlutterSecureStorage _storage;

  Future<void> init() async {
    _prefs = await SharedPreferences.getInstance();
    _storage = const FlutterSecureStorage(
      aOptions: AndroidOptions(
        encryptedSharedPreferences: true,
      ),
    );
  }

  Future<void> removeEncrypt(String key) => _storage.delete(key: key);

  Future<void> putEncrypt(String key, String value) async =>
      await _storage.write(key: key, value: value);

  Future<String?> getEncrypt(String key) => _storage.read(key: key);

  Future<void> putEncryptObject(String key, Object? value) async =>
      await _storage.write(
          key: key, value: value == null ? "{}" : json.encode(value));

  Future<T?> getEncryptObject<T>(
      String key, T Function(Map<String, dynamic> v) f,
      {T? defValue}) async {
    String? data = await getEncrypt(key);
    var map = (data == null || data.isEmpty) ? null : (json.decode(data));
    return map == null ? defValue : f(map);
  }

  /// put object.
  Future<bool>? putObject(String key, Object? value) {
    return _prefs.setString(key, value == null ? "{}" : json.encode(value));
  }

  /// get obj.
  T? getObject<T>(String key, T Function(Map<String, dynamic> v) f,
      {T? defValue}) {
    String? data = _prefs.getString(key);
    var map = (data == null || data.isEmpty) ? null : json.decode(data);
    return map == null ? defValue : f(map);
  }

  /// get string.
  String getString(String key, {String defValue = ''}) {
    return _prefs.getString(key) ?? defValue;
  }

  /// put string.
  Future<bool>? putString(String key, String value) {
    return _prefs.setString(key, value);
  }

  /// get bool.
  bool getBool(String key, {bool defValue = false}) {
    return _prefs.getBool(key) ?? defValue;
  }

  /// put bool.
  Future<bool>? putBool(String key, bool value) {
    return _prefs.setBool(key, value);
  }

  /// get int.
  int getInt(String key, {int defValue = 0}) {
    return _prefs.getInt(key) ?? defValue;
  }

  /// put int.
  Future<bool>? putInt(String key, int value) {
    return _prefs.setInt(key, value);
  }

  /// get double.
  double getDouble(String key, {double defValue = 0.0}) {
    return _prefs.getDouble(key) ?? defValue;
  }

  /// put double.
  Future<bool>? putDouble(String key, double value) {
    return _prefs.setDouble(key, value);
  }

  /// get string list.
  List<String> getStringList(String key, {List<String> defValue = const []}) {
    return _prefs.getStringList(key) ?? defValue;
  }

  /// put string list.
  Future<bool>? putStringList(String key, List<String> value) {
    return _prefs.setStringList(key, value);
  }

  /// get dynamic.
  dynamic getDynamic(String key, {Object? defValue}) {
    return _prefs.get(key) ?? defValue;
  }

  /// have key.
  bool haveKey(String key) {
    return _prefs.getKeys().contains(key);
  }

  /// get keys.
  Set<String>? getKeys() {
    return _prefs.getKeys();
  }

  /// remove.
  Future<bool>? remove(String key) {
    return _prefs.remove(key);
  }

  /// clear.
  Future<bool>? clear() {
    return _prefs.clear();
  }
}
