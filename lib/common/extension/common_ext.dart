import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:path/path.dart';

extension numExtension on num {
  String getFormatText({int fractionDigits = 2}) {
    if (this == 0) {
      return '--';
    }

    return toStringAsFixed(fractionDigits);
  }
}

extension GetExtension on GetInterface {
  T findOrPut<T>(T Function() factory, {String? tag}) {
    try {
      final instance = find<T>(tag: tag);
      return instance;
    } catch (error) {
      if (kDebugMode) {
        print("Error finding dependency: $error");
      }
      return put<T>(factory(), tag: tag);
    }
  }
}

extension ListExtensions<T> on List<T> {
  T? firstItemWhereOrNull(bool Function(T element) test) {
    var index = indexWhere((element) => test(element));
    if (index != -1) {
      return this[index];
    }
    return null;
  }

  T? lastItemWhereOrNull(bool Function(T element) test) {
    var index = lastIndexWhere((element) => test(element));
    if (index != -1) {
      return this[index];
    }
    return null;
  }

  List<List<T>> splitList(int size) {
    List<List<T>> result = [];
    for (int i = 0; i < length; i += size) {
      result.add(sublist(i, i + size > length ? length : i + size));
    }
    return result;
  }
}

extension FileExtension on File {
  String get name {
    return basename(path);
  }

  String get ext {
    return extension(path);
  }
}


extension UriExtensions on Uri {
  /// True if Uri scheme is content
  bool get isHttpUri => scheme == 'http' || scheme == 'https';
}
