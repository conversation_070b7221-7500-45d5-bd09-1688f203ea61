import 'package:flutter/material.dart';

class Dimens {
  static const double fontSp10 = 10.0;
  static const double fontSp12 = 12.0;

  static const Radius radius2 = Radius.circular(2);
  static const Radius radius3 = Radius.circular(3);
  static const Radius radius4 = Radius.circular(4);
  static const Radius radius5 = Radius.circular(5);
  static const Radius radius6 = Radius.circular(6);
  static const Radius radius7 = Radius.circular(7);
  static const Radius radius8 = Radius.circular(8);
  static const Radius radius9 = Radius.circular(9);
  static const Radius radius10 = Radius.circular(10);
  static const Radius radius12 = Radius.circular(12);
  static const Radius radius13 = Radius.circular(13);
  static const Radius radius14 = Radius.circular(14);
  static const Radius radius15 = Radius.circular(15);
  static const Radius radius16 = Radius.circular(16);
  static const Radius radius17 = Radius.circular(17);
  static const Radius radius19 = Radius.circular(19);
  static const Radius radius20 = Radius.circular(20);
  static const Radius radius22 = Radius.circular(22);
  static const Radius radius24 = Radius.circular(24);

  static const Radius radius25 = Radius.circular(25);
  static const Radius radius27 = Radius.circular(27);
  static const Radius radius30 = Radius.circular(30);
  static const Radius radius32 = Radius.circular(32);
  static const Radius radius42 = Radius.circular(42);
  static const Radius radius44 = Radius.circular(44);
  static const Radius radius50 = Radius.circular(50);

  static const BorderRadius topBorderRadius8 =
      BorderRadius.only(topLeft: radius8, topRight: radius8);
  static const BorderRadius topBorderRadius10 =
      BorderRadius.only(topLeft: radius10, topRight: radius10);
  static const BorderRadius topBorderRadius12 =
      BorderRadius.only(topLeft: radius12, topRight: radius12);

  static const BorderRadius topBorderRadius24 =
      BorderRadius.only(topLeft: radius24, topRight: radius24);
  static const BorderRadius topBorderRadius50 =
      BorderRadius.only(topLeft: radius50, topRight: radius50);
  static const BorderRadius leftBorderRadius4 =
      BorderRadius.only(topLeft: radius4, bottomLeft: radius4);
  static const BorderRadius leftBorderRadius8 =
      BorderRadius.only(topLeft: radius8, bottomLeft: radius8);
  static const BorderRadius leftBorderRadius12 =
      BorderRadius.only(topLeft: radius12, bottomLeft: radius12);
  static const BorderRadius rightBorderRadius4 =
      BorderRadius.only(topRight: radius4, bottomRight: radius4);
  static const BorderRadius rightBorderRadius8 =
      BorderRadius.only(topRight: radius8, bottomRight: radius8);
  static const BorderRadius rightBorderRadius12 =
      BorderRadius.only(topRight: radius12, bottomRight: radius12);

  static const BorderRadius borderRadius2 = BorderRadius.all(radius2);
  static const BorderRadius borderRadius3 = BorderRadius.all(radius3);
  static const BorderRadius borderRadius4 = BorderRadius.all(radius4);
  static const BorderRadius borderRadius5 = BorderRadius.all(radius5);
  static const BorderRadius borderRadius6 = BorderRadius.all(radius6);
  static const BorderRadius borderRadius7 = BorderRadius.all(radius7);
  static const BorderRadius borderRadius8 = BorderRadius.all(radius8);
  static const BorderRadius borderRadius9 = BorderRadius.all(radius9);
  static const BorderRadius borderRadius10 = BorderRadius.all(radius10);
  static const BorderRadius borderRadius12 = BorderRadius.all(radius12);
  static const BorderRadius borderRadius13 = BorderRadius.all(radius13);
  static const BorderRadius borderRadius14 = BorderRadius.all(radius14);
  static const BorderRadius borderRadius15 = BorderRadius.all(radius15);
  static const BorderRadius borderRadius16 = BorderRadius.all(radius16);
  static const BorderRadius borderRadius17 = BorderRadius.all(radius17);
  static const BorderRadius borderRadius19 = BorderRadius.all(radius19);
  static const BorderRadius borderRadius20 = BorderRadius.all(radius20);
  static const BorderRadius borderRadius22 = BorderRadius.all(radius22);
  static const BorderRadius borderRadius24 = BorderRadius.all(radius24);

  static const BorderRadius borderRadius25 = BorderRadius.all(radius25);
  static const BorderRadius borderRadius27 = BorderRadius.all(radius27);
  static const BorderRadius borderRadius30 = BorderRadius.all(radius30);
  static const BorderRadius borderRadius32 = BorderRadius.all(radius32);
  static const BorderRadius borderRadius42 = BorderRadius.all(radius42);
  static const BorderRadius borderRadius44 = BorderRadius.all(radius44);

  static const BorderRadius borderRadius50 = BorderRadius.all(radius50);

  static const double listItemHeight = 54;
  static const double listPadding = 22;
  static const double horizontalMargin = 18;

  static const double bottomBarHeight = 46;

  static const double defaultTabHeight = 46;
  static const double stepTopPadding = 28;
}
