import 'package:flutter/material.dart';

abstract class DColor {
  static const Color primaryColor = Color(0xFFFCCB5C);
  static const Color secondaryColor = Color(0xFFE5FD3C);
  static const Color primaryTextColor = Color(0xFF333333);
  static const Color primarySubTextColor = grey4D;

  static const Color secondaryTextColor = Color(0xFF666666);

  static const Color borderColor = Color(0xFFCACACA);
  static const Color borderActiveColor = primaryColor;

  static const Color submitPrimaryColor = Color(0xFFF67C3D);
  static const Color submitSecondaryColor = Color(0xFFFF9C69);

  static const Color background = Color(0xFFF8F8F8);
  static const Color secondaryBackground = Color(0xFFF6F7FB);

  static const Color divider = greyE7;
  static const Color titleColor = Color(0xff1F1F20);

  static const Color shadowColor = Color(0xFFEFEFEF);
  static const Color hintText = Color(0xFF666666);

  static const Color warningColor = Color(0xFFD0021B);

  static const cardColor = Colors.white;
  static const cardShadowColor = Color(0xFFEFEFEF);

  static const Color secureColor = Color(0xFF0A8800);
  static const Color color5A7095 = Color(0xFF5A7095);

  static const Color grey4D = Color(0xFF4D4D4D);
  static const Color grey50 = Color(0xFF505050);
  static const Color grey63 = Color(0xFF636363);
  static const Color grey80 = Color(0xFF808080);
  static const Color grey83 = Color(0xFF838383);
  static const Color grey8F = Color(0xFF8F8F8F);
  static const Color grey99 = Color(0xFF999999);
  static const Color greyBD = Color(0xFFBDBDBD);
  static const Color greyCC = Color(0xFFCCCCCC);
  static const Color greyE7 = Color(0xFFE7E7E7);
  static const Color greyEB = Color(0xFFEBEBEB);
  static const Color greyF1 = Color(0xFFF1F1F1);
  static const Color greyF5 = Color(0xFFF5F5F5);
}
