import 'package:flutter/material.dart';

import 'colours.dart';

abstract class TextStyles {
  /// 18号字体
  static const fontSize18Normal = TextStyle(
    fontSize: 18,
    fontWeight: FontWeight.w400,
  );

  static const fontSize18Medium = TextStyle(
    color: DColor.primaryTextColor,
    fontSize: 18,
    fontWeight: FontWeight.w500,
  );

  static const fontSize18SemiBold = TextStyle(
    color: DColor.primaryTextColor,
    fontSize: 18,
    fontWeight: FontWeight.w600,
  );

  static const fontSize18Bold = TextStyle(
    color: DColor.primaryTextColor,
    fontSize: 18,
    fontWeight: FontWeight.w700,
  );

  /// 16号字体
  static const fontSize16Normal = TextStyle(
    color: DColor.primaryTextColor,
    fontSize: 16,
    fontWeight: FontWeight.w400,
  );

  static const fontSize16Medium = TextStyle(
    color: DColor.primaryTextColor,
    fontSize: 16,
    fontWeight: FontWeight.w500,
  );

  static const fontSize16SemiBold = TextStyle(
    color: DColor.primaryTextColor,
    fontSize: 16,
    fontWeight: FontWeight.w600,
  );

  static const fontSize16Bold = TextStyle(
    color: DColor.primaryTextColor,
    fontSize: 16,
    fontWeight: FontWeight.w700,
  );

  /// 14号字体
  static const fontSize14Normal = TextStyle(
    color: DColor.primaryTextColor,
    fontSize: 14,
    fontWeight: FontWeight.w400,
  );

  static const fontSize15Normal = TextStyle(
    color: DColor.primaryTextColor,
    fontSize: 15,
    fontWeight: FontWeight.w400,
  );

  static const fontSize14Medium = TextStyle(
    color: DColor.primaryTextColor,
    fontSize: 14,
    fontWeight: FontWeight.w500,
  );

  static const fontSize14SemiBold = TextStyle(
    color: DColor.primaryTextColor,
    fontSize: 14,
    fontWeight: FontWeight.w600,
  );

  static const fontSize14Bold = TextStyle(
    color: DColor.primaryTextColor,
    fontSize: 14,
    fontWeight: FontWeight.w700,
  );

  /// 12号字体
  static const fontSize12Normal = TextStyle(
    color: DColor.primaryTextColor,
    fontSize: 12,
    fontWeight: FontWeight.w400,
  );

  static const fontSize13Normal = TextStyle(
    color: DColor.primaryTextColor,
    fontSize: 13,
    fontWeight: FontWeight.w400,
  );

  static const fontSize12Medium = TextStyle(
    color: DColor.primaryTextColor,
    fontSize: 12,
    fontWeight: FontWeight.w500,
  );

  static const fontSize12SemiBold = TextStyle(
    color: DColor.primaryTextColor,
    fontSize: 12,
    fontWeight: FontWeight.w600,
  );

  static const fontSize12Bold = TextStyle(
    color: DColor.primaryTextColor,
    fontSize: 12,
    fontWeight: FontWeight.w700,
  );
}
