import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';

abstract class EasyLoadingHelper {
  static Future<void> show({
    String? status,
    Widget? indicator,
    EasyLoadingMaskType maskType = EasyLoadingMaskType.clear,
    bool? dismissOnTap,
    required Future<void> Function() onAction,
    Function(Object)? onError,
    Duration delay = Duration.zero,
  }) async {
    Timer? timer;

    void showLoading() {
      EasyLoading.show(
        status: status,
        indicator: indicator,
        maskType: maskType,
        dismissOnTap: dismissOnTap,
      );
    }

    try {
      // 设置延迟计时器，超时后显示加载提示
      timer = Timer(delay, showLoading);

      // 执行实际操作
      await onAction();
    } catch (e) {
      e.printError();
      onError?.call(e);
    } finally {
      // 取消计时器防止重复显示
      timer?.cancel();

      // 如果加载提示已显示，则关闭
      if (EasyLoading.isShow) {
        EasyLoading.dismiss();
      }
    }
  }
}
