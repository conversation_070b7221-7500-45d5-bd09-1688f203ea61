import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:wukongimfluttersdk/wkim.dart';

class Logs {
  static File? _logFile;
  
  static Future<void> initLogFile() async {
    if (_logFile == null) {
      final directory = await getApplicationDocumentsDirectory();
      _logFile = File('${directory.path}/wukongim_logs.txt');
    }
  }

  static Future<void> _writeToFile(String message) async {
    await initLogFile();
    await _logFile?.writeAsString('$message\n', mode: FileMode.append);
  }

  static logToFile(String message) {
    if (WKIM.shared.options.debug) {
      // 获取调用栈信息
      StackTrace stackTrace = StackTrace.current;
      String caller = stackTrace.toString().split('\n')[1];

      // 记录到日志文件
      _writeToFile('$message, 调用位置: $caller, ${DateTime.now()}');
    }
  }

  static debug(Object msg) {
    if (WKIM.shared.options.debug) {
      // ignore: avoid_print
      print("debug:$msg");
    }
  }

  static info(Object msg) {
    if (WKIM.shared.options.debug) {
      // ignore: avoid_print
      print("info:$msg");
      _writeToFile("info:$msg");
    }
  }

  static error(Object msg) {
    if (WKIM.shared.options.debug) {
      // ignore: avoid_print
      print("error:$msg");
      _writeToFile("error:$msg");
    }
  }
}
