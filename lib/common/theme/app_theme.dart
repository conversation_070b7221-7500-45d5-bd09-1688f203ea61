import 'package:and/common/res/colours.dart';
import 'package:flutter/material.dart';

class AppTheme {
  // 亮色主题
  static final ThemeData lightTheme = ThemeData(
    useMaterial3: true,
    colorScheme: const ColorScheme.light(
        primary: DColor.primaryColor,
        surface: DColor.background,
        outlineVariant: Colors.transparent),
    primaryColor: DColor.primaryColor,
    scaffoldBackgroundColor: DColor.background,
    materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
    dividerColor: const Color(0xFFF7F8FA),
    buttonTheme: const ButtonThemeData(
        minWidth: 10,
        height: 10,
        materialTapTargetSize: MaterialTapTargetSize.shrinkWrap),
    checkboxTheme: CheckboxThemeData(
        fillColor: WidgetStateProperty.resolveWith((Set<WidgetState> states) {
      if (states.contains(WidgetState.disabled)) {
        return null;
      }
      if (states.contains(WidgetState.selected)) {
        return DColor.primaryColor;
      }
      return null;
    })),
    radioTheme: RadioThemeData(
        fillColor: WidgetStateProperty.resolveWith((Set<WidgetState> states) {
      if (states.contains(WidgetState.disabled)) {
        return null;
      }
      if (states.contains(WidgetState.selected)) {
        return DColor.primaryColor;
      }
      return null;
    })),
    switchTheme: SwitchThemeData(
        trackColor: WidgetStateProperty.resolveWith((Set<WidgetState> states) {
      if (states.contains(WidgetState.disabled)) {
        return null;
      }
      if (states.contains(WidgetState.selected)) {
        return DColor.primaryColor;
      }
      return null;
    })),
    listTileTheme: const ListTileThemeData(
      horizontalTitleGap: 0.0,
      minTileHeight: 48,
      minVerticalPadding: 5
    ),
    appBarTheme: const AppBarTheme(
        elevation: 1,
        surfaceTintColor: Colors.white,
        iconTheme: IconThemeData(
          color: Colors.black, // 设置返回按钮的颜色
        ),
        titleSpacing: 0,
        titleTextStyle:
            TextStyle(color: DColor.primaryTextColor, fontSize: 18)),
    brightness: Brightness.light,
  );
}
