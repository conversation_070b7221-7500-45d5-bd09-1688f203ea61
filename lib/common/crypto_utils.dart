import 'dart:convert';
import 'dart:typed_data';
import 'package:crypto/crypto.dart';
import 'package:encrypt/encrypt.dart';

import 'package:x25519/x25519.dart';

class CryptoUtils {
  static String aesKey = "";
  static String salt = "";
  static List<int>? dhPrivateKey;
  static List<int>? dhPublicKey;

  static init() {
    var keyPair = generateKeyPair();
    dhPrivateKey = keyPair.privateKey;
    dhPublicKey = keyPair.publicKey;
  }

  static generateMD5(String content) {
    return md5.convert(utf8.encode(content)).toString();
  }

  static setServerKeyAndSalt(String serverKey, String salt) {
    CryptoUtils.salt = salt;
    var sharedSecret = X25519(dhPrivateKey!, base64Decode(serverKey));
    var key = generateMD5(base64Encode(sharedSecret));
    if (key != "" && key.length > 16) {
      aesKey = key.substring(0, 16);
    } else {
      aesKey = key;
    }
  }

  // 加密
  static String aesEncrypt(String content) {
    final iv = _getIV();
    final key = Key(Uint8List.fromList(aesKey.codeUnits));
    final encrypter = Encrypter(AES(key, mode: AESMode.cbc));
    return encrypter.encrypt(content, iv: iv).base64;
  }

  // 解密
  static String aesDecrypt(String content) {
    final iv = _getIV();
    final key = Key(Uint8List.fromList(aesKey.codeUnits));
    var encrypter = Encrypter(AES(key, mode: AESMode.cbc));
    Encrypted encrypted = Encrypted(base64Decode(content));
    var decrypted = encrypter.decrypt(encrypted, iv: iv);
    return decrypted;
  }

  static IV _getIV() {
    // 确保 IV 长度为 16 字节
    var saltBytes = Uint8List.fromList(salt.codeUnits);
    var ivBytes = Uint8List(16);

    // 如果 salt 长度小于 16，用 0 填充
    // 如果 salt 长度大于 16，截取前 16 位
    for (var i = 0; i < 16; i++) {
      ivBytes[i] = i < saltBytes.length ? saltBytes[i] : 0x0;
    }

    return IV(ivBytes);
  }
}
