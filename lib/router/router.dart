//配置路由
import 'package:and/module/chat/chat_binding.dart';
import 'package:and/module/chat/chat_page.dart';
import 'package:and/module/chat/multiForward/multi_forward_binding.dart';
import 'package:and/module/chat/multiForward/multi_forward_page.dart';
import 'package:and/module/contact/apply/friend_apply_list_binding.dart';
import 'package:and/module/contact/apply/friend_apply_list_page.dart';
import 'package:and/module/contact/black/friend_black_list_binding.dart';
import 'package:and/module/contact/black/friend_black_list_page.dart';
import 'package:and/module/contact/choose/choose_contact_binding.dart';
import 'package:and/module/contact/choose/choose_contact_page.dart';
import 'package:and/module/contact/groups/my_groups_page.dart';
import 'package:and/module/country/choose_country_binding.dart';
import 'package:and/module/country/choose_country_page.dart';
import 'package:and/module/forget/pwd_forget_binding.dart';
import 'package:and/module/forget/pwd_forget_page.dart';
import 'package:and/module/group/delete/delete_group_member_binding.dart';
import 'package:and/module/group/delete/delete_group_member_page.dart';
import 'package:and/module/group/group_detail_binding.dart';
import 'package:and/module/group/group_detail_page.dart';
import 'package:and/module/group/join/group_join_binding.dart';
import 'package:and/module/group/join/group_join_page.dart';
import 'package:and/module/group/qr/group_qr_binding.dart';
import 'package:and/module/group/qr/group_qr_page.dart';
import 'package:and/module/login/login_binding.dart';
import 'package:and/module/login/login_page.dart';
import 'package:and/module/main_page.dart';
import 'package:and/module/my/commonSetting/common_setting_page.dart';
import 'package:and/module/my/commonSetting/language_setting_page.dart';
import 'package:and/module/my/messageNotification/message_notification_page.dart';
import 'package:and/module/my/securityAndPrivacy/destoryAccount/destory_account_binding.dart';
import 'package:and/module/my/securityAndPrivacy/destoryAccount/destory_account_page.dart';
import 'package:and/module/my/securityAndPrivacy/info_collection.dart';
import 'package:and/module/my/securityAndPrivacy/security_privacy_page.dart';
import 'package:and/module/register/register_binding.dart';
import 'package:and/module/register/register_page.dart';
import 'package:and/module/scan/code_scanner_page.dart';
import 'package:and/module/search/addfriend/add_friend_page.dart';
import 'package:and/module/search/globalSearch/channel_message_search_result.dart';
import 'package:and/module/search/globalSearch/global_search_binding.dart';
import 'package:and/module/search/globalSearch/global_search_page.dart';
import 'package:and/module/search/searchuser/search_user_binding.dart';
import 'package:and/module/search/searchuser/search_user_page.dart';
import 'package:and/module/share/share_target_page.dart';
import 'package:and/module/user/chat/chat_detail_binding.dart';
import 'package:and/module/user/chat/chat_detail_page.dart';
import 'package:and/module/user/completeInfo/complete_user_info_page.dart';
import 'package:and/module/user/mine/my_avatar_page.dart';
import 'package:and/module/user/mine/my_info_binding.dart';
import 'package:and/module/user/mine/my_info_page.dart';
import 'package:and/module/user/qr/user_qr_binding.dart';
import 'package:and/module/user/qr/user_qr_page.dart';
import 'package:and/module/user/user_info_binding.dart';
import 'package:and/module/user/user_info_page.dart';
import 'package:and/module/group/manager/group_manager_binding.dart';
import 'package:and/module/group/manager/group_manager_page.dart';
import 'package:get/get.dart';

class RouteGet {
  ///root page
  static const String main = "/";
  static const String login = "/login";
  static const String signup = "/signup";
  static const String forgotPwd = "/forgot";
  static const String chat = "/chat";
  static const String userInfo = "/user/detail";
  static const String scan = "/scan";
  static const String myInfo = "/my/detail";
  static const String myQr = "/my/qrcode";
  static const String addFriend = "/friend/add";
  static const String applyFriend = "/friend/apply";
  static const String blackFriend = "/friend/black";
  static const String searchUser = "/search/user";
  static const String chooseCountry = "/choose/country";
  static const String chooseContact = "/choose/contact";
  static const String myAvatar = "/my/avatar";
  static const String groupDetail = "/group/detail";
  static const String groupJoin = "/group/join";
  static const String groupMembersDelete = "/group/members/delete";
  static const String groupQr = "/group/qr";
  static const String chatDetail = "/chat/detail";
  static const String commonSetting = "/my/common/setting";
  static const String pushSetting = "/my/push/setting";
  static const String privacyAndSecurity = "/my/privacy/security";
  static const String language = "/my/language";
  static const String globalSearch = "/search/global";
  static const String channelMsgSearchResult = "/search/global/message";
  static const String chatMultiForward = "/chat/multi_forward";
  static const String myGroups = "/contact/my_groups";
  static const String infoCollection = "/my/info_collection";
  static const String destroyAccount = "/my/destroy_account";
  static const String completeUserInfo = "/my/complete_user_info";
  static const String destoryAccount = "/my/destory_account";
  static const String videoCall = "/video/call";
  static const String groupManager = "/group/manager";
  static const String addManager = "/group/manager/add";
  static const String removeManager = "/group/manager/remove";
  static const String shareTarget = "/share/target";

  static final List<GetPage> getPages = [
    GetPage(name: main, page: () => const MainPage()),
    GetPage(
        name: login, page: () => const LoginPage(), binding: LoginBinding()),
    GetPage(
        name: signup,
        page: () => const RegisterPage(),
        binding: RegisterBinding()),
    GetPage(
        name: forgotPwd,
        page: () => const PwdForgetPage(),
        binding: PwdForgetBinding()),
    GetPage(name: chat, page: () => const ChatPage(), binding: ChatBinding()),
    GetPage(
        name: userInfo,
        page: () => const UserInfoPage(),
        binding: UserInfoBinding()),
    GetPage(
        name: myInfo, page: () => const MyInfoPage(), binding: MyInfoBinding()),
    GetPage(name: scan, page: () => const CodeScannerPage()),
    GetPage(
        name: myQr, page: () => const UserQrPage(), binding: UserQrBinding()),
    GetPage(name: addFriend, page: () => const AddFriendPage()),
    GetPage(
        name: searchUser,
        page: () => const SearchUserPage(),
        binding: SearchUserBinding()),
    GetPage(
        name: chooseCountry,
        page: () => const ChooseCountryPage(),
        binding: ChooseCountryBinding()),
    GetPage(
        name: applyFriend,
        page: () => const FriendApplyListPage(),
        binding: FriendApplyListBinding()),
    GetPage(
        name: blackFriend,
        page: () => const FriendBlackListPage(),
        binding: FriendBlackListBinding()),
    GetPage(
        name: chooseContact,
        page: () => const ChooseContactPage(),
        binding: ChooseContactBinding()),
    GetPage(
        name: groupDetail,
        page: () => const GroupDetailPage(),
        binding: GroupDetailBinding()),
    GetPage(
        name: groupMembersDelete,
        page: () => const DeleteGroupMembersPage(),
        binding: DeleteGroupMemberBinding()),
    GetPage(
        name: groupQr,
        page: () => const GroupQrPage(),
        binding: GroupQrBinding()),
    GetPage(
        name: groupJoin,
        page: () => const GroupJoinPage(),
        binding: GroupJoinBinding()),
    GetPage(
        name: chooseContact,
        page: () => const ChooseContactPage(),
        binding: ChooseContactBinding()),
    GetPage(
        name: myAvatar,
        page: () => const MyAvatarPage(),
        binding: MyInfoBinding()),
    GetPage(
        name: chatDetail,
        page: () => const ChatDetailPage(),
        binding: ChatDetailBinding()),
    GetPage(name: commonSetting, page: () => const CommonSettingPage()),
    GetPage(name: pushSetting, page: () => const MessageNotificationPage()),
    GetPage(name: privacyAndSecurity, page: () => const SecurityPrivacyPage()),
    GetPage(name: language, page: () => const LanguagePage()),
    GetPage(
        name: globalSearch,
        page: () => const GlobalSearchPage(),
        binding: GlobalSearchBinding()),
    GetPage(
        name: channelMsgSearchResult,
        page: () => const ChannelMessageSearchResult(),
        binding: GlobalSearchBinding()),
    GetPage(
        name: chatMultiForward,
        page: () => const MultiForwardPage(),
        binding: MultiForwardBinding()),
    GetPage(name: myGroups, page: () => const MyGroupsPage()),
    GetPage(name: infoCollection, page: () => const InfoCollectionPage()),
    GetPage(
        name: destroyAccount,
        page: () => const DestoryAccountPage(),
        binding: DestoryAccountBinding()),
    GetPage(
        name: completeUserInfo,
        page: () => const CompleteUserInfoPage(
              isEmailMode: true,
            )),
    GetPage(
        name: groupManager,
        page: () => const GroupManagerPage(),
        binding: GroupManagerBinding()),
    GetPage(name: shareTarget, page: () => const ShareTargetPage()),
  ];
}
