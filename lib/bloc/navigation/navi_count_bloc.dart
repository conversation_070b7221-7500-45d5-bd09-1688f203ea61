import 'package:and/app.dart';
import 'package:and/module/main/main_tab_page.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'navi_count_event.dart';
import 'navi_count_state.dart';

class NaviCountBloc extends Bloc<NaviCountEvent, NaviCountState> {
  NaviCountBloc() : super(NaviCountState({})) {
    on<NaviCountEvent>((event, emit) {
      final newCounts = Map<TabType, int>.from(state.counts);
      newCounts[event.tabType] = event.count;
      emit(NaviCountState(newCounts));
    });
  }

  static void notify(TabType tabType, int count) async {
    globalContext
        ?.read<NaviCountBloc>()
        .add(NaviCountEvent(tabType, count));
  }
}
