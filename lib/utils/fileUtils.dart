import 'dart:io';

import 'package:and/constant/file_dir_keys.dart';
import 'package:mime/mime.dart';
import 'package:path/path.dart' as p;
import 'package:path_provider/path_provider.dart';
import 'package:downloadsfolder/downloadsfolder.dart';

class FileUtils {
  static final FileUtils _instance = FileUtils._();

  static FileUtils get instance => _instance;

  static late Directory storageDirectory;
  static late Directory saveDirectory;

  FileUtils._();

  static Future<void> init() async {
    storageDirectory = await getStorageDirectory();
    saveDirectory = await getSaveDirectory();
  }

  static Future<bool> isStoragePath(String path) async {
    return path.startsWith(storageDirectory.path);
  }

  static String getFullStoragePath(String path) {
    return p.join(storageDirectory.path, path);
  }

  static String getStoragePath(String fileName,
      {required int channelType,
      required String channelID,
      required String folderName,
      bool isFullPath = false}) {
    var path = p.join(folderName, channelType.toString(), channelID, fileName);
    if (isFullPath) {
      return getFullStoragePath(path);
    }
    return path;
  }

  static Future<String> getDefaultFullPath(String url) async {
    String fileName = url.split('/').last;
    var path = p.join("download", fileName);
    return getFullStoragePath(path);
  }

  static Future<Directory> getStorageDirectory() async {
    Directory dir = await getApplicationDocumentsDirectory();
    if (Platform.isAndroid) {
      dir = (await getExternalStorageDirectory()) ??
          await getApplicationDocumentsDirectory();
    }
    return dir;
  }

  static Future<Directory> getCacheImageDirectory() async {
    Directory dir = await getApplicationCacheDirectory();
    final Directory cacheImageDir =
        Directory('${dir.path}/${FileDirKeys.cacheImage}');

    // 确保目录存在
    if (!await cacheImageDir.exists()) {
      await cacheImageDir.create(recursive: true);
    }
    
    return cacheImageDir;
  }

  static Directory getWKImageDirectory() {
    return Directory('${storageDirectory.path}/${FileDirKeys.wkImages}');
  }

  static Directory getWKVideoDirectory() {
    return Directory('${storageDirectory.path}/${FileDirKeys.wkVideos}');
  }

  static Directory getChatDownloadDirectory() {
    return Directory(
        '${storageDirectory.path}/${FileDirKeys.chatDownloadFile}');
  }

  static Future<Directory> getSaveDirectory() async {
    Directory dir = await getStorageDirectory();
    try {
      var downloadDirectory = await getDownloadDirectory();
      dir = downloadDirectory;
    } catch (e) {
      print('Failed to retrieve downloads folder path $e');
    }

    var path = p.join(dir.path, "gleezy");
    return Directory(path);
  }

  static bool isMediaFile(File file) {
    final mimeType = lookupMimeType(file.path);
    if (mimeType != null &&
        (mimeType.startsWith("image") || mimeType.startsWith("video"))) {
      return true;
    }
    return false;
  }
}
