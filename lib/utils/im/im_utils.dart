import 'package:and/app.dart';
import 'package:and/bloc/navigation/navi_count_bloc.dart';
import 'package:and/cache/cache_helper.dart';
import 'package:and/constant/wk_message_content_type_ext.dart';
import 'package:and/http/my_http.dart';
import 'package:and/io/user.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/model/content/wk_call_content.dart';
import 'package:and/model/content/wk_file_content.dart';
import 'package:and/model/content/wk_multi_forward_content.dart';
import 'package:and/model/content/wk_sticker_content.dart';
import 'package:and/model/extension/wk_msg_ext.dart';
import 'package:and/model/user_login_info.dart';
import 'package:and/module/main/main_tab_page.dart';
import 'package:and/module/videocall/videocall_logic.dart';
import 'package:and/utils/audio_utils.dart';
import 'package:and/utils/im/msg_upload_utils.dart';
import 'package:and/utils/im/wk_cmd_utils.dart';
import 'package:and/utils/notification_utils.dart';
import 'package:wukongimfluttersdk/common/logs.dart';
import 'package:wukongimfluttersdk/common/options.dart';
import 'package:wukongimfluttersdk/model/wk_text_provider.dart';
import 'package:wukongimfluttersdk/type/const.dart';
import 'package:wukongimfluttersdk/wkim.dart';

import '../http_utils.dart';

class MessageTextProvider implements WKTextProvider {
  @override
  String? displayText(int contentType) {
    var context = globalContext;
    if (context == null) return null;
    switch (contentType) {
      case WkMessageContentType.card:
        return context.l10n.msgCard;
      case WkMessageContentType.image:
        return context.l10n.msgImage;
      case WkMessageContentType.gif:
        return context.l10n.msgGif;
      case WkMessageContentType.video:
        return context.l10n.msgVideo;
      case WkMessageContentType.voice:
        return context.l10n.msgVoice;
      case WkMessageContentType.location:
        return context.l10n.msgLocation;
      case WkMessageContentType.unknown:
        return context.l10n.msgUnknown;
      case WkMessageContentType.file:
        return context.l10n.msgFile;
    }
    return null;
  }

  @override
  String? searchableWord(int contentType) {
    return displayText(contentType);
  }
}

class IMUtils {
  static const key = "system";

  static Future<bool> initIM() async {
    UserLoginInfo? userInfo = CacheHelper.userProfile;
    if (userInfo == null) return false;
    bool result = await WKIM.shared
        .setup(Options.newDefault(userInfo.uid, userInfo.token));
    WKIM.shared.options.getAddr = (Function(String address) complete) async {
      try {
        var response = await UserApi(MyHttp.dio).getImIp(userInfo.uid ?? "");
        complete(response.tcpAddr);
      } catch (e) {
        print(e);
        complete("");
      }
    };
    // if (result) {
    //   WKIM.shared.connectionManager.connect();
    //   Logs.logToFile("iminit");
    // }
    CacheHelper.saveSyncFriend(true);
    // 注册自定义消息
    registerMsgContent();
    return result;
  }

  static void resumeConnection() {
    WKIM.shared.connectionManager.connect();
  }

  static void registerMsgContent() {
    WKIM.shared.messageManager.textProvider =  MessageTextProvider();
    WKIM.shared.messageManager.registerMsgContent(
        WkMessageContentTypeExt.multiForward,
        (data) => WKMultiForwardContent().decodeJson(data));
    WKIM.shared.messageManager.registerMsgContent(
        WkMessageContentType.file, (data) => WKFileContent().decodeJson(data));
    WKIM.shared.messageManager.registerMsgContent(
        WkMessageContentTypeExt.sticker,
        (data) => WKStickerContent(0, 0).decodeJson(data));

    WKIM.shared.messageManager.registerMsgContent(
        WkMessageContentTypeExt.cancelCall, (data) => WkCallContent().decodeJson(data));
    WKIM.shared.messageManager.registerMsgContent(
        WkMessageContentTypeExt.endCall, (data) => WkCallContent().decodeJson(data));
    WKIM.shared.messageManager.registerMsgContent(
        WkMessageContentTypeExt.callTimeOut, (data) => WkCallContent().decodeJson(data));
    WKIM.shared.messageManager.registerMsgContent(
        WkMessageContentTypeExt.rejectCall, (data) => WkCallContent().decodeJson(data));
    WKIM.shared.messageManager.registerMsgContent(
        WkMessageContentTypeExt.missedCall, (data) => WkCallContent().decodeJson(data));
  }

  // 监听sdk事件
  // 以下事件必须得实现
  static addListener() {
    // 监听同步消息扩展
    WKIM.shared.cmdManager.addOnCmdListener(key, (wkcmd) async {
      WKCmdUtils.sendCmd(wkcmd);
    });
    // 监听同步某个频道的消息
    WKIM.shared.messageManager.addOnSyncChannelMsgListener((channelID,
        channelType, startMessageSeq, endMessageSeq, limit, pullMode, back) {
      HttpUtils.syncChannelMsg(channelID, channelType, startMessageSeq,
          endMessageSeq, limit, pullMode, back);
    });
    // 监听获取channel资料（群/个人信息）
    WKIM.shared.channelManager
        .addOnGetChannelListener((channelId, channelType, back) {
      HttpUtils.getChannel(channelId, channelType, back: back);
    });
    // 监听上传消息附件
    WKIM.shared.messageManager
        .addOnUploadAttachmentListener((wkMsg, back) async {
      MsgUploadUtils.onUploadAttachment(wkMsg, back);
    });

    // 监听新消息
    WKIM.shared.messageManager.addOnNewMsgListener(key, (msgs) {
      VideoCallLogic.pendingMsgs = msgs;
      for (var msg in msgs) {
        var channelID = msg.channelID;
        var channelType = msg.channelType;
        if (msg.isCallMsg) {
          VideoCallLogic.handleCall(msg);
        } else if (msg.contentType ==
            WkMessageContentTypeExt.setNewGroupAdmin) {
          print("设置群管理");
          HttpUtils.syncGroupMembers(channelID);
        } else if (msg.contentType == WkMessageContentTypeExt.groupSystemInfo) {
          print("更新群信息");
          HttpUtils.getChannel(channelID, channelType);
          HttpUtils.syncGroupMembers(channelID);
        } else if (msg.contentType ==
                WkMessageContentTypeExt.addGroupMembersMsg ||
            msg.contentType == WkMessageContentTypeExt.removeGroupMembersMsg) {
          print("添加/删除群成员");
          HttpUtils.syncGroupMembers(channelID);
        } else {
          if (WkMessageContentTypeExt.isSupportNotification(msg.contentType)) {
            AudioUtils.remindUserIfNeed(msg);
            NotificationUtils.showLocalNotificationIfNeed(msg);
          }
        }

        //TODO 其他类型消息的处理
      }
    });

    // 监听同步最近会话
    WKIM.shared.conversationManager
        .addOnSyncConversationListener((lastSsgSeqs, msgCount, version, back) {
      HttpUtils.syncConversation(lastSsgSeqs, msgCount, version, back);
    });
    WKIM.shared.conversationManager.addOnRefreshMsgListListener(key, (msgs) async {
      VideoCallLogic.pendingMsgs = msgs;
      _updateUnreadCount();
    });
    WKIM.shared.conversationManager.addOnDeleteMsgListener(key,
        (channelID, channelType) {
      _updateUnreadCount();
    });
    //监听清除所有未读红点
    WKIM.shared.conversationManager.addOnClearAllRedDotListener(key, () {
      _updateUnreadCount();
    });
  }

  static removeListener() {
    WKIM.shared.cmdManager.removeCmdListener(key);
    WKIM.shared.messageManager.removeNewMsgListener(key);

    WKIM.shared.conversationManager.removeOnRefreshMsgListListener(key);
    WKIM.shared.conversationManager.removeDeleteMsgListener(key);
    WKIM.shared.conversationManager.removeClearAllRedDotListener(key);
  }

  static _updateUnreadCount() async {
    var unreadCount = await WKIM.shared.conversationManager.getAllUnreadCount();
    NaviCountBloc.notify(TabType.conversations, unreadCount);
  }

}
