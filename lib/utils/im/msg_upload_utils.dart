import 'dart:io';

import 'package:and/constant/wk_message_content_type_ext.dart';
import 'package:and/model/content/wk_file_content.dart';
import 'package:and/model/extension/wk_media_content_ext.dart';
import 'package:and/model/extension/wk_video_content_ext.dart';
import 'package:and/utils/http_utils.dart';
import 'package:and/utils/video_utils.dart';
import 'package:wukongimfluttersdk/entity/msg.dart';
import 'package:wukongimfluttersdk/model/wk_image_content.dart';
import 'package:wukongimfluttersdk/model/wk_video_content.dart';
import 'package:wukongimfluttersdk/model/wk_voice_content.dart';
import 'package:wukongimfluttersdk/type/const.dart';

class MsgUploadUtils {
  static onUploadAttachment(
      WKMsg wkMsg, Function(bool isSuccess, WKMsg wkMsg) back) async {
    if (wkMsg.contentType == WkMessageContentType.image) {
      try {
        WKImageContent imageContent = wkMsg.messageContent! as WKImageContent;
        if (imageContent.url.isNotEmpty) {
          back(true, wkMsg);
          return;
        }
        var url = await HttpUtils.uploadChannelFile(
            wkMsg.channelID, wkMsg.channelType, imageContent.localUri.path);
        if (url == null) {
          back(false, wkMsg);
          return;
        }
        imageContent.url = url;
        wkMsg.messageContent = imageContent;
        back(true, wkMsg);
      } catch (e) {
        back(false, wkMsg);
      }
    } else if (wkMsg.contentType == WkMessageContentType.voice) {
      try {
        WKVoiceContent voiceContent = wkMsg.messageContent! as WKVoiceContent;
        if (voiceContent.url.isNotEmpty) {
          back(true, wkMsg);
          return;
        }
        var url = await HttpUtils.uploadChannelFile(
            wkMsg.channelID, wkMsg.channelType, voiceContent.localUri.path);
        if (url == null) {
          back(false, wkMsg);
          return;
        }
        voiceContent.url = url;
        wkMsg.messageContent = voiceContent;
        back(true, wkMsg);
      } catch (e) {
        back(false, wkMsg);
      }
    } else if (wkMsg.contentType == WkMessageContentType.video) {
      try {
        WKVideoContent videoContent = wkMsg.messageContent! as WKVideoContent;
        if (videoContent.url.isNotEmpty) {
          back(true, wkMsg);
          return;
        }
        var file = File.fromUri(videoContent.localUri);
        if (!file.existsSync()) {
          back(false, wkMsg);
          return;
        }

        if (videoContent.cover.isEmpty) {
          // 获取视频封面
          String? coverLocalPath = videoContent.coverLocalPath;
          if (coverLocalPath.isEmpty) {
            coverLocalPath = await VideoUtils.getVideoThumbnail(file.path);
            if (coverLocalPath != null) {
              videoContent.coverLocalPath = coverLocalPath;
            }
          }
          if (coverLocalPath == null) {
            back(false, wkMsg);
            return;
          }
          var coverUrl = await HttpUtils.uploadChannelFile(wkMsg.channelID,
              wkMsg.channelType, videoContent.localCoverUri.path);
          if (coverUrl == null) {
            back(false, wkMsg);
            return;
          } else {
            videoContent.cover = coverUrl;
          }
        }

        var url = await HttpUtils.uploadChannelFile(
            wkMsg.channelID, wkMsg.channelType, videoContent.localUri.path,
            isLarge: true);
        if (url == null) {
          back(false, wkMsg);
          return;
        }
        videoContent.url = url;
        wkMsg.messageContent = videoContent;
        back(true, wkMsg);
      } catch (e) {
        back(false, wkMsg);
      }
    } else if (wkMsg.contentType == WkMessageContentType.file) {
      try {
        WKFileContent fileContent = wkMsg.messageContent! as WKFileContent;
        if (fileContent.url.isNotEmpty) {
          back(true, wkMsg);
          return;
        }
        var url = await HttpUtils.uploadChannelFile(
            wkMsg.channelID, wkMsg.channelType, fileContent.localUri.path);
        if (url == null) {
          back(false, wkMsg);
          return;
        }
        fileContent.url = url;
        wkMsg.messageContent = fileContent;
        back(true, wkMsg);
      } catch (e) {
        back(false, wkMsg);
      }
    }  else if (wkMsg.contentType == WkMessageContentTypeExt.sticker) {
      back(true, wkMsg);
    }
  }
}
