import 'package:and/cache/cache_helper.dart';
import 'package:and/constant/common_keys.dart';
import 'package:and/utils/http_utils.dart';
import 'package:flutter/services.dart';
import 'package:wukongimfluttersdk/common/logs.dart';
import 'package:wukongimfluttersdk/wkim.dart';

class BackgroundTask {
  static const MethodChannel _channel = MethodChannel('background_task');

  static void init() {
    _channel.setMethodCallHandler((MethodCall call) async {
      if (call.method == "disconnectIM") {
        disconnectIM();
      } else if (call.method == "uploadDeviceToken") {
        uploadDeviceToken(call.arguments['deviceToken']);
      }
    });
  }

  /// 启动后台任务（iOS: beginBackgroundTask, Android: WorkManager）
  static Future<void> startBackgroundTask() async {
    try {
      await _channel.invokeMethod('startBackgroundTask');
    } catch (e) {
      print("启动后台任务失败: $e");
    }
  }

  /// 结束后台任务（iOS: endBackgroundTask, Android: 取消 WorkManager 任务）
  static Future<void> endBackgroundTask() async {
    try {
      await _channel.invokeMethod('endBackgroundTask');
    } catch (e) {
      print("结束后台任务失败: $e");
    }
  }

  /// 断开 IM 连接（Flutter 侧方法，供 iOS/Android 调用）
  static void disconnectIM() {
    // Logs.logToFile("disconnectIM");
    WKIM.shared.connectionManager.disconnect(false);
  }

  static void uploadDeviceToken(String deviceToken) {
    if (CacheHelper.token != null) {
      HttpUtils.uploadDeviceToken(deviceToken);
    }
    CacheHelper.saveDeviceToken(deviceToken);
  }
}