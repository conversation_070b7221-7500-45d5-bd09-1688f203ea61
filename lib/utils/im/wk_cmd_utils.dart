import 'package:and/app.dart';
import 'package:and/constant/common_keys.dart';
import 'package:and/constant/wk_cmd_keys.dart';
import 'package:and/eventbus/pinned_msg_sync_event.dart';
import 'package:and/model/extension/wk_channel_ext.dart';
import 'package:and/model/sync_pinned_message.dart';
import 'package:and/module/chat/chat_logic.dart';
import 'package:and/module/user/chat/chat_detail_logic.dart';
import 'package:and/module/videocall/videocall_logic.dart';
import 'package:and/utils/http_utils.dart';
import 'package:and/utils/notification_utils.dart';
import 'package:wukongimfluttersdk/entity/cmd.dart';
import 'package:wukongimfluttersdk/entity/conversation.dart';
import 'package:wukongimfluttersdk/entity/msg.dart';
import 'package:wukongimfluttersdk/type/const.dart';
import 'package:wukongimfluttersdk/wkim.dart';

class WKCmdUtils {
  static void sendCmd(WKCMD wkcmd) async {
    print("Send cmd:${wkcmd.cmd}");
    if (wkcmd.cmd == WKCMDKeys.wkMessageRevoke) {
      String channelID = wkcmd.param['channel_id'];
      var channelType = wkcmd.param['channel_type'];
      var messageId = wkcmd.param['message_id'];
      var fromUid = wkcmd.param['from_uid'];

      var channel =
          await WKIM.shared.channelManager.getChannel(channelID, channelType);
      //是否撤回提醒
      int revokeRemind = 1;
      //TODO 临时处理，都按撤回提醒来做
      // if (channel != null) {
      //   revokeRemind = channel.revokeRemind ?? 1;
      // }

      WKMsg? wkMsg =
          await WKIM.shared.messageManager.getWithMessageID(messageId);

      /// 撤回消息本地推送
      if (wkMsg != null && !channelID.endsWith(WKCMDKeys.wkCMDEnd)) {
        NotificationUtils.showLocalNotificationIfNeed(wkMsg, isRecalled: true);
      }

      if (revokeRemind == 1) {
        // 同步消息扩展
        var msgExtra = wkMsg?.wkMsgExtra;
        if (msgExtra == null) {
          msgExtra = WKMsgExtra();
          msgExtra.channelID = channelID;
          msgExtra.channelType = channelType;
          msgExtra.messageID = messageId;
        }

        msgExtra.revoke = 1;
        msgExtra.revoker = fromUid ?? '';
        msgExtra.editedAt =
            (DateTime.now().millisecondsSinceEpoch / 1000).toInt();
        WKIM.shared.messageManager.saveRemoteExtraMsg([msgExtra]);

        HttpUtils.syncMsgExtra(channelID, channelType);
      } else {
        if (wkMsg != null) {
          HttpUtils.deleteMsg([wkMsg],
              channelID: channelID, channelType: channelType);
        }
        int rowNo =
            await WKIM.shared.messageManager.deleteWithMessageID(messageId);
        WKConversationMsg? uiConversion = await WKIM.shared.conversationManager
            .getMsgWithChannel(channelID, channelType);
        if (uiConversion != null) {
          if (rowNo < uiConversion.unreadCount) {
            uiConversion.unreadCount--;
          }
          WKIM.shared.conversationManager.updateUnreadCount(uiConversion);
        }
      }
    } else if (wkcmd.cmd == WKCMDKeys.wkChannelUpdate) {
      var channelID = wkcmd.param['channel_id'];
      var channelType = wkcmd.param['channel_type'];
      if (channelID != '') {
        HttpUtils.getChannel(channelID, channelType);
      }
    } else if (wkcmd.cmd == WKCMDKeys.wkUnreadClear) {
      print('清空红点的cmd');
      // 未读消息清除
      var channelID = wkcmd.param['channel_id'];
      var channelType = wkcmd.param['channel_type'];
      var unread = wkcmd.param['unread'];
      if (channelID != '') {
        WKIM.shared.conversationManager
            .updateRedDot(channelID, channelType, unread);
      }
    } else if (wkcmd.cmd == WKCMDKeys.wkOnlineStatus) {
      print('在线状态');
      var deviceFlag = wkcmd.param['device_flag'];
      var mainDeviceFlag = wkcmd.param['main_device_flag'];
      //TODO

      var online = wkcmd.param['online'];
      var allOffline =
          wkcmd.param['all_offline'] ?? 1; //如果字段不存在或者为0则表示未下线，为1则表示已下线
      var uid = wkcmd.param['uid'];
      if (uid != '') {
        var channel = await WKIM.shared.channelManager
            .getChannel(uid, WKChannelType.personal);
        if (channel != null) {
          channel.online = (allOffline == 1) ? 0 : 1;
          channel.lastOffline =
              (DateTime.now().millisecondsSinceEpoch / 1000).toInt();
          WKIM.shared.channelManager.addOrUpdateChannel(channel);
        }
      }
    } else if (wkcmd.cmd == WKCMDKeys.wkFriendRequest) {
      print('好友申请请求');
      HttpUtils.syncFriendApplyCount();
    } else if (wkcmd.cmd == WKCMDKeys.wkFriendAccept) {
      print('好友申请通过');
      HttpUtils.syncFriends();
    } else if (wkcmd.cmd == WKCMDKeys.wkFriendDeleted) {
      print('好友被删除');
      HttpUtils.syncFriends();
    } else if (wkcmd.cmd == WKCMDKeys.wkMemberUpdate) {
      print('更新频道成员');
      var groupNo = wkcmd.param['group_no'];
      if (groupNo != null) {
        HttpUtils.syncGroupMembers(groupNo);
      }
    } else if (wkcmd.cmd == WKCMDKeys.wkSyncMessageExtra) {
      if (wkcmd.param == null) {
        return;
      }

      var channelID = wkcmd.param['channel_id'];
      var channelType = wkcmd.param['channel_type'];
      if (channelID == null) {
        return;
      }
      HttpUtils.syncMsgExtra(channelID, channelType);
    } else if (wkcmd.cmd == WKCMDKeys.wkSyncReminders) {
      HttpUtils.syncReminder();
    } else if (wkcmd.cmd == WKCMDKeys.wkSyncConversationExtra) {
      HttpUtils.syncConversationExtra();
    } else if (wkcmd.cmd == WKCMDKeys.wkSyncMessageReaction) {
      if (wkcmd.param == null) {
        return;
      }

      var channelID = wkcmd.param['channel_id'];
      var channelType = wkcmd.param['channel_type'];
      if (channelID == null) {
        return;
      }
      HttpUtils.syncReactions(channelID, channelType);
    } else if (wkcmd.cmd == WKCMDKeys.wkSingleCall) {
      /// 过期的邀请消息不处理,以防打开应用突然显示很久前的通话邀请
      final timestamp = wkcmd.param['timestamp'] ?? 0;
      if (timestamp >=
              DateTime.now().millisecondsSinceEpoch ~/ 1000 -
                  CommonKeys.callTimeOut ||
          timestamp == 0) {
        VideoCallLogic.showCall(wkcmd);
      }
    } else if (wkcmd.cmd == WKCMDKeys.wkConversationDeleted) {
      if (wkcmd.param == null) {
        return;
      }

      var channelID = wkcmd.param['channel_id'];
      var channelType = wkcmd.param['channel_type'];
      if (channelID == null) {
        return;
      }
      await WKIM.shared.conversationManager.deleteMsg(channelID, channelType);
    } else if (wkcmd.cmd == WKCMDKeys.conversationMessageBothClear) {
      final channelID = wkcmd.param['channel_id'];
      final channelType = wkcmd.param['channel_type'];
      final messageIDs = wkcmd.param['messageIDs'] as List<dynamic>?;
      if (messageIDs != null) {
        var ids = messageIDs.map((e) => e.toString()).toList();
        WKIM.shared.messageManager.deleteWithMessageIDs(ids);
      } else {
        WKIM.shared.messageManager.clearWithChannel(channelID, channelType);
      }
    } else if (wkcmd.cmd == WKCMDKeys.syncAnonymous) {
      final channelID = wkcmd.param['channel_id'];
      final channelType = wkcmd.param['channel_type'];
      if (channelID == null) {
        return;
      }
      HttpUtils.syncGroupMembers(channelID, maxVersion: 0);
    } else if (wkcmd.cmd == WKCMDKeys.syncChannelUnread) {
      final channelID = wkcmd.param['channel_id'];
      final channelType = wkcmd.param['channel_type'];
      if (channelID == null) {
        return;
      }
      HttpUtils.syncUnreadCount(channelID, channelType);
    } else if (wkcmd.cmd == WKCMDKeys.syncPinnedMessage) {
      final channelID = wkcmd.param['channel_id'];
      final channelType = wkcmd.param['channel_type'];
      if (channelID == null) {
        return;
      }
      var syncPinnedMessage =
          await HttpUtils.syncPinnedMessages(channelID, channelType);
      if (syncPinnedMessage != null) {
        eventBus.fire(
            PinnedMsgSyncEvent(channelID, channelType, syncPinnedMessage));
      }
    }
  }
}
