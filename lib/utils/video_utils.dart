import 'dart:io';
import 'dart:ui';

import 'package:get/get.dart';
import 'package:get_thumbnail_video/index.dart';
import 'package:get_thumbnail_video/video_thumbnail.dart';
import 'package:path/path.dart' as p;
import 'package:tmp_path/tmp_path.dart';
import 'package:video_player/video_player.dart';

class VideoInfo {
  final int width;
  final int height;
  final int size;
  final Duration duration;

  VideoInfo({
    required this.width,
    required this.height,
    required this.duration,
    required this.size,
  });
}

class VideoUtils {
  static Future<VideoInfo> getVideoInfo(String path) async {
    final controller = VideoPlayerController.file(File(path));
    await controller.initialize();
    final size = controller.value.size;
    final duration = controller.value.duration;
    await controller.dispose();

    File file = File(path);
    var length = await file.length();
    return VideoInfo(
      size: length,
      width: size.width.toInt(),
      height: size.height.toInt(),
      duration: duration,
    );
  }

  static Future<String?> getVideoThumbnail(String path,
      {int width = 300, int height = 300, int quality = 90}) async {
    final parent = Directory.systemTemp.path;
    final name = path.split('/').last.split('.').first;
    final destPath = p.join(parent, "${path.hashCode.toString()}_$name.jpg");
    final destFile = File(destPath);
    if (destFile.existsSync()) {
      return destPath;
    }

    try {
      XFile thumbnailFile = await VideoThumbnail.thumbnailFile(
        video: path,
        thumbnailPath: destPath,
        imageFormat: ImageFormat.JPEG,
        maxHeight: height,
        maxWidth: width,
        quality: quality,
      );
      return thumbnailFile.path;
    } catch(e) {
      e.printError();
    }
    return null;
  }
}
