import 'package:flutter/material.dart';
import 'package:flutter/gestures.dart';

class SpannableTextBuilder {
  String _text = '';
  final List<TextStyle> _styles = [];
  final List<int> _styleStarts = [];
  final List<int> _styleEnds = [];
  final List<WidgetSpan> _widgetSpans = [];
  final List<int> _widgetPositions = [];
  final List<GestureRecognizer> _recognizers = [];
  final List<int> _recognizerStarts = [];
  final List<int> _recognizerEnds = [];

  /// 添加纯文本
  SpannableTextBuilder append(String text, {TextStyle? style}) {
    if (style != null) {
      _styles.add(style);
      _styleStarts.add(_text.length);
      _styleEnds.add(_text.length + text.length);
    }
    _text += text;
    return this;
  }

  /// 添加Widget
  SpannableTextBuilder appendWidget(Widget widget, {PlaceholderAlignment alignment = PlaceholderAlignment.middle}) {
    _widgetSpans.add(WidgetSpan(child: widget, alignment: alignment));
    _widgetPositions.add(_text.length);
    return this;
  }

  /// 设置指定范围的文本样式
  SpannableTextBuilder setSpan(TextStyle style, int start, int end) {
    if (start < 0 || end > _text.length || start >= end) return this;
    
    _styles.add(style);
    _styleStarts.add(start);
    _styleEnds.add(end);
    return this;
  }
  
  /// 设置指定范围的文本样式和点击事件
  SpannableTextBuilder setSpanWithClick(TextStyle style, int start, int end, VoidCallback onTap) {
    if (start < 0 || end > _text.length || start >= end) return this;
    
    _styles.add(style);
    _styleStarts.add(start);
    _styleEnds.add(end);
    
    final recognizer = TapGestureRecognizer()..onTap = onTap;
    _recognizers.add(recognizer);
    _recognizerStarts.add(start);
    _recognizerEnds.add(end);
    
    return this;
  }

  /// 构建RichText
  RichText build({TextAlign textAlign = TextAlign.start}) {
    return RichText(
      text: TextSpan(children: buildSpans()),
      textAlign: textAlign,
    );
  }

  /// 构建RichText的spans
  List<InlineSpan> buildSpans() {
    List<InlineSpan> spans = [];
    int currentPos = 0;
    int widgetIndex = 0;

    while (currentPos < _text.length) {
      // 检查是否需要插入widget
      while (widgetIndex < _widgetPositions.length && _widgetPositions[widgetIndex] == currentPos) {
        spans.add(_widgetSpans[widgetIndex]);
        widgetIndex++;
      }

      // 找出当前位置的所有样式
      TextStyle? mergedStyle;
      for (int i = 0; i < _styleStarts.length; i++) {
        if (_styleStarts[i] <= currentPos && currentPos < _styleEnds[i]) {
          mergedStyle = mergedStyle?.merge(_styles[i]) ?? _styles[i];
        }
      }
      
      // 找出当前位置的点击事件
      GestureRecognizer? recognizer;
      for (int i = 0; i < _recognizerStarts.length; i++) {
        if (_recognizerStarts[i] <= currentPos && currentPos < _recognizerEnds[i]) {
          recognizer = _recognizers[i];
          break; // 只使用第一个匹配的recognizer
        }
      }

      // 找出下一个分割点
      int nextPos = _text.length;
      for (int i = 0; i < _styleStarts.length; i++) {
        if (_styleStarts[i] > currentPos && _styleStarts[i] < nextPos) {
          nextPos = _styleStarts[i];
        }
        if (_styleEnds[i] > currentPos && _styleEnds[i] < nextPos) {
          nextPos = _styleEnds[i];
        }
      }
      for (int pos in _widgetPositions) {
        if (pos > currentPos && pos < nextPos) {
          nextPos = pos;
        }
      }

      // 添加文本span
      spans.add(TextSpan(
        text: _text.substring(currentPos, nextPos),
        style: mergedStyle,
        recognizer: recognizer,
      ));

      currentPos = nextPos;
    }

    // 添加剩余的widget
    while (widgetIndex < _widgetPositions.length) {
      spans.add(_widgetSpans[widgetIndex]);
      widgetIndex++;
    }

    return spans;
  }

  /// 获取当前文本的长度
  int get length => _text.length;

  /// 清空所有内容
  void clear() {
    _text = '';
    _styles.clear();
    _styleStarts.clear();
    _styleEnds.clear();
    _widgetSpans.clear();
    _widgetPositions.clear();
    
    // 清理recognizers以避免内存泄漏
    for (var recognizer in _recognizers) {
      recognizer.dispose();
    }
    _recognizers.clear();
    _recognizerStarts.clear();
    _recognizerEnds.clear();
  }
}