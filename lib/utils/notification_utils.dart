import 'dart:io';

import 'package:and/app.dart';
import 'package:and/cache/cache_helper.dart';
import 'package:and/constant/wk_message_content_type_ext.dart';
import 'package:and/firebase_options.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/model/extension/wk_channel_ext.dart';
import 'package:and/model/extension/wk_msg_ext.dart';
import 'package:and/module/user/mine/my_setting_manager.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_app_badger/flutter_app_badger.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:wukongimfluttersdk/entity/channel.dart';
import 'package:wukongimfluttersdk/entity/msg.dart';
import 'package:wukongimfluttersdk/type/const.dart';
import 'package:wukongimfluttersdk/wkim.dart';

import 'http_utils.dart';

class NotificationUtils {
  static final NotificationUtils _instance = NotificationUtils._internal();

  static NotificationUtils get instance => _instance;

  factory NotificationUtils() => _instance;

  NotificationUtils._internal();

  static final FlutterLocalNotificationsPlugin _notificationsPlugin =
      FlutterLocalNotificationsPlugin();

  /// 初始化推送功能
  static Future<void> init() async {
    await Firebase.initializeApp(
        options: DefaultFirebaseOptions.currentPlatform);

    FlutterError.onError = (errorDetails) {
      FirebaseCrashlytics.instance.recordFlutterFatalError(errorDetails);
    };
    // Pass all uncaught asynchronous errors that aren't handled by the Flutter framework to Crashlytics
    PlatformDispatcher.instance.onError = (error, stack) {
      FirebaseCrashlytics.instance.recordError(error, stack, fatal: true);
      return true;
    };

    if (Platform.isAndroid) {
      _setupFirebaseMessagingListeners();
      NotificationUtils.getToken();

      /// 自定义邀请推送铃声
      const AndroidNotificationChannel callChannel = AndroidNotificationChannel(
        'custom_sound_channel', // channelId
        '通话邀请通知',
        description: '用于语音或视频通话的通知',
        importance: Importance.max,
        sound: RawResourceAndroidNotificationSound('call_incoming'),
      );

      await _notificationsPlugin
          .resolvePlatformSpecificImplementation<
              AndroidFlutterLocalNotificationsPlugin>()
          ?.createNotificationChannel(callChannel);
    }

    _initLocalNotification();
  }

  static Future<void> requestNotificationPermission() async {
    if (Platform.isAndroid) {
      NotificationSettings settings =
      await FirebaseMessaging.instance.requestPermission(
        alert: true,
        badge: true,
        sound: true,
      );

      if (settings.authorizationStatus == AuthorizationStatus.authorized) {
        print('✅ 推送权限已授权');
      } else {
        print('❌ 推送权限被拒绝');
      }
    }
  }

  /// 获取 Firebase 推送 Token
  static Future<String?> getToken() async {
    String? fcmToken = await FirebaseMessaging.instance.getToken();
    // String? deviceToken = await FirebaseMessaging.instance.getAPNSToken();
    print("📲 FCM Token: $fcmToken");

    if (fcmToken != null) {
      if (CacheHelper.token != null) {
        HttpUtils.uploadDeviceToken(fcmToken);
      }
      CacheHelper.saveDeviceToken(fcmToken);
    }

    return fcmToken;
  }

  /// 监听 Firebase 推送消息
  static void _setupFirebaseMessagingListeners() {
    // 应用在前台时
    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      print("📩 前台收到推送: ${message.notification?.title}");
      // _showLocalNotification(message);
    });

    // 应用在后台被点击
    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
      print("🚀 用户点击通知: ${message.notification?.title}");
    });

    // 应用在终止状态时启动
    FirebaseMessaging.instance
        .getInitialMessage()
        .then((RemoteMessage? message) {
      if (message != null) {
        print("🔄 终止状态启动应用: ${message.notification?.title}");
      }
    });

    FirebaseMessaging.instance.onTokenRefresh.listen((fcmToken) {
      print("📲 FCM Token: $fcmToken");
      if (CacheHelper.token != null) {
        HttpUtils.uploadDeviceToken(fcmToken);
      }
      CacheHelper.saveDeviceToken(fcmToken);
    }).onError((err) {
      // Error getting token.
    });
  }

  /// 初始化本地通知
  static void _initLocalNotification() {
    var androidSettings =
        const AndroidInitializationSettings('@mipmap/ic_launcher');
    var iosSettings = const DarwinInitializationSettings();
    var settings =
        InitializationSettings(android: androidSettings, iOS: iosSettings);

    _notificationsPlugin.initialize(settings);
  }

  static Future<void> showLocalNotificationIfNeed(WKMsg message,
      {bool isRecalled = false}) async {
    if (!WkMessageContentTypeExt.isSupportNotification(message.channelType) ||
        !message.header.redDot ||
        !MySettingManager.instance.newMsgNotice) {
      return;
    }
    if (_isAppActive()) {
      return;
    }
    await showLocalNotification(message, isRecalled: isRecalled);
  }

  static bool _isAppActive() {
    return WidgetsBinding.instance.lifecycleState == AppLifecycleState.resumed;
  }

  static Future<void> updateBadge() async {
    if (CacheHelper.token != null) {
      var unreadCount =
          await WKIM.shared.conversationManager.getAllUnreadCount();
      FlutterAppBadger.updateBadgeCount(unreadCount);
      HttpUtils.updateBadge(unreadCount);
    } else {
      FlutterAppBadger.removeBadge();
    }
  }

  static Future<void> showVideoCallLocalNotification(
      {required int pushId,
      required String title,
      String content = '',
      bool isInvite = false}) async {
    if (_isAppActive()) {
      return;
    }

    AndroidNotificationDetails androidDetails = AndroidNotificationDetails(
      'channel_id',
      'channel_name',
      importance: Importance.max,
      priority: Priority.max,
    );

    DarwinNotificationDetails iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
        sound: isInvite ? "call_incoming.wav" : null);

    NotificationDetails details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    /// 非邀请消息先把邀请消息移除再显示才能停止邀请消息的铃声
    /// 偶现出现大量本地邀请通话消息（如果ID一样的话移除旧的确保只显示一条）
    await _notificationsPlugin.cancel(pushId);
    await _notificationsPlugin.show(
      pushId,
      title,
      content,
      details,
    );
  }

  /// 显示本地通知 , RemoteMessage? remoteMessage
  /// isRecalled 是否为撤回消息
  static Future<void> showLocalNotification(WKMsg message,
      {bool isRecalled = false}) async {
    final WKChannel? channelInfo = await WKIM.shared.channelManager
        .getChannel(message.channelID, message.channelType);
    if (channelInfo == null || channelInfo.mute == 1) {
      return;
    }

    String title;
    String alert;
    String content;
    final String fromName = await message.fromNameDisplay;

    if (message.channelType == WKChannelType.personal &&
        fromName.isNotEmpty == true) {
      title = fromName;
    } else {
      title = channelInfo.displayName;
    }
    switch (message.contentType) {
      case WkMessageContentType.text:
        alert = message.messageContent?.content ?? '';
        break;
      case WkMessageContentType.image:
        alert = globalContext?.l10n.msgImage ?? '';
        break;
      case WkMessageContentType.gif:
        alert = globalContext?.l10n.msgGif ?? '';
        break;
      case WkMessageContentType.voice:
        alert = globalContext?.l10n.msgVoice ?? '';
        break;
      case WkMessageContentType.video:
        alert = globalContext?.l10n.msgVideo ?? '';
        break;
      case WkMessageContentType.location:
        alert = globalContext?.l10n.msgLocation ?? '';
        break;
      case WkMessageContentType.card:
        alert = globalContext?.l10n.msgCard ?? '';
        break;
      case WkMessageContentType.file:
        alert = globalContext?.l10n.msgFile ?? '';
        break;
      default:
        alert = message.messageContent?.content ?? '';
        return;
    }

    if (isRecalled) {
      alert = globalContext?.l10n.messageRecalled ?? '';
    }
    content = (fromName.isNotEmpty == true &&
            message.channelType != WKChannelType.personal)
        ? '$fromName：$alert'
        : alert;

    const AndroidNotificationDetails androidDetails =
        AndroidNotificationDetails(
      'channel_id',
      'channel_name',
      importance: Importance.high,
      priority: Priority.high,
    );

    const DarwinNotificationDetails iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    const NotificationDetails details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    print("pushed ---- ${message.messageSeq}");
    await _notificationsPlugin.show(
      message.messageSeq,
      title,
      content,
      details,
    );
  }

  /// **测试远程推送 service key不能用了所以用命令测
  Future<void> testRemoteNotification() async {
    // gcloud auth activate-service-account --key-file=/Users/<USER>/Downloads/gleezy-test-01-firebase-adminsdk-fbsvc-b68edb9eb2.json
    // gcloud auth print-access-token
    //
    //   curl -X POST "https://fcm.googleapis.com/v1/projects/gleezy-test-01/messages:send" \
    //   -H "Authorization: Bearer ya29.c.c0ASRK0GZKKZotWTuHRZDTN6C1bcQpv95xBupJNyiEw50f62-hC4TpEPDQULb9hI4tmmQYA6l8TRyBajcizy3jeLINpg5_wwqOZnZR8UIELaYjsRmOSpqNsYjYCfyZe_bM3-3eerSzQKy8ipOriHa0e7Z9W2XoEMWllQopA-qYR6Id170elp3enN9TQSfqvgELPcgt0wzq6Vuj8XBL2e49cPNODf_r3f4OW27vGrvpovyx1U3o5yhOc52fmikMENE17GI8xm9z-NRamB1YQbcqanPL79IhMC6a27dX621X59hLu9h03jg7whFzv8a_pMrjEYPWKuVFi-nAPzZWIYRpysFq9GIG_ZnCecQ-QJnp4AeU42XiRpFkv7dNeRK12pUizWeINY8T400KdXtolx3RUMXVrBo7nBZdRZdBry6f3jkOk6Xf8plaptRma3h8qu_m_64MZ1MncmrvUxe8csS_Ywx-7Yn9l4_Jowxmgd96-6JyReeVFq98-rgUf3dZR2RgXtpyIeY111_3VrMM0iuoq6e1vIZBnRe1-zgu_lb48ueJu8aa17wXbwfdt_J9Rhc0w-g-ouJi3cmZurVW84W6gISUn0d3Rh316SgjvkBRRoJx2BIFucu2S9Fnsi3ukr8dIbe4rdrSceiytS8kRdctWcv_2lWusrRBdaM_40xc9j59wh58c_QdZ9a-8gOrYZRoqRcxpaJRcVb_k5beuXjt4ieVq_FvomjmVJwYRq-sghsJ5xFUW_FQZmkmwrSdhRq6MbOsFjqmghOSbYc8-n6exMlQ8t6owJnFbSIhauagoUpdoRh8Rl44nbVadlRF3j3kweMvRguOfujiYiWucs2Z29cwBf7SQeVpt6I208Uy2WwZYWzghroseRcvbyr4S3zJxIFzSUUS1_t_QgVO9-yQy40uOyrlQX3ut8gI65b46xIrRnshv2Ozfw6W-cddc_xmWVou8Sdu9MbuiW4qJZZbVBa9Y1rX8VYYv3WgJlR6ZQ2Ycothd1bw321" \
    //   -H "Content-Type: application/json" \
    //   -d '{
    //   "message": {
    //   "token": "ekHYj6i1e08AkGOONk62N1:APA91bFXL8LmEzibkaIX84cxp1c5TRh1UB2vJ8_TvqW6nTVIrin6zqnr8RiRyb4FczZi0BvdKcUSkkluUMLFg_BsKjOG_yPqK2imwSEEpO4dQlAAagaTXY4",
    //   "notification": {
    //   "title": "测试推送",
    //   "body": "这是一条 Firebase 测试通知"
    //   }
    //   }
    // }
    // '
  }
}
