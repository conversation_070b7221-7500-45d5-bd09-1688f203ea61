
import 'package:pinyin/pinyin.dart';

class PinyinUtils {
  /// 获取字符串的拼音首字母
  static String getFirstLetter(String text) {
    if (text.isEmpty) return '#';
    // 先尝试直接获取英文首字母
    String firstChar = text[0].toUpperCase();
    if (RegExp(r'[A-Z]').hasMatch(firstChar)) {
      return firstChar;
    }
    // 如果不是英文字母，则获取拼音首字母
    String pinyin = PinyinHelper.getFirstWordPinyin(text);
    if (pinyin.isEmpty) return '#';
    String letter = pinyin[0].toUpperCase();
    return RegExp(r'[A-Z]').hasMatch(letter) ? letter : '#';
  }

  /// 获取完整拼音
  static String getPinyin(String text) {
    return PinyinHelper.getPinyin(text);
  }
}