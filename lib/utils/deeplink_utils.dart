import 'package:and/module/chat/chat_page.dart';
import 'package:and/router/router.dart';
import 'package:and/utils/common_helper.dart';
import 'package:and/utils/http_utils.dart';
import 'package:and/utils/scan_utils.dart';
import 'package:get/get.dart';
import 'package:wukongimfluttersdk/type/const.dart';

class DeeplinkUtils {
  static String? visitorToken;

  static Future<bool> handleDeepLinkUrl(String? url) async {
    if (url == null || (url.isEmpty)) {
      return false;
    }
    var uri = Uri.tryParse(url);
    if (uri == null) return false;

    return handleDeepLinkUri(uri);
  }

  static Future<bool> handleDeepLinkUri(Uri uri) async {
    if (uri.host == "visitor" || uri.pathSegments.firstOrNull == "visitor") {
      visitorToken = uri.queryParameters["token"];
      if (CommonHelper.isLogin()) {
        //Do nothing
        // var channelID = uri.queryParameters["channelID"];
        // if (channelID != null) {
        //   ChatPage.open(
        //       channelID: channelID, channelType: WKChannelType.personal);
        // }
      } else {
        var uid = uri.queryParameters["uid"];
        if (uid != null) {
          var user = await HttpUtils.getChannel(uid, WKChannelType.personal);
          if (user?.category == "visitor") {
            Get.offAllNamed(RouteGet.signup);
          } else {
            Get.offAllNamed(RouteGet.login);
          }
        }
      }
      return true;
    } else if (uri.host == "open") {
      var qrcode = uri.queryParameters["qrcode"];
      if (qrcode != null) {
        ScanUtils.scanQrCode(qrcode);
      }
    }
    return false;
  }
}
