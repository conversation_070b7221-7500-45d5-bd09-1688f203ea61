import 'package:and/cache/cache_helper.dart';

class LiveKitConfig {
  static String host = CacheHelper.devTestMode
      ? 'wss://livekit.testaibox.com/livekit/ws'
      : 'wss://livekit.gleezy.top/ws';

  // 房间配置
  static const Duration roomTimeout = Duration(minutes: 10);
  static const Duration participantTimeout = Duration(minutes: 2);

  // 视频配置
  static const bool simulcast = true;
  static const int width = 640;
  static const int height = 480;
  static const int fps = 24;

  // 音频配置
  static const bool echoCancellation = true;
  static const bool noiseReduction = true;
  static const bool autoGainControl = true;
}