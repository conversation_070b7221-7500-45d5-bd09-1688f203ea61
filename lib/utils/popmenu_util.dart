import 'package:and/common/res/text_styles.dart';
import 'package:and/widget/popup_window.dart';
import 'package:flutter/material.dart';

class MenuItem {
  final String text;
  final String? icon;
  final VoidCallback? onTap;
  final Widget? widget;

  MenuItem({required this.text, this.icon, this.onTap, this.widget});
}

class PopMenuUtil {
  static Future showPopupMenu(
      BuildContext context, Offset offset, List<MenuItem> items) async {
    if (items.isEmpty) {
      return null;
    }
    final List<PopupMenuEntry> popupMenuItems = [];
    for (MenuItem item in items) {
      PopupMenuItem popupMenuItem = PopupMenuItem(
        // PopupMenuItem 的坑，默认为8，点击到边矩的地方会无反应
        padding: const EdgeInsets.all(0),
        onTap: item.onTap,
        child: Builder(builder: (context) {
          return item.widget ??
              Container(
                padding: const EdgeInsets.all(8.0),
                child: Row(children: [
                  if (item.icon != null)
                    Padding(
                      padding: EdgeInsets.only(right: 10),
                      child: Image.asset(
                        item.icon!,
                        width: 20,
                        height: 20,
                        color: Colors.black,
                      ),
                    ),
                  Text(item.text, style: TextStyles.fontSize15Normal)
                ]),
              );
        }),
      );

      popupMenuItems.add(popupMenuItem);
    }

    RenderBox? renderBox =
        Overlay.of(context).context.findRenderObject() as RenderBox;

    // 表示位置（在画面边缘会自动调整位置）
    final RelativeRect position = RelativeRect.fromRect(
      Rect.fromLTRB(
        offset.dx,
        offset.dy,
        offset.dx + 110, // 菜单显示位置X轴坐标
        offset.dy - 40, // 菜单显示位置Y轴坐标
      ),
      Offset.zero & renderBox.size,
    );

    return showMenu(
        context: context,
        position: position,
        items: popupMenuItems,
        useRootNavigator: true,
        requestFocus: true);
  }

  static Future showCustomPopupMenu(
      BuildContext context, GlobalKey key, List<MenuItem> items,
      {Widget? headerView}) async {
    final Widget popWidget = Column(
      children: [
        headerView ?? Container(),
        Container(
          padding: const EdgeInsets.all(0),
          margin: const EdgeInsets.symmetric(horizontal: 20),
          decoration: BoxDecoration(
              color: Colors.white, borderRadius: BorderRadius.circular(8)),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ...items.map((item) {
                return item.widget ??
                    GestureDetector(
                      behavior: HitTestBehavior.translucent,
                      onTap: () {
                        Navigator.pop(context);
                        item.onTap?.call();
                      },
                      child: Container(
                        padding: const EdgeInsets.all(8.0),
                        margin: const EdgeInsets.all(8.0),
                        child: Row(children: [
                          if (item.icon != null)
                            Padding(
                              padding: EdgeInsets.only(right: 10),
                              child: Image.asset(
                                item.icon!,
                                width: 20,
                                height: 20,
                                color: Colors.black,
                              ),
                            ),
                          Text(item.text,
                              style: TextStyles.fontSize15Normal)
                        ]),
                      ),
                    );
              })
            ],
          ),
        )
      ],
    );

    PopupWindow.showPopWindow(
      context,key,
      popWidget: popWidget
    );
  }
}
