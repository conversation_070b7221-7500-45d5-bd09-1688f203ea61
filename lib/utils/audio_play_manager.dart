import 'dart:async';

import 'package:audio_session/audio_session.dart';
import 'package:just_audio/just_audio.dart';

class AudioPlayManager {
  static final AudioPlayManager _instance = AudioPlayManager._internal();

  factory AudioPlayManager() => _instance;

  AudioPlayManager._internal();

  final AudioPlayer _player = AudioPlayer();
  String? _playId;
  final _progressController = StreamController<double>.broadcast();

  Stream<double> get progressStream => _progressController.stream;

  final _playStateController = StreamController<PlayerState>.broadcast();

  Stream<PlayerState> get playStream => _playStateController.stream;

  String? get currentPlayId => _playId;

  bool get isPlaying =>
      _player.playerState.playing &&
      _player.playerState.processingState != ProcessingState.completed;

  bool get isPause => _playId != null;

  Future<void> play(String filePath,
      {String? playId, bool followMute = false, bool isLoop = false}) async {
    print("Start play");
    await setupAudioSession(followMute: followMute);
    try {
      var currentPlayId = playId ?? filePath;
      // 如果当前正在播放其他音频，先停止
      if (isPlaying) {
        await pause();
        if (_playId == currentPlayId) {
          return;
        }
      }

      _playId = currentPlayId;
      if (filePath.startsWith("assets")) {
        await _player.setAsset(filePath);
      } else {
        await _player.setFilePath(filePath);
      }
      // 设置循环模式
      await _player.setLoopMode(isLoop ? LoopMode.one : LoopMode.off);

      _player.positionStream.listen((position) {
        if (_player.duration != null) {
          final progress =
              position.inMilliseconds / _player.duration!.inMilliseconds;
          _progressController.add(progress);
        }
      });

      _player.playerStateStream.listen((state) {
        _playStateController.add(state);
      });

      await _player.play();
    } catch (e) {
      print('Error playing audio: $e');
      _playId = null;
    }
  }

  Future<void> pause() async {
    print("Pause play");
    try {
      if (_player.playing) {
        await _player.pause();
      }
    } catch (e) {
      print('Error pausing audio: $e');
    }
  }

  Future<void> stop() async {
    print("Stop play");
    try {
      if (_player.playing) {
        await _player.stop();
      }
      _playId = null;
    } catch (e) {
      print('Error stop audio: $e');
    }
  }

  static Future<void> setupAudioSession({bool followMute = false}) async {
    try {
      final session = await AudioSession.instance;
      await session.configure(AudioSessionConfiguration(
        avAudioSessionCategory: followMute
            ? AVAudioSessionCategory.ambient
            : AVAudioSessionCategory.playback,
        // iOS设置修改
        avAudioSessionCategoryOptions:
            AVAudioSessionCategoryOptions.mixWithOthers,
        avAudioSessionMode: AVAudioSessionMode.spokenAudio,
        androidAudioAttributes: const AndroidAudioAttributes(
          contentType: AndroidAudioContentType.speech,
          usage: AndroidAudioUsage.voiceCommunication,
          flags: AndroidAudioFlags.audibilityEnforced,
        ),
        androidAudioFocusGainType: AndroidAudioFocusGainType.gain,
        androidWillPauseWhenDucked: false,
      ));
    } catch (e) {
      print('Error setting up audio session: $e');
    }
  }
}
