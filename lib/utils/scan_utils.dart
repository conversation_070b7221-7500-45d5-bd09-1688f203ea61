import 'package:and/app.dart';
import 'package:and/http/http_config.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/module/chat/chat_page.dart';
import 'package:and/module/group/join/group_join_page.dart';
import 'package:and/module/user/user_info_page.dart';
import 'package:and/utils/common_helper.dart';
import 'package:and/utils/http_utils.dart';
import 'package:mobile_scanner/mobile_scanner.dart';
import 'package:oktoast/oktoast.dart';
import 'package:wukongimfluttersdk/type/const.dart';

class ScanUtils {
  static Future<void> handle(Barcode content) async {
    var rawValue = content.rawValue;
    if (rawValue == null) {
      return;
    }
    scanQrCode(rawValue);
  }

  static Future<bool> scanQrCode(String content) async {
    var result = await HttpUtils.scanResult(content);
    if (result == null) {
      if (content.startsWith("http")) {
        CommonHelper.launchInWebView(content);
      } else if (globalContext != null) {
        showToast(globalContext!.l10n.noValidQrcode);
      }
      return false;
    }
    if (result.type == "userInfo" && result.data != null) {
      var uid = result.data!["uid"];
      var vercode = result.data!["vercode"];
      UserInfoPage.open(
          channelID: uid,
          channelType: WKChannelType.personal,
          vercode: vercode);
      return true;
    } else if (result.type == "group" && result.data != null) {
      var groupNo = result.data!["group_no"];
      ChatPage.open(channelID: groupNo, channelType: WKChannelType.group);
      return true;
    } else if (result.type == "webview" && result.data != null) {
      var url = result.data!["url"];
      var uri = Uri.parse(url);
      var path = uri.path;

      var queryParameters = uri.queryParameters;
      if (path.contains("join_group.html")) {
        var groupNo = queryParameters["group_no"];
        var authCode = queryParameters["auth_code"];
        if (groupNo == null || authCode == null) return false;
        GroupJoinPage.open(groupNo: groupNo, authCode: authCode);
      }
      return true;
    }
    return false;
  }
}
