import 'dart:async';
import 'package:connectivity_plus/connectivity_plus.dart';

class NetworkManager {
  // 标准单例
  static final NetworkManager instance = NetworkManager._internal();
  factory NetworkManager() => instance;
  NetworkManager._internal();

  final Connectivity _connectivity = Connectivity();
  StreamSubscription<List<ConnectivityResult>>? _subscription;
  ConnectivityResult _currentStatus = ConnectivityResult.none;

  // 监听网络状态变化的回调
  Function(ConnectivityResult)? onStatusChange;

  /// 获取当前网络状态（适配 List<ConnectivityResult>）
  Future<ConnectivityResult> getCurrentStatus() async {
    List<ConnectivityResult> results = await _connectivity.checkConnectivity();
    _currentStatus = _parseConnectivityResults(results);
    return _currentStatus;
  }

  /// 开始监听网络状态
  void startListening({Function(ConnectivityResult)? onChange}) {
    if (_subscription != null) return; // 避免重复监听
    onStatusChange = onChange;

    _subscription = _connectivity.onConnectivityChanged.listen((results) {
      _currentStatus = _parseConnectivityResults(results);
      onStatusChange?.call(_currentStatus);
      print("网络状态变化: $_currentStatus");
    });
  }

  /// 停止监听
  void stopListening() {
    _subscription?.cancel();
    _subscription = null;
  }

  /// 解析 List<ConnectivityResult>，返回一个最合适的状态
  ConnectivityResult _parseConnectivityResults(List<ConnectivityResult> results) {
    if (results.contains(ConnectivityResult.wifi)) {
      return ConnectivityResult.wifi;
    } else if (results.contains(ConnectivityResult.mobile)) {
      return ConnectivityResult.mobile;
    } else if (results.contains(ConnectivityResult.ethernet)) {
      return ConnectivityResult.ethernet;
    } else if (results.contains(ConnectivityResult.bluetooth)) {
      return ConnectivityResult.bluetooth;
    } else {
      return ConnectivityResult.none;
    }
  }
}