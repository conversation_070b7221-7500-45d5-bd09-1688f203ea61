import 'dart:io';

import 'package:flutter/services.dart';
import 'package:permission_handler/permission_handler.dart';

class PermissionCheckUtils {
  static const MethodChannel _channel = MethodChannel('permission_check');
  static const String foregroundServicePermission =
      "android.permission.FOREGROUND_SERVICE";
  static const String dataSyncPermission =
      "android.permission.FOREGROUND_SERVICE_DATA_SYNC";
  static const String microphonePermission =
      "android.permission.FOREGROUND_SERVICE_MICROPHONE";

  static Future<bool> checkForegroundMicrophoneServicePermission() async {
    final hasPermission = await checkPermission(microphonePermission);
    if (!hasPermission) {
      return false;
    }
    var status = await Permission.microphone.request();
    //Android 15 以上需要申请录音权限
    return status.isGranted;
  }

  static Future<bool> checkPermission(String permission) async {
    if (Platform.isIOS) {
      return false;
    }
    bool result = false;

    try {
      result = (await _channel.invokeMethod(
            'hasPermissionInManifest',
            {'permission': permission},
          )) ??
          false;
    } catch (e) {
      print("Permission check failed: $e");
    }
    return result;
  }
}
