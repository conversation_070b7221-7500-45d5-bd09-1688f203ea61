import 'dart:async';
import 'dart:math';
import 'dart:ui';

import 'package:and/common/res/colours.dart';
import 'package:and/common/res/text_styles.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/utils/dialog_utils.dart';
import 'package:and/utils/image_path.dart';
import 'package:and/widget/submit_button.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:slider_captcha/slider_captcha.dart';

class SmsUtil {
  /// 在发送验证码前调用此方法
  /// [context] 上下文
  /// 返回值: 验证是否通过
  static Future<bool> verifyCaptchaBeforeSendSms(BuildContext? context) async {
    if (context == null) return true;
    final controller = SliderController();

    bool verified = false;
    bool? result = await Get.dialog<bool>(
      AlertDialog(
        content: StatefulBuilder(
          builder: (context, setState) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  children: [
                    // 关闭按钮
                    GestureDetector(
                      onTap: () {
                        Navigator.of(context).pop();
                      },
                      child: Container(
                        padding: const EdgeInsets.all(4),
                        child: const Icon(
                          Icons.close,
                          size: 25,
                          color: Colors.black54,
                        ),
                      ),
                    ),
                    // 标题文本
                    Expanded(
                      child: Center(
                        child: Text(
                          context.l10n.dragSliderToCompletePuzzle,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                    // 为了平衡布局，右侧添加一个占位
                    const SizedBox(width: 28),
                  ],
                ),
                SizedBox(height: 10,),
                SliderCaptcha(
                  image: Image.asset(ImagePath.captcha,
                    fit: BoxFit.fitWidth,
                  ),
                  colorBar: DColor.primaryColor,
                  colorCaptChar: DColor.primaryColor,
                  controller: controller,
                  title: context.l10n.sliderVerify,
                  imageToBarPadding: 10,
                  borderImager: 5,
                  onConfirm: (value) async {
                    if (value) {
                      verified = true;
                      Navigator.of(context).pop();
                    } else {
                      controller.create.call();
                    }
                  },
                )
              ],
            );
          },
        ),
      ),
    );
    
    return verified;
  }
}

