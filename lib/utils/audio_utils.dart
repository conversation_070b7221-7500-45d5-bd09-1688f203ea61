import 'dart:io';
import 'dart:ui';

import 'package:and/l10n/l10n.dart';
import 'package:and/module/user/mine/my_setting_manager.dart';
import 'package:and/utils/audio_play_manager.dart';
import 'package:and/utils/dialog_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_mute/flutter_mute.dart';

// import 'package:flutter_vibrate/flutter_vibrate.dart';
import 'package:just_audio/just_audio.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:vibration/vibration.dart';
import 'package:wukongimfluttersdk/entity/msg.dart';
import 'package:wukongimfluttersdk/wkim.dart';

import '../app.dart';
import 'file_path.dart';

class AudioUtils {
  static Future<Duration?> getAudioDuration(String filePath) async {
    try {
      final player = AudioPlayer();
      await player.setFilePath(filePath);
      return player.duration;
    } catch (e) {
      print(e);
    }
    return null;
  }

  /// **提醒用户（如果需要）**
  static void remindUserIfNeed(WKMsg msg) async {
    // 获取应用状态
    AppLifecycleState? state = WidgetsBinding.instance.lifecycleState;

    if (state != AppLifecycleState.resumed) {
      // App 在后台，不播放铃声，因为本地通知会播
      return;
    }

    // 判断频道是否静音
    var channel = await WKIM.shared.channelManager
        .getChannel(msg.channelID, msg.channelType);
    var isMute = channel?.mute == 1;
    if (isMute) {
      return;
    }

    // if (OnlineStatusManager.instance.muteOfApp) {
    //   // App 全局静音，不做提醒
    //   return;
    // }

    if (MySettingManager.instance.newMsgNotice) {
      // 是否开启新消息提醒
      if (MySettingManager.instance.voiceOn && !await isMuted()) {
        AudioPlayManager().play(FilePath.new_msg, followMute: true);
      }
      if (MySettingManager.instance.shockOn) {
        vibrate();
      }
    }
  }

  /// **震动反馈**
  static void vibrate() async {
    if (await Vibration.hasVibrator()) {
      // 检查设备是否支持震动
      Vibration.vibrate();
    }
  }

  /// 检查Android设备是否静音状态
  static Future<bool> isMuted() async {
    if (Platform.isAndroid) {
      RingerMode ringerMode = await FlutterMute.getRingerMode();
      return ringerMode == RingerMode.Silent;
    }
    return false;
  }

  /// 请求麦克风权限
  static Future<bool> requestMicrophonePermission() async {
    final status = await Permission.audio.request();
    if (status != PermissionStatus.denied &&
        status != PermissionStatus.permanentlyDenied) {
      return true;
    }

    return false;
  }

  /// 需要麦克风权限引导设置弹窗
  static Future showRequestMicrophonePermission() async {
    DialogUtils.showCustomDialog(globalContext!,
        title: globalContext!.l10n.needVoicePermission,
        msg: globalContext!.l10n.enableAudioPermission,
        confirmText: globalContext!.l10n.globalSetting,
        onConfirm: () => openAppSettings());
  }
}
