import 'dart:async';
import 'dart:io';

import 'package:and/app.dart';
import 'package:and/common/extension/common_ext.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/utils/fileUtils.dart';
import 'package:background_downloader/background_downloader.dart';
import 'package:flutter/cupertino.dart';
import 'package:oktoast/oktoast.dart';
import 'package:path/path.dart' as p;
import 'package:path_provider/path_provider.dart';

class DownloadStatus {
  final String taskId;
  final TaskStatus status;
  final double? progress;

  DownloadStatus({
    required this.taskId,
    required this.status,
    this.progress,
  });
}

class DownloadManager {
  static final DownloadManager _instance = DownloadManager._internal();

  factory DownloadManager() => _instance;

  DownloadManager._internal();

  final FileDownloader _downloader = FileDownloader();

  final _progressController = StreamController<DownloadStatus>.broadcast();

  final Map<String, DownloadStatus> _statusMap = {};

  Stream<DownloadStatus> get progressStream {
    return _progressController.stream;
  }

  /// Attempt to get permissions if not already granted
  Future<void> getPermission(PermissionType permissionType) async {
    var status = await FileDownloader().permissions.status(permissionType);
    if (status != PermissionStatus.granted) {
      if (await FileDownloader()
          .permissions
          .shouldShowRationale(permissionType)) {
        debugPrint('Showing some rationale');
      }
      status = await FileDownloader().permissions.request(permissionType);
      debugPrint('Permission for $permissionType was $status');
    }
  }

  Future<void> configDownloadNotification() async{
    // Registering a callback and configure notifications
    await getPermission(PermissionType.notifications);
    FileDownloader()
        .configureNotificationForGroup(FileDownloader.defaultGroup,
            // For the main download button
            // which uses 'enqueue' and a default group
            running: const TaskNotification('Download {filename}',
                'File: {filename} - {progress} - speed {networkSpeed} and {timeRemaining} remaining'),
            complete: const TaskNotification(
                '{displayName} download {filename}', 'Download complete'),
            error: const TaskNotification(
                'Download {filename}', 'Download failed'),
            paused: const TaskNotification(
                'Download {filename}', 'Paused with metadata {metadata}'),
            canceled: const TaskNotification('Download {filename}', 'Canceled'),
            progressBar: true)
        .configureNotification(
            // for the 'Download & Open' dog picture
            // which uses 'download' which is not the .defaultGroup
            // but the .await group so won't use the above config
            complete: const TaskNotification(
                'Download {filename}', 'Download complete'),
            tapOpensFile: true); // dog can also open directly from tap
  }

  Future<String> download(String url,
      {String? localPath, String? taskId}) async {
    try {
      String downloadPath = localPath ?? "";
      if (downloadPath.isEmpty) {
        downloadPath = (await FileUtils.getDefaultFullPath(url));
      }

      //避免重复下载
      var downloadId = taskId ?? url;
      var downloadStatus = statusForId(downloadId);
      if (downloadStatus != null) {
        print("Download status: ${downloadStatus.status}");
        var isDownload = downloadStatus.status == TaskStatus.enqueued ||
            downloadStatus.status == TaskStatus.paused ||
            downloadStatus.status == TaskStatus.running;
        if (isDownload) {
          return downloadPath;
        }
      }
      File downloadFile = File(downloadPath);
      if (!downloadFile.parent.existsSync()) {
        if (Platform.isAndroid) {
          //Android 11以上需要申请权限
          await getExternalStorageDirectory();
        }
        await downloadFile.parent.create(recursive: true);
      }

      //await configDownloadNotification();
      final task = DownloadTask(
        taskId: downloadId,
        url: url,
        filename: downloadFile.name,
        directory: downloadFile.parent.path,
        baseDirectory: BaseDirectory.root,
        updates: Updates.statusAndProgress,
        allowPause: true,
        retries: 3,
        // requiresWiFi: true 某些机型判断NetworkType.CONNECTED有问题，导致无法启动下载， 如果设置为true，则使用NetworkType.UNMETERED，可以正常下载
      );

      await _downloader.download(
        task,
        onProgress: (progress) {
          DownloadStatus downloadStatus = DownloadStatus(
              taskId: downloadId,
              status: TaskStatus.running,
              progress: progress);
          _progressController.add(downloadStatus);
          _statusMap[downloadId] = downloadStatus;
        },
        onStatus: (status) async {
          print('Download status: $status');

          DownloadStatus downloadStatus =
              DownloadStatus(taskId: downloadId, status: status);
          _progressController.add(downloadStatus);

          if (status == TaskStatus.complete) {
            downloadStatus = DownloadStatus(
                taskId: downloadId, status: TaskStatus.complete, progress: 1.0);
          } else if (status == TaskStatus.failed) {
            downloadStatus = DownloadStatus(
                taskId: downloadId, status: TaskStatus.failed, progress: 0.0);
            var context = globalContext;
            if (context != null) {
              showToast(context.l10n.downloadFail);
            }
          }
          _progressController.add(downloadStatus);
          _statusMap[downloadId] = downloadStatus;
        },
      );
      return downloadPath;
    } catch (e) {
      print('Download failed: $e');
      rethrow;
    }
  }

  DownloadStatus? statusForId(String taskId) {
    return _statusMap[taskId];
  }
}
