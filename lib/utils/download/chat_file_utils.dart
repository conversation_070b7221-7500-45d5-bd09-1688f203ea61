import 'dart:io';

import 'package:and/utils/fileUtils.dart';

import 'download_manager.dart';

class ChatFileUtils {
  final String url;
  final String messageID;
  final int channelType;
  final String channelID;
  final int fileSize;
  final String folderName;

  String localPath;

  ChatFileUtils({
    required this.messageID,
    required this.channelType,
    required this.channelID,
    required this.url,
    required this.fileSize,
    required this.folderName,
    this.localPath = "",
  });

  final DownloadManager downloadManager = DownloadManager();

  Future<bool> viewOrDownload(
      {required Function() onViewFile, bool showErrorToast = false}) async {
    var uri = getFileUri();
    final file = File.fromUri(uri);
    if (file.existsSync()) {
      onViewFile();
      return true;
    } else {
      // Start download
      try {
        await downloadManager.download(url,
            localPath: file.path, taskId: messageID);
        return true;
      } catch (e) {
        print('Download failed: $e');
        return false;
      }
    }
  }

  Uri getUri() {
    if (url.isNotEmpty) {
      return Uri.parse(url);
    }
    return getFileUri();
  }

  Uri getFileUri() {
    if (localPath.isEmpty) {
      var name = url.split('/').last;
      localPath = FileUtils.getStoragePath(name,
          channelType: channelType,
          channelID: channelID,
          folderName: folderName,
          isFullPath: true);
    } else {
      if (localPath.startsWith("/") == false) {
        localPath = FileUtils.getFullStoragePath(localPath);
      }
    }
    return Uri.file(localPath);
  }

  bool isFileExist() {
    var uri = getFileUri();
    var file = File.fromUri(uri);
    return file.existsSync();
  }
}
