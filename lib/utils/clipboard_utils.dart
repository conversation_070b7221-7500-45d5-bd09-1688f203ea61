import 'dart:io';

import 'package:and/l10n/l10n.dart';
import 'package:flutter/services.dart';
import 'package:oktoast/oktoast.dart';

import '../app.dart';

class ClipboardUtils {
  static Future<void> clearClipboard() async {
    copyToClipboard("");
  }

  static Future<void> copyToClipboard(String text) async {
    try {
      await Clipboard.setData(ClipboardData(text: text));

      if (text.isNotEmpty) {
        if (globalContext == null) return;
        if (Platform.isIOS) {
          showToast(globalContext!.l10n.copied);
        }
      }
    } catch (e) {
      print('Error copy to clipboard: $e');
    }
  }
}
