import 'dart:math';

class FormatUtils {
  static String formatUnreadCount(int count) {
    String text = "$count";
    if (count > 99) {
      text = "99+";
    }
    return text;
  }

  static String formatFileSize(int bytes) {
    if (bytes <= 0) return "0 B";

    final units = ['B', 'KB', 'MB', 'GB', 'TB'];
    int digitGroups = (log(bytes) / log(1024)).floor();
    if (digitGroups >= units.length) digitGroups = units.length - 1;

    double size = bytes / pow(1024, digitGroups);
    String formattedSize = size < 10 ? size.toStringAsFixed(2) : size
        .toStringAsFixed(1);

    return "$formattedSize ${units[digitGroups]}";
  }
}
