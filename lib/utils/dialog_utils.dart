import 'dart:async';

import 'package:and/common/res/colours.dart';
import 'package:and/common/res/text_styles.dart';
import 'package:and/l10n/l10n.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

enum DrawerDirection { left, right, top, bottom }

class DialogUtils {
  static Future<String?> showInputDialog(
    BuildContext context, {
    required String title,
        String? defaultValue,
    String? summary,
    String? hint,
    String? submitText,
    String? cancelText,
  }) async {
    final TextEditingController controller = TextEditingController(text: defaultValue);
    return await showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title, style: TextStyles.fontSize18Medium),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (summary != null)
              Padding(
                padding: const EdgeInsets.only(bottom: 10),
                child: Text(summary,
                    style: TextStyles.fontSize15Normal.copyWith(
                        color: DColor.secondaryTextColor,
                        fontWeight: FontWeight.normal)),
              ),
            TextField(
              controller: controller,
              style: TextStyles.fontSize16Normal,
              decoration: InputDecoration(
                hintText: hint,
                hintStyle: TextStyles.fontSize16Normal
                    .copyWith(color: DColor.hintText),
                border: const OutlineInputBorder(),
              ),
              autofocus: true,
            )
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text(cancelText ?? context.l10n.globalCancel),
          ),
          TextButton(
            onPressed: () {
              var result = controller.text.trim();
              Get.back(result: result);
            },
            child: Text(submitText ?? context.l10n.globalConfirm),
          ),
        ],
      ),
    );
  }

  static Future<bool?> showAlertDialog(
    BuildContext context,
    String msg, {
    String? title,
    String? cancelText,
    String? confirmText,
    EdgeInsets? contentPadding,
    EdgeInsets? insetPadding,
    bool showCancel = false,
  }) {
    return showCustomDialog<bool>(
      context,
      title: title ?? context.l10n.alertDialogTitle,
      msg: msg,
      confirmText: confirmText,
      cancelText: cancelText,
      showCancel: showCancel,
      contentPadding: contentPadding,
      insetPadding: insetPadding,
      completer: Completer<bool?>(),
    );
  }

  static Future<bool?> showConfirmDialog(
    BuildContext context,
    String msg, {
    String? title,
    String? cancelText,
    String? confirmText,
    EdgeInsets? contentPadding,
    EdgeInsets? insetPadding,
    bool showCancel = true,
  }) {
    return showCustomDialog<bool>(
      context,
      title: title,
      msg: msg,
      confirmText: confirmText,
      cancelText: cancelText,
      showCancel: showCancel,
      contentPadding: contentPadding,
      insetPadding: insetPadding,
      completer: Completer<bool?>(),
    );
  }

  static OverlayEntry? _currentOverlay;

  static Future<T?> _showOverlayDialog<T>(
    BuildContext context,
    Widget dialog, {
    bool barrierDismissible = true,
    Completer<T?>? completer,
  }) {
    completer ??= Completer<T?>();
    final overlay = OverlayEntry(
      builder: (context) => Stack(
        children: [
          GestureDetector(
            onTap: barrierDismissible ? () {
              _removeCurrentOverlay();
              completer!.complete(null);
            } : null,
            child: Container(
              color: Colors.black.withAlpha(102),
            ),
          ),
          Center(child: dialog),
        ],
      ),
    );

    _removeCurrentOverlay();
    _currentOverlay = overlay;
    Overlay.of(context).insert(overlay);

    return completer.future;
  }

  static void _removeCurrentOverlay() {
    _currentOverlay?.remove();
    _currentOverlay = null;
  }

  static Future<T?> showCustomDialog<T>(BuildContext context,
      {String? title,
      String? msg,
      Widget? content,
      String? confirmText,
      String? cancelText,
      bool showCancel = true,
      bool showConfirm = true,
      bool barrierDismissible = true,
      bool fullscreen = false,
      Color? backgroundColor,
      EdgeInsets? contentPadding,
      EdgeInsets? insetPadding,
      double? borderRadius,
      Function()? onConfirm,
      Function()? onCancel,
        Completer<T?>? completer,}) async {
    completer ??= Completer<T?>();
    List<Widget>? actions = <Widget>[];
    if (showCancel) {
      actions.add(TextButton(
          onPressed: () {
            if (onCancel != null) {
              onCancel.call();
            } else {
              _removeCurrentOverlay();
              completer?.complete(null);
            }
          },
          child: Text(cancelText ?? context.l10n.globalCancel,
              style: TextStyles.fontSize16Normal)));
    }
    if (showConfirm) {
      actions.add(TextButton(
          onPressed: () {
            if (onConfirm != null) {
              onConfirm.call();
            } else {
              _removeCurrentOverlay();
              completer?.complete(true as T);
            }
          },
          child: Text(confirmText ?? context.l10n.globalOk,
              style: TextStyles.fontSize16Normal
                  .copyWith(color: DColor.primaryColor))));
    }
    if (!showCancel && !showConfirm) {
      actions = null;
    }

    final dialog = PopScope(
      canPop: barrierDismissible,
      child: Material(
        type: MaterialType.transparency,
        child: AlertDialog(
          insetPadding: insetPadding,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadius ?? 12.0),
          ),
          title: ((title != null) || (msg != null))
              ? Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Visibility(
                      visible: title?.isNotEmpty ?? false,
                      child: Text(title ?? "",
                          style: TextStyles.fontSize16SemiBold),
                    ),
                    Visibility(
                      visible: msg?.isNotEmpty ?? false,
                      child: Padding(
                        padding: const EdgeInsets.symmetric(vertical: 10),
                        child: Text(msg ?? "",
                            style: TextStyles.fontSize16Normal),
                      ),
                    ),
                  ],
                )
              : null,
          content: content != null
              ? SizedBox(
                  width: MediaQuery.of(context).size.width,
                  child: content)
              : null,
          clipBehavior: Clip.antiAlias,
          contentPadding: contentPadding,
          backgroundColor: backgroundColor,
          actions: actions,
        ),
      ),
    );

    return _showOverlayDialog<T>(
      context,
      dialog,
      barrierDismissible: barrierDismissible,
      completer: completer,
    );
  }

  static Future<T?> showDrawerDialog<T>(BuildContext context,
      {required Widget child,
      DrawerDirection direction = DrawerDirection.left}) async {
    return showGeneralDialog(
        context: context,
        barrierDismissible: true,
        barrierColor: Colors.black.withOpacity(0.4),
        barrierLabel: '',
        pageBuilder: (context, anim1, anim2) {
          if (direction == DrawerDirection.left) {
            return Align(alignment: Alignment.centerLeft, child: child);
          } else if (direction == DrawerDirection.right) {
            return Align(alignment: Alignment.centerRight, child: child);
          } else if (direction == DrawerDirection.top) {
            return Align(alignment: Alignment.topCenter, child: child);
          } else {
            return Align(alignment: Alignment.bottomCenter, child: child);
          }
        },
        transitionBuilder: (context, anim1, anim2, child) {
          if (direction == DrawerDirection.left) {
            return SlideTransition(
              position:
                  Tween(begin: const Offset(-1, 0), end: const Offset(0, 0))
                      .animate(anim1),
              child: child,
            );
          } else if (direction == DrawerDirection.right) {
            return SlideTransition(
              position:
                  Tween(begin: const Offset(1, 0), end: const Offset(0, 0))
                      .animate(anim1),
              child: child,
            );
          } else if (direction == DrawerDirection.top) {
            return SlideTransition(
              position:
                  Tween(begin: const Offset(0, -1), end: const Offset(0, 0))
                      .animate(anim1),
              child: child,
            );
          } else {
            return SlideTransition(
              position:
                  Tween(begin: const Offset(0, 1), end: const Offset(0, 0))
                      .animate(anim1),
              child: child,
            );
          }
        },
        transitionDuration: Duration(milliseconds: 300));
  }

  static Future<T?> showSelectBottomActionSheet<T>(
    BuildContext context, {
    String? title,
    String? subtitle,
    List<Widget>? children,
  }) {
    return showCupertinoModalPopup<T>(
        context: context,
        builder: (context) {
          var dialog = CupertinoActionSheet(
            title:
                (title?.isNotEmpty ?? false) || (subtitle?.isNotEmpty ?? false)
                    ? Column(children: [
                        Visibility(
                            visible: title?.isNotEmpty ?? false,
                            child: Text(title ?? "",
                                style: TextStyles.fontSize16Bold)),
                        Visibility(
                            visible: subtitle?.isNotEmpty ?? false,
                            child: Text(subtitle ?? "",
                                style: TextStyles.fontSize15Normal)),
                      ])
                    : null,
            cancelButton: CupertinoActionSheetAction(
              onPressed: () {
                Navigator.pop(context);
              },
              child: Text(context.l10n.globalCancel),
            ),
            actions: children,
          );
          return dialog;
        });
  }
}
