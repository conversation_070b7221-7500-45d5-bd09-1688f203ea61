import 'dart:async';
import 'dart:io';
import 'dart:ui' as ui;

import 'package:and/app.dart';
import 'package:and/common/res/text_styles.dart' show TextStyles;
import 'package:and/common/utils/easy_loading_helper.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/l10n/language_utils.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:dio/dio.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:image_gallery_saver_plus/image_gallery_saver_plus.dart';
import 'package:image_picker/image_picker.dart';
import 'package:oktoast/oktoast.dart';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:synchronized/synchronized.dart';
import 'package:wechat_assets_picker/wechat_assets_picker.dart';

import 'dialog_utils.dart';

class ImageUtils {
  static const double precisionErrorTolerance = 1e-10;

  static Future<ui.Image> getImageInfo(File file) async {
    Uint8List bytes = await file.readAsBytes();
    final Completer<ui.Image> completer = Completer();
    ui.decodeImageFromList(bytes, (ui.Image img) {
      return completer.complete(img);
    });
    return completer.future;
  }

  static int doubleCompare(double value, double other,
      {double precision = precisionErrorTolerance}) {
    if (value.isNaN || other.isNaN) {
      throw UnsupportedError('Compared with Infinity or NaN');
    }
    final double n = value - other;
    if (n.abs() < precision) {
      return 0;
    }
    return n < 0 ? -1 : 1;
  }

  static Future<File?> cropImage(BuildContext context,
      {required File file,
      CropAspectRatio? aspectRatio,
      CropAspectRatioPreset? aspectRatioPreset,
      bool? lockAspectRatio}) async {
    print(file.path);
    CroppedFile? croppedFile = await ImageCropper().cropImage(
      sourcePath: file.path,
      aspectRatio: CropAspectRatio(ratioX: 1, ratioY: 1),
      uiSettings: [
        AndroidUiSettings(
          toolbarTitle: context.l10n.cropImage,
          toolbarColor: Colors.black,
          toolbarWidgetColor: Colors.white,
          initAspectRatio: aspectRatioPreset,
          hideBottomControls: lockAspectRatio,
        ),
        IOSUiSettings(
          title: context.l10n.cropImage,
          resetAspectRatioEnabled: false,
          aspectRatioLockEnabled: true,
        ),
        WebUiSettings(
          context: context,
        ),
      ],
    );
    if (croppedFile != null) {
      return File(croppedFile.path);
    }
    return null;
  }

  static Future<File?> compressImage(String path, {int quality = 88}) async {
    Directory cache = await getTemporaryDirectory();
    var targetPath =
        "${cache.path}/${DateTime.now().millisecondsSinceEpoch}.jpg";

    var result = await FlutterImageCompress.compressAndGetFile(
      path,
      targetPath,
      quality: quality,
    );
    return File(result?.path ?? path);
  }

  static Future<File> compressImage2(File file,
      {int maxSize = 50 * 1024}) async {
    final int fileSize = await file.length();

    // 如果文件已经小于 maxSize，直接返回
    if (fileSize <= maxSize) {
      return file;
    }

    // 计算压缩质量（逐步减少）
    int quality = 90;
    while (quality > 10) {
      final Directory tempDir = await getTemporaryDirectory();
      final String targetPath =
          path.join(tempDir.path, "compressed_${path.basename(file.path)}");

      // 执行压缩
      final List<int>? result = await FlutterImageCompress.compressWithFile(
        file.absolute.path,
        quality: quality,
      );

      if (result != null) {
        final File compressedFile = File(targetPath)..writeAsBytesSync(result);

        // 检查压缩后大小
        if (compressedFile.lengthSync() <= maxSize) {
          return compressedFile;
        }
      }

      // 逐步降低质量
      quality -= 10;
    }

    // 最终仍然超出大小，返回原文件
    return file;
  }

  /// 检查并请求所需权限
  static Future<bool> _checkAndRequestPermission(ImageSource source) async {
    if (source == ImageSource.camera) {
      final hasPermission = await requestCameraPermission();
      if (!hasPermission) {
        showRequestCameraPermission();
      }
      return hasPermission;
    } else {
      final hasPermission = await requestImagePermission();
      if (!hasPermission) {
        showRequestImagePermission();
      }
      return hasPermission;
    }
  }

  static Future<File?> pickImage(BuildContext context,
      {ImageSource imageSource = ImageSource.gallery,
      CropAspectRatio? aspectRatio,
      bool? lockAspectRatio,
      bool compress = false,
      int quality = 88}) async {
    try {
      if (!await _checkAndRequestPermission(imageSource)) {
        return null;
      }
      var file = await ImagePicker().pickImage(source: imageSource);
      if (file == null) {
        return null;
      }
      var path = file.path;
      if (compress) {
        path = (await compressImage(path, quality: quality))?.path ?? file.path;
      }
      if (aspectRatio == null) {
        return File(path);
      } else {
        return cropImage(context,
            file: File(path),
            aspectRatio: aspectRatio,
            lockAspectRatio: lockAspectRatio);
      }
    } catch (e) {
      return null;
    }
  }

  static Future<List<File>> pickImages(BuildContext context,
      {int maxAssets = 1,
      bool compress = false,
      int quality = 88,
      RequestType requestType = RequestType.image}) async {
    try {
      if (!await _checkAndRequestPermission(ImageSource.gallery)) {
        return [];
      }

      Locale? locale = Localizations.maybeLocaleOf(context);
      var textDelegate = assetPickerTextDelegateFromLocale(locale);
      //修复繁体中文没有翻译的问题
      if (locale != null && locale.countryCode == "TW") {
        textDelegate = TraditionalChineseAssetPickerTextDelegate();
      }
      final List<AssetEntity> assetList = await AssetPicker.pickAssets(context,
              pickerConfig: AssetPickerConfig(
                maxAssets: maxAssets,
                textDelegate: textDelegate,
                requestType: requestType,
                limitedPermissionOverlayPredicate: (state) {
                  return false;
                },
              )) ??
          [];
      if (assetList.isEmpty) {
        return [];
      }
      List<File> files = [];
      for (int i = 0; i < assetList.length; i++) {
        var asset = assetList[i];
        String? path;
        if (Platform.isIOS) {
          path = (await asset.originFileWithSubtype)?.path;
        } else {
          path = (await asset.file)?.path;
        }
        if (path == null) {
          continue;
        }
        if (compress) {
          if (asset.type == AssetType.image && asset.mimeType != "image/gif" && !path.toLowerCase().endsWith(".gif")) {
            path = (await compressImage(path, quality: quality))?.path ?? path;
          }
        }
        files.add(File(path));
      }

      return files;
    } catch (e) {
      return [];
    }
  }

  static Future<List<AssetEntity>> pickAssets(BuildContext context,
      {List<AssetEntity>? selectedAssets,
      int maxAssets = 10,
      RequestType requestType = RequestType.image}) async {
    Locale? locale = LanguageUtils.getLocale();
    var textDelegate = assetPickerTextDelegateFromLocale(locale);
    if (!await requestImagePermission()) {
      showRequestImagePermission();
      return [];
    }
    final List<AssetEntity> assetList = await AssetPicker.pickAssets(context,
            pickerConfig: AssetPickerConfig(
              selectedAssets: selectedAssets,
              maxAssets: maxAssets,
              textDelegate: textDelegate,
              requestType: requestType,
              limitedPermissionOverlayPredicate: (state) {
                return false;
              },
            )) ??
        [];
    return assetList;
  }

  static Future<File?> selectPhoto(
    BuildContext context, {
    String? title,
    String? subtitle,
    CropAspectRatio? aspectRatio,
    bool? lockAspectRatio,
    bool compress = false,
  }) {
    return DialogUtils.showSelectBottomActionSheet(context,
        subtitle: subtitle,
        children: [
          CupertinoActionSheetAction(
            onPressed: () async {
              var file = await pickImage(context,
                  imageSource: ImageSource.camera,
                  aspectRatio: aspectRatio,
                  lockAspectRatio: lockAspectRatio,
                  compress: compress);
              Navigator.pop(context, file);
            },
            child: Text(context.l10n.imageTakePhoto,
                style: TextStyles.fontSize16Normal),
          ),
          CupertinoActionSheetAction(
            onPressed: () async {
              var file = await pickImage(context,
                  imageSource: ImageSource.gallery,
                  aspectRatio: aspectRatio,
                  lockAspectRatio: lockAspectRatio,
                  compress: compress);
              Navigator.pop(context, file);
            },
            child: Text(context.l10n.imageGallery,
                style: TextStyles.fontSize16Normal),
          )
        ]);
  }

  /// 请求保存到相册权限
  static Future<bool> requestStoragePermission() async {
    if (Platform.isAndroid) {
      final androidInfo = await DeviceInfoPlugin().androidInfo;
      if (androidInfo.version.sdkInt <= 32) {
        final storageStatus = await Permission.storage.request();
        if (storageStatus != PermissionStatus.denied &&
            storageStatus != PermissionStatus.permanentlyDenied) {
          return true;
        }
      } else {
        if (await requestImagePermission()) {
          return true;
        }
      }
    } else {
      if (await requestImagePermission()) {
        return true;
      }
    }

    return false;
  }

  // 添加权限请求锁
  static final Lock _permissionLock = Lock();

  /// 请求相册权限
  static Future<bool> requestImagePermission() async {
    return _permissionLock.synchronized(() async {
      try {
        final status = await Permission.photos.request();
        return status != PermissionStatus.denied &&
            status != PermissionStatus.permanentlyDenied;
      } catch (e) {
        return false;
      }
    });
  }

  /// 请求相机权限
  static Future<bool> requestCameraPermission() async {
    try {
      return _permissionLock.synchronized(() async {
        final status = await Permission.camera.request();
        return status != PermissionStatus.denied &&
            status != PermissionStatus.permanentlyDenied;
      });
    } catch (e) {
      return false;
    }
  }

  static Future showRequestImagePermission() async {
    showDialog(
      context: globalContext!,
      builder: (_) => AlertDialog(
        title: Text(globalContext!.l10n.needAlbumPermission),
        content: Text(globalContext!.l10n.openAlbumPermission),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(globalContext!),
            child: Text(globalContext!.l10n.globalCancel),
          ),
          TextButton(
            onPressed: () => openAppSettings(), // 打开系统设置
            child: Text(globalContext!.l10n.globalSetting),
          ),
        ],
      ),
    );
  }

  static Future showRequestCameraPermission() async {
    DialogUtils.showCustomDialog(globalContext!,
        title: globalContext!.l10n.needCameraPermission,
        msg: globalContext!.l10n.enableCameraPermission,
        confirmText: globalContext!.l10n.globalSetting,
        onConfirm: () => openAppSettings());
  }

  static Future saveToAlbum(File file) async {
    if (!await requestStoragePermission()) {
      showRequestImagePermission();
      return;
    }
    EasyLoadingHelper.show(onAction: () async {
      var result = await ImageGallerySaverPlus.saveFile(file.path);
      bool? savedFile = result["isSuccess"];
      showToast(globalContext!.l10n.saveToGallerySuccess);
      print(savedFile);
    });
  }

  static saveTemp(String urlPath) async {
    var path = "${(await getTemporaryDirectory()).path}/tmp.png";
    await Dio().download(urlPath, path);
    return path;
  }

  static bool isSvgImage(String path) {
    return path.endsWith(".svg");
  }
}
