import 'dart:io';

import 'package:advertising_id/advertising_id.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/services.dart';
import 'package:flutter_device_id/flutter_device_id.dart';
import 'package:ulid/ulid.dart';

class DeviceUtils {
  static Future<String?> getAdvertisingId() async {
    String? advertisingId;
// Platform messages may fail, so we use a try/catch PlatformException.
    try {
      advertisingId = await AdvertisingId.id(true);
    } on PlatformException {
      advertisingId = null;
    }
    return advertisingId;
  }

  static Future<String> getDeviceId() async {
    if (Platform.isAndroid) {
      String? advertisingId = await getAdvertisingId();
      if (advertisingId != null) {
        return advertisingId;
      }
    }
    String? deviceId = await FlutterDeviceId().getDeviceId();
    return (deviceId ?? Ulid().toUuid()).replaceAll("-", "");
  }

  static Future<String> getDeviceName() async {
    final deviceInfo = DeviceInfoPlugin();
    String manufacturer;
    if (Platform.isIOS) {
      manufacturer = 'Apple';
    } else {
      manufacturer = (await deviceInfo.androidInfo).manufacturer;
    }

    String model = await getDeviceModel();
    if (model.startsWith(manufacturer)) {
      return _capitalize(model);
    } else {
      return '${_capitalize(manufacturer)} $model';
    }
  }

  static Future<String> getDeviceModel() async {
    final deviceInfo = DeviceInfoPlugin();
    if (Platform.isIOS) {
      return (await deviceInfo.iosInfo).model;
    } else {
      return (await deviceInfo.androidInfo).model;
    }
  }

  static Future<String> getDeviceBrand() async {
    final deviceInfo = DeviceInfoPlugin();
    if (Platform.isIOS) {
      return "Apple";
    } else {
      return (await deviceInfo.androidInfo).brand;
    }
  }

  static Future<String> getDeviceManufacturer() async {
    final deviceInfo = DeviceInfoPlugin();
    if (Platform.isIOS) {
      return "Apple Inc.";
    } else {
      return (await deviceInfo.androidInfo).manufacturer;
    }
  }

  static Future<String> getDeviceOSVersion() async {
    final deviceInfo = DeviceInfoPlugin();
    if (Platform.isIOS) {
      return (await deviceInfo.iosInfo).systemVersion;
    } else {
      return (await deviceInfo.androidInfo).version.release;
    }
  }

  static Future<String> getDeviceOSName() async {
    if (Platform.isIOS) {
      return "iOS";
    } else {
      return "Android";
    }
  }

  static Future<String> getDeviceCpuAbi() async {
    final deviceInfo = DeviceInfoPlugin();
    if (Platform.isIOS) {
      return (await deviceInfo.iosInfo).utsname.machine;
    } else {
      return (await deviceInfo.androidInfo).supportedAbis.first;
    }
  }

  static String _capitalize(String s) {
    if (s.isEmpty) return s;
    return s[0].toUpperCase() + s.substring(1).toLowerCase();
  }
}
