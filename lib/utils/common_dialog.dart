import 'dart:math';

import 'package:and/widget/app_bar_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'image_path.dart';

Widget CommonDialogContent(
    {String? title,
    BoxConstraints? constraints,
    bool showClose = false,
    required Widget child}) {
  return ClipRRect(
      borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20), topRight: Radius.circular(20)),
      child: Safe<PERSON><PERSON>(
          child: Container(
        color: Colors.white,
        constraints: constraints,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            (title?.isNotEmpty ?? false)
                ? DialogBarWidget(title ?? "", showClose: showClose)
                : const SizedBox(),
            Flexible(flex: 1, child: child)
          ],
        ),
      )));
}

Widget CommonBottomSheetContentForHeight(BuildContext context,
    {String? title,
    required num percent,
    bool showBack = false,
    bool showClose = false,
    Color? backgroundColor,
    required Widget child}) {
  return CommonBottomSheetContent(context,
      title: title,
      showClose: showClose,
      showBack: showBack,
      backgroundColor: backgroundColor,
      constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * min(0.85, percent)),
      child: child);
}

Widget CommonBottomSheetContent(BuildContext context,
    {String? title,
    BoxConstraints? constraints,
    bool showBack = false,
    bool showClose = false,
    Color? backgroundColor,
    required Widget child}) {
  return ClipRRect(
      borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20), topRight: Radius.circular(20)),
      child: SafeArea(
          child: Container(
        color: backgroundColor ?? Colors.white,
        constraints: constraints ??
            BoxConstraints(
                maxHeight: MediaQuery.of(context).size.height * 0.85),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ((title?.isNotEmpty ?? false) || showClose)
                ? AppBarWidget(context, title ?? "",
                    backgroundColor: backgroundColor ?? Colors.white,
                    actions: [
                      Visibility(
                          visible: showClose,
                          child: Padding(
                            padding: const EdgeInsets.only(right: 15),
                            child: IconButton(
                                onPressed: () {
                                  Get.back();
                                },
                                icon: Image.asset(ImagePath.ic_close,
                                    height: 20)),
                          ))
                    ],
                    showBack: showBack)
                : const SizedBox(),
            Flexible(flex: 1, child: child)
          ],
        ),
      )));
}
