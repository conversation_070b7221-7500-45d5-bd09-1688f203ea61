import 'dart:io';

import 'package:and/cache/cache_helper.dart';
import 'package:and/http/my_http.dart';
import 'package:and/io/common.dart';
import 'package:and/model/request/device_report_request.dart';
import 'package:android_play_install_referrer/android_play_install_referrer.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'device_utils.dart';

class InstallReferrerUtils {
  // Platform messages are asynchronous, so we initialize in an async method.
  static Future<void> initReferrerDetails() async {
    if (CacheHelper.reportReferrer) {
      return;
    }
    try {
      ReferrerDetails? referrerDetails;
      if (Platform.isAndroid) {
        referrerDetails = await AndroidPlayInstallReferrer.installReferrer;
      }

      final mediaData = MediaQueryData.fromView(WidgetsBinding.instance.window);
      await CommonApi(MyHttp.dio).deviceReport(
        body: DeviceReportRequest(
            referrerUrl: referrerDetails?.installReferrer ?? '',
            referrerClickTime:
                referrerDetails?.referrerClickTimestampSeconds ?? 0,
            appInstallTime: referrerDetails?.installBeginTimestampSeconds ?? 0,
            instantExperienceLaunched:
                (referrerDetails?.googlePlayInstantParam ?? false) ? 1 : 0,
            deviceId: await DeviceUtils.getDeviceId(),
            deviceModel: await DeviceUtils.getDeviceModel(),
            deviceBrand: await DeviceUtils.getDeviceBrand(),
            deviceManufacturer: await DeviceUtils.getDeviceManufacturer(),
            osName: await DeviceUtils.getDeviceOSName(),
            osVersion: await DeviceUtils.getDeviceOSVersion(),
            screenWidth: mediaData.size.width.toString(),
            screenHeight: mediaData.size.height.toString(),
            screenDensity: mediaData.devicePixelRatio.toString(),
            cpuAbi: await DeviceUtils.getDeviceCpuAbi()),
      );
      CacheHelper.saveReportReferrer(true);
    } catch (e) {
      e.printError();
    }
  }
}
