import 'dart:io';

import 'package:and/cache/cache_helper.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/module/chat/chat_send_utils.dart';
import 'package:and/module/search/globalSearch/global_search_page.dart';
import 'package:and/module/search/globalSearch/global_search_logic.dart';
import 'package:and/module/share/share_target_page.dart';
import 'package:oktoast/oktoast.dart';
import 'package:receive_sharing_intent/receive_sharing_intent.dart';
import 'package:wukongimfluttersdk/entity/channel.dart';
import 'package:flutter/foundation.dart';
import 'package:wukongimfluttersdk/type/const.dart';

import '../app.dart';

/// 分享工具类，用于处理外部应用分享过来的文件
class ShareUtils {
  /// 私有构造函数，防止外部实例化
  ShareUtils._();

  /// 初始化分享监听
  static void init() {
    // 处理应用启动时的分享内容
    handleInitialSharing();
  }

  /// 处理应用启动时的分享内容
  static Future<void> handleInitialSharing() async {
    try {
      ReceiveSharingIntent.instance.getInitialMedia().then((value) {
        debugPrint("初始分享内容: ${value.length} 个文件");
        if (value.isNotEmpty) {
          // 延迟执行，确保应用已完全启动
          Future.delayed(const Duration(milliseconds: 1000), () {
            handleSharedFiles(value);
          });
        }
      });

      // 监听应用运行时的分享内容
      ReceiveSharingIntent.instance.getMediaStream().listen((sharedData) {
        debugPrint("收到分享内容: ${sharedData.length} 个文件");
        if (sharedData.isNotEmpty) {
          handleSharedFiles(sharedData);
        }
      }, onError: (error) {
        debugPrint("分享内容接收错误: $error");
      });
    } catch (e) {
      debugPrint("分享初始化错误: $e");
    }
  }

  /// 处理分享的文件
  static void handleSharedFiles(List<SharedMediaFile> sharedFiles) async {
    if (sharedFiles.isEmpty) return;
    
    debugPrint("处理分享文件: ${sharedFiles.length} 个文件");
    ShareTargetPage.open(sharedFiles);
    // 重置分享状态，避免重复处理
    ReceiveSharingIntent.instance.reset();
  }

  /// 发送文件到指定频道
  static Future<void> sendFilesToChannels(List<SharedMediaFile> sharedFiles, List<WKChannel> channels) async {
    if (channels.isEmpty || sharedFiles.isEmpty) return;

    final channel = channels.first;

    for (var sharedFile in sharedFiles) {
      File file = File(sharedFile.path);
      if (await file.exists()) {
        ChatSendUtils sendUtils = ChatSendUtils(
          channelID: channel.channelID,
          channelType: channel.channelType
        );

        // 根据文件类型选择发送方式
        final mimeType = sharedFile.mimeType;

        if (mimeType != null &&
            (mimeType.startsWith("image/") || mimeType.startsWith("video/"))) {
          sendUtils.sendMediaMessage(file);
        } else {
          sendUtils.sendFileMessage(file);
        }
      }
    }
  }
}