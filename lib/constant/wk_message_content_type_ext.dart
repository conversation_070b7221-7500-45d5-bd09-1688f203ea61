import 'package:wukongimfluttersdk/type/const.dart';

class WkMessageContentTypeExt {
  //转发消息
  static const multiForward = 11;

  //矢量贴图
  static const int vectorSticker = 12;

  //emoji 贴图
  static const int emojiSticker = 13;

  // content 格式错误
  static const int contentFormatError = 97;

  // signal 解密失败
  static const int signalDecryptError = 98;

  //内部消息，无需存储到数据库
  static const int insideMsg = 99;

  //系统消息
  static const int systemMsg = 0;

  //以下是新消息提示分割线
  static const int msgPromptNewMsg = -1;

  //消息时间
  static const int msgPromptTime = -2;

  //未知消息
  static const int unknown_msg = -3;

  //正在输入
  static const int typing = -4;

  //撤回消息
  static const int revoke = -5;

  //加载中
  static const int loading = -6;

  //本地显示的群会议音视频
  static const int videoCallGroup = -7;

  // 非好友
  static const int noRelation = -9;

  // 敏感词提醒
  static const int sensitiveWordsTips = -10;
  static const int emptyView = -12;
  static const int spanEmptyView = -13;

  // 音视频通话
  static const int inviteGroupCall= -101;//发起群通话
  static const int cancelCall = -102; //发起人取消通话
  static const int endCall = -103; //结束通话
  static const int callTimeOut = -104; //邀请通话超时
  static const int rejectCall = -105; //拒绝通话
  static const int missedCall = -106; //未接来电

  static const int sticker = 12; //表情

  // 富文本
  static const int richText = 14;

  //群聊加人
  static const int addGroupMembersMsg = 1002;

  //群聊减人
  static const int removeGroupMembersMsg = 1003;

  //群系统消息
  static const int groupSystemInfo = 1005;

  //撤回消息
  static const int withdrawSystemInfo = 1006;

  //设置新的管理员
  static const int setNewGroupAdmin = 1008;

  //审核群成员
  static const int approveGroupMember = 1009;

  //截屏消息
  static const int screenshot = 1014;

  static bool isSystemMsg(int type) {
    return type >= 1000 && type <= 2000;
  }

  static bool isLocalMsg(int type) {
    return type <= 0;
  }

  static bool isSupportNotification(int type) {
    return type >= WkMessageContentType.text && type <= richText;
  }

  /// 是否为通话消息
  static bool isCallMsg(int type) {
    return type >= missedCall && type <= inviteGroupCall;
  }
}
