class WKSystemAccount {
  static const String systemTeam = "u_10000";
  static const String systemFileHelper = "fileHelper";
  static const String systemTeamShortNo = "10000";
  static const String systemFileHelperShortNo = "20000";

  static const String accountCategorySystem = "system";
  static const String accountCategoryVisitor = "visitor";
  static const String accountCategoryCustomerService = "customerService";
  static const String channelCategoryOrganization = "organization";
  static const String channelCategoryDepartment = "department";

  static bool isSystemAccount(String channelID) {
    return channelID == systemTeam || channelID == systemFileHelper;
  }

}