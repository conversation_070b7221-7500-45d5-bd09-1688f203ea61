import 'package:and/common/res/colours.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/utils/image_path.dart';
import 'package:and/widget/input_form_widget.dart';
import 'package:flutter/material.dart';

class SearchInputWidget extends StatefulWidget {
  final String? keyword;
  final bool showImage;
  final bool showSearch;
  final bool autofocus;
  final String? searchPlaceHolder;
  final Color? backgroundColor;
  final ValueChanged<String>? onKeyChanged;
  final ValueChanged<String> onFieldSubmitted;
  final Function(String)? onImageTap;
  final GestureTapCallback? onTap;
  final EdgeInsets? padding;

  const SearchInputWidget(
      {super.key,
      this.keyword,
      this.searchPlaceHolder,
        this.onKeyChanged,
      required this.onFieldSubmitted,
      this.onTap,
      this.autofocus = false,
      this.showImage = true,
      this.showSearch = false,
      this.backgroundColor,
      this.padding,
      this.onImageTap});

  @override
  State<StatefulWidget> createState() {
    return _SearchInputWidgetState();
  }
}

class _SearchInputWidgetState extends State<SearchInputWidget> {
  final _searchTextController = TextEditingController();

  final GlobalKey<FormState> _searchKey = GlobalKey<FormState>();

  @override
  void dispose() {
    _searchTextController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
        padding: widget.padding ?? EdgeInsets.zero,
        child: _buildSearchInput(context));
  }

  @override
  void didUpdateWidget(SearchInputWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.keyword != widget.keyword) {
      _searchTextController.text = widget.keyword ?? "";
    }
  }

  Widget _buildSearchInput(BuildContext context) {
    return InputTextForm(
        formKey: _searchKey,
        hint: widget.searchPlaceHolder ?? context.l10n.search,
        value: widget.keyword,
        autofocus: widget.autofocus,
        keyboardType: TextInputType.text,
        controller: _searchTextController,
        backgroundColor: widget.backgroundColor,
        textInputAction: TextInputAction.search,
        contentPadding: const EdgeInsets.all(10),
        maxLines: 1,
        onFieldSubmitted: (value) {
          _startSearchKeyword(value);
        },
        onChanged: (value) {
          widget.onKeyChanged?.call(value);
          setState(() {});
        },
        onTap: widget.onTap,
        prefixIcon: Padding(
          padding: const EdgeInsets.only(left: 15, right: 10),
          child: _buildSearchAction(),
        ));
  }

  Widget _buildSearchAction() {
    return InkWell(
      onTap: () {
        _startSearchKeyword(_searchTextController.text);
      },
      child: Image.asset(
        ImagePath.ic_search,
        height: 25,
        fit: BoxFit.fitHeight,
        color: DColor.secondaryTextColor,
      ),
    );
  }

  void _startSearchKeyword(String keyword) {
    widget.onFieldSubmitted.call(keyword);
  }
}
