import 'package:and/l10n/l10n.dart';
import 'package:and/module/search/searchuser/widget/ui_search_user_item.dart';
import 'package:and/module/search/widget/search_input_widget.dart';
import 'package:and/widget/refresh/load_state.dart';
import 'package:and/widget/refresh/refresh_page.dart';
import 'package:and/widget/refresh/refresh_widget.dart';
import 'package:and/widget/submit_button.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'search_user_logic.dart';

class SearchUserPage extends StatefulWidget {
  const SearchUserPage({super.key});

  @override
  State<StatefulWidget> createState() {
    return _SearchUserPageState();
  }
}

class _SearchUserPageState extends RefreshState<SearchUserPage> {
  final logic = Get.find<SearchUserLogic>();
  late final item = logic.data;

  @override
  Rx<FitLoadStatus> getLoadState() {
    return logic.loadStatus;
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Scaffold(
        appBar: AppBar(
          title: _buildSearchInput(),
          titleSpacing: 0,
          actions: [
            Padding(
                padding: EdgeInsets.only(left: 10, right: 15),
                child: _buildSearchButton())
          ],
        ),
        body: Obx(() => _buildRefreshList()));
  }

  Widget _buildSearchInput() {
    return SearchInputWidget(
      searchPlaceHolder: context.l10n.search,
      showImage: false,
      autofocus: true,
      onKeyChanged: (key) {
        logic.onKeyChanged(key);
      },
      onFieldSubmitted: (key) {
        logic.search(key: key);
      },
    );
  }

  Widget _buildSearchButton() {
    return SubmitButton(
        onPressed: () {
          logic.search();
        },
        text: context.l10n.search);
  }

  Widget _buildRefreshList() {
    return buildRefreshWidget(
      refreshController: refreshController,
      loadStatus: logic.loadStatus.value,
      refreshOnStart: false,
      hasData: item.value != null,
      builder: (physics) => _buildList(physics),
      onRefresh: () {
        logic.refreshData();
      }
    );
  }

  Widget _buildList(ScrollPhysics? physics) {
    var searchUser = item.value;
    if (searchUser == null) {
      return Container();
    }
    return CustomScrollView(
      physics: physics,
      slivers: [
        SliverPadding(
            padding: const EdgeInsets.symmetric(vertical: 15),
            sliver: SliverList(
              delegate:
                  SliverChildBuilderDelegate((BuildContext context, int index) {
                return UiSearchUserItem(msg: searchUser);
              }, childCount: 1),
            ))
      ],
    );
  }
}
