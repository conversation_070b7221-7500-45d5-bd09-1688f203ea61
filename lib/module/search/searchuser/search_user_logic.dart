import 'package:and/common/utils/easy_loading_helper.dart';
import 'package:and/http/my_http.dart';
import 'package:and/io/user.dart';
import 'package:and/model/search_user.dart';
import 'package:and/module/common/controller/simple_controller.dart';
import 'package:get/get.dart';

class SearchUserLogic extends SimpleController<SearchUser?> {
  final searchKey = ''.obs;

  SearchUserLogic();

  @override
  Future<SearchUser?> loadData() async {
    if (searchKey.value.isEmpty) return null;
    try {
      var searchUser = await UserApi(MyHttp.dio).searchUser(searchKey.value);
      if (searchUser.exist == 0) {
        return null;
      }
      return searchUser;
    } catch (e) {
      print(e);
      return null;
    }
  }

  void onKeyChanged(String key) async {
    searchKey.value = key;
  }

  void search({String? key}) async {
    if (key != null) {
      searchKey.value = key;
    }
    data.value = null;
    EasyLoadingHelper.show(onAction: () async {
      await refreshData();
    });
  }
}
