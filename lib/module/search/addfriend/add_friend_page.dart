import 'package:and/cache/cache_helper.dart';
import 'package:and/common/res/colours.dart';
import 'package:and/common/res/text_styles.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/router/router.dart';
import 'package:and/utils/common_helper.dart';
import 'package:and/utils/image_path.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class AddFriendPage extends StatefulWidget {
  const AddFriendPage({super.key});

  @override
  State<StatefulWidget> createState() {
    return _AddFriendPageState();
  }
}

class _AddFriendPageState extends State<AddFriendPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(title: Text(context.l10n.addFriend)),
        body: _buildContent());
  }

  Widget _buildContent() {
    return Column(
      children: [_buildSearchBar(), _buildShorNo(), _buildQrcode()],
    );
  }

  Widget _buildSearchBar() {
    return GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: () {
          Get.toNamed(RouteGet.searchUser);
        },
        child: Container(
          color: Colors.white,
          padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 15),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.search),
              const SizedBox(width: 10),
              Text(context.l10n.search, style: TextStyles.fontSize15Normal)
            ],
          ),
        ));
  }

  Widget _buildShorNo() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 15),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
              context.l10n.myAppId(
                  context.l10n.appName, CacheHelper.userProfile?.shortNo ?? ''),
              style: TextStyles.fontSize15Normal),
          const SizedBox(width: 10),
          Image.asset(ImagePath.ic_qrcode,
              height: 20, color: DColor.primaryTextColor),
        ],
      ),
    );
  }

  Widget _buildQrcode() {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        CommonHelper.scanContact();
      },
      child: Container(
        color: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 15),
        child: Row(
          children: [
            Image.asset(ImagePath.ic_scan, height: 30),
            const SizedBox(width: 10),
            Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(context.l10n.scan, style: TextStyles.fontSize16Normal),
                Text(context.l10n.scanUserQr,
                    style: TextStyles.fontSize15Normal
                        .copyWith(color: DColor.secondaryTextColor)),
              ],
            ),
            Spacer(),
            Padding(
              padding: EdgeInsets.only(left: 10),
              child: Icon(Icons.arrow_forward_ios,
                  size: 16, color: DColor.secondaryTextColor),
            ),
          ],
        ),
      ),
    );
  }
}
