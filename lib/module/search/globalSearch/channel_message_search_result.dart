import 'package:and/common/res/colours.dart';
import 'package:and/module/search/globalSearch/global_search_logic.dart';
import 'package:and/module/search/globalSearch/global_search_page.dart';
import 'package:and/module/search/globalSearch/model/global_search_view_model.dart';
import 'package:and/module/search/globalSearch/widget/search_channel_message_item.dart';
import 'package:and/module/search/globalSearch/widget/search_contact_item.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ChannelMessageSearchResult extends StatefulWidget {
  const ChannelMessageSearchResult({super.key});

  @override
  State<StatefulWidget> createState() {
    return _ChannelMessageSearchResultState();
  }
}

class _ChannelMessageSearchResultState
    extends State<ChannelMessageSearchResult> {
  late GlobalSearchArgument argument = GlobalSearchArgument.fromGet();
  late GlobalSearchLogic logic =
      Get.find<GlobalSearchLogic>(tag: argument.getTag());
  late RxList<GlobalSearchResultModel> results = logic.results;

  @override
  void initState() {
    super.initState();

    if (argument.searchKey != null) {
      logic.startSearch(argument.searchKey!);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        titleSpacing: 0,
        title: Text(""),
      ),
      body: Column(
        children: [
          _buildConversation(),
          SizedBox(height: 10),
          Expanded(child: Obx(() => _buildSearchResult())),
        ],
      ),
    );
  }

  Widget _buildConversation() {
    return Row(
      children: [
        Expanded(
          child: UiSearchContactItem(
            channel: argument.channel!,
            keyword: argument.searchKey,
            onTap: (argument.onSelectedChannel == null)
                ? null
                : (channel) {
              argument.onSelectedChannel?.call([channel]);
            },
          ),
        ),
        Container(
          color: Colors.white,
          height: 68,
          child: Padding(
            padding: EdgeInsets.only(left: 10, right: 16),
            child: Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: DColor.secondaryTextColor,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSearchResult() {
    return ListView.builder(
      itemCount: results.length,
      itemBuilder: (context, index) {
        final item = results[index];
        return _buildSearchResultItem(item);
      },
    );
  }

  Widget? _buildSearchResultItem(GlobalSearchResultModel item) {
    switch (item.viewType) {
      case GlobalSearchViewType.channelMsg:
        if (item.msg != null) {
          return UiSearchMessageItem(
            msg: item.msg!,
            keyword: item.keyword,
            onTap: (argument.onSelectedChannel == null)
                ? null
                : (channel) {
                    argument.onSelectedChannel?.call([channel]);
                  },
          );
        }
        break;
      default:
        break;
    }
    return Container();
  }

  @override
  void dispose() {
    super.dispose();
  }
}
