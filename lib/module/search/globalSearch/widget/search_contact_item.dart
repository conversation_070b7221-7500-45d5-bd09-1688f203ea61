import 'package:and/common/res/colours.dart';
import 'package:and/common/res/text_styles.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/model/extension/wk_channel_ext.dart';
import 'package:and/module/chat/chat_page.dart';
import 'package:and/utils/common_helper.dart';
import 'package:and/utils/time_utils.dart';
import 'package:and/widget/avatar_widget.dart';
import 'package:and/widget/highlighted_text.dart';
import 'package:flutter/material.dart';
import 'package:wukongimfluttersdk/entity/channel.dart';
import 'package:wukongimfluttersdk/entity/msg.dart';
import 'package:wukongimfluttersdk/type/const.dart';
import 'package:wukongimfluttersdk/wkim.dart';

class UiSearchContactItem extends StatefulWidget {
  final WKChannel channel;
  final String? keyword;
  final String? containMemberName;
  final Function(WKChannel)? onTap;

  const UiSearchContactItem(
      {super.key,
      required this.channel,
      this.keyword,
      this.containMemberName,
      this.onTap});

  @override
  State<StatefulWidget> createState() {
    return _UiSearchContactItemState();
  }
}

class _UiSearchContactItemState extends State<UiSearchContactItem> {
  late Future<String?> _channelNameFuture;

  @override
  void initState() {
    super.initState();
    _channelNameFuture = getChannelName();
  }

  @override
  void didUpdateWidget(covariant UiSearchContactItem oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.channel != oldWidget.channel) {
      _channelNameFuture = getChannelName();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      padding: const EdgeInsets.all(10),
      child: ListTile(
        onTap: () {
          if (widget.onTap != null) {
            widget.onTap?.call(widget.channel);
          } else {
            ChatPage.open(
                channelID: widget.channel.channelID,
                channelType: widget.channel.channelType);
          }
        },
        contentPadding: EdgeInsets.only(left: 8, right: 12),
        leading: Padding(
          padding: EdgeInsets.only(right: 12),
          child: AvatarWidget(CommonHelper.getAvatarUrl(
              widget.channel.channelID,
              channelType: widget.channel.channelType)),
        ),
        title: FutureBuilder<String?>(
            future: _channelNameFuture,
            builder: (BuildContext context, AsyncSnapshot<String?> snapshot) {
              return HighlightedText(
                  text: snapshot.data ?? '',
                  keyword: widget.keyword,
                  maxLine: 1,
                  highlightColor: DColor.primaryColor);
            }),
        subtitle: _buildSubtitle(context),
      ),
    );
  }

  Widget? _buildSubtitle(BuildContext context) {
    if ((widget.containMemberName ?? "").isNotEmpty){
      return Padding(
      padding: EdgeInsets.only(top: 2),
      child: HighlightedText(
          text: context.l10n
              .containMemberName(widget.containMemberName ?? ''),
          keyword: widget.keyword,
          maxLine: 1,
          highlightColor: DColor.primaryColor),
      );
    }
    if (widget.channel.channelType == WKChannelType.group &&
        (widget.channel.channelRemark).isNotEmpty) {
      return Padding(
        padding: EdgeInsets.only(top: 2),
        child: HighlightedText(
            text: context.l10n
               .groupChannelName(widget.channel.channelName),
            keyword: widget.keyword,
            maxLine: 1,
            highlightColor: DColor.primaryColor));
    }
    return null;
  }

  Future<String?> getChannelName() async {
    var channel = widget.channel;
    var displayName = channel.displayName ?? '';
    if (displayName.isEmpty) {
      var channelDB = await WKIM.shared.channelManager
          .getChannel(widget.channel.channelID, widget.channel.channelType);
      displayName = channelDB?.displayName ?? '';
      if (displayName.isEmpty) {
        WKIM.shared.channelManager.fetchChannelInfo(
            widget.channel.channelID, widget.channel.channelType);
      }
    }
    return displayName;
  }
}
