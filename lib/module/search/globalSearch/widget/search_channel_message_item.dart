import 'package:and/app.dart';
import 'package:and/cache/cache_helper.dart';
import 'package:and/common/res/colours.dart';
import 'package:and/common/res/text_styles.dart';
import 'package:and/constant/wk_channel_member_role.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/model/extension/wk_channel_ext.dart';
import 'package:and/model/extension/wk_msg_ext.dart';
import 'package:and/module/chat/chat_page.dart';
import 'package:and/module/search/globalSearch/global_search_logic.dart';
import 'package:and/utils/common_helper.dart';
import 'package:and/utils/time_utils.dart';
import 'package:and/widget/avatar_widget.dart';
import 'package:and/widget/highlighted_text.dart';
import 'package:flutter/material.dart';
import 'package:wukongimfluttersdk/entity/channel.dart';
import 'package:wukongimfluttersdk/entity/msg.dart';
import 'package:wukongimfluttersdk/type/const.dart';
import 'package:wukongimfluttersdk/wkim.dart';

class UiSearchMessageItem extends StatefulWidget {
  final WKMsg msg;
  final GlobalSearchType? searchType;
  final String? keyword;
  final Function(WKChannel)? onTap;

  const UiSearchMessageItem(
      {super.key,
      required this.msg,
      this.keyword,
      this.searchType,
      this.onTap});

  @override
  State<StatefulWidget> createState() {
    return _UiSearchMessageItemState();
  }
}

class _UiSearchMessageItemState extends State<UiSearchMessageItem> {
  late Future<String?> _channelNameFuture;

  @override
  void initState() {
    super.initState();
    _channelNameFuture = getChannelName();
  }

  @override
  void didUpdateWidget(covariant UiSearchMessageItem oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.msg != oldWidget.msg) {
      _channelNameFuture = getChannelName();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      padding: const EdgeInsets.all(10),
      child: ListTile(
        onTap: () async {
          if (widget.onTap != null) {
            var channel = await WKIM.shared.channelManager
                .getChannel(widget.msg.fromUID, WKChannelType.personal);
            if (channel != null) {
              widget.onTap?.call(channel);
            }
          } else {
            ChatPage.open(
                channelID: widget.msg.channelID,
                channelType: widget.msg.channelType,
                tipsOrderSeq: widget.msg.orderSeq);
          }
        },
        contentPadding: EdgeInsets.only(left: 8, right: 12),
        leading: Padding(
          padding: EdgeInsets.only(right: 12),
          child: AvatarWidget(CommonHelper.getAvatarUrl(widget.msg.fromUID)),
        ),
        trailing: Text(
          TimeUtils.getNewChatTime(widget.msg.timestamp),
          style: const TextStyle(color: Colors.grey, fontSize: 14),
          textAlign: TextAlign.right,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        title: FutureBuilder<String?>(
            future: _channelNameFuture,
            builder: (BuildContext context, AsyncSnapshot<String?> snapshot) {
              return Text(
                snapshot.data ?? '',
                style: TextStyles.fontSize16Normal,
                maxLines: 1,
              );
            }),
        subtitle: Padding(
          padding: EdgeInsets.only(top: 2),
          child: HighlightedText(
              text: widget.msg.messageContent?.content ?? '',
              keyword: widget.keyword,
              maxLine: 1,
              highlightColor: DColor.primaryColor),
        ),
      ),
    );
  }

  Future<String?> getChannelName() async {
    var channel = widget.msg.getChannelInfo();
    var displayName = channel?.displayName ?? '';
    if (widget.searchType == GlobalSearchType.channelMsg) {
      displayName = await widget.msg.fromNameDisplay;
    }
    if (displayName.isEmpty) {
      WKIM.shared.channelManager
          .fetchChannelInfo(widget.msg.fromUID, WKChannelType.personal);
    }
    return displayName;
  }
}
