import 'package:and/common/res/colours.dart';
import 'package:and/common/res/text_styles.dart';
import 'package:and/model/extension/wk_channel_ext.dart';
import 'package:and/module/chat/chat_page.dart';
import 'package:and/module/search/globalSearch/channel_message_search_result.dart';
import 'package:and/module/search/globalSearch/global_search_logic.dart';
import 'package:and/module/search/globalSearch/global_search_page.dart';
import 'package:and/router/router.dart';
import 'package:and/utils/common_helper.dart';
import 'package:and/utils/time_utils.dart';
import 'package:and/widget/avatar_widget.dart';
import 'package:and/widget/highlighted_text.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:wukongimfluttersdk/entity/channel.dart';
import 'package:wukongimfluttersdk/entity/msg.dart';
import 'package:wukongimfluttersdk/type/const.dart';
import 'package:wukongimfluttersdk/wkim.dart';

class UiSearchConversationItem extends StatefulWidget {
  final WKMsg? msg;
  final WKChannel channel;
  final String? content;
  final String? keyword;
  final int messageCount;
  final Function(WKChannel)? onTap;

  const UiSearchConversationItem(
      {super.key,
      required this.msg,
      required this.channel,
      this.messageCount = 1,
      this.content,
      this.keyword,
      this.onTap});

  @override
  State<StatefulWidget> createState() {
    return _UiSearchConversationItemState();
  }
}

class _UiSearchConversationItemState extends State<UiSearchConversationItem> {
  late Future<String?> _channelNameFuture;

  @override
  void initState() {
    super.initState();
    _channelNameFuture = getChannelName();
  }

  @override
  void didUpdateWidget(covariant UiSearchConversationItem oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.channel != oldWidget.channel) {
      _channelNameFuture = getChannelName();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      padding: const EdgeInsets.all(10),
      child: ListTile(
        onTap: () {
          if (widget.onTap != null) {
            widget.onTap?.call(widget.channel);
          } else {
            if (widget.messageCount == 1) {
              ChatPage.open(
                  channelID: widget.channel.channelID ?? '',
                  channelType: widget.channel.channelType,
                  tipsOrderSeq: widget.msg?.orderSeq ?? 0);
            } else {
              Get.toNamed(RouteGet.channelMsgSearchResult,
                  arguments: GlobalSearchArgument(
                      searchKey: widget.keyword,
                      channel: widget.channel,
                      searchType: GlobalSearchType.channelMsg));
            }
          }
        },
        contentPadding: EdgeInsets.only(left: 8, right: 12),
        leading: Padding(
          padding: EdgeInsets.only(right: 12),
          child: AvatarWidget(CommonHelper.getAvatarUrl(
              widget.channel.channelID,
              channelType: widget.channel.channelType)),
        ),
        title: FutureBuilder<String?>(
            future: _channelNameFuture,
            builder: (BuildContext context, AsyncSnapshot<String?> snapshot) {
              return Text(
                snapshot.data ?? '',
                maxLines: 1,
              );
            }),
        subtitle: Padding(
          padding: EdgeInsets.only(top: 2),
          child: widget.messageCount > 1
              ? Text(
                  widget.content ?? '',
                  maxLines: 1,
                )
              : HighlightedText(
                  text: widget.content ?? '',
                  keyword: widget.keyword,
                  maxLine: 1,
                  highlightColor: DColor.primaryColor),
        ),
      ),
    );
  }

  Future<String?> getChannelName() async {
    var channel = widget.channel;
    var displayName = channel.displayName ?? '';
    if (displayName.isEmpty) {
      var channelDB = await WKIM.shared.channelManager
          .getChannel(widget.channel.channelID, widget.channel.channelType);
      displayName = channelDB?.displayName ?? '';
      if (displayName.isEmpty) {
        WKIM.shared.channelManager.fetchChannelInfo(
            widget.channel.channelID, widget.channel.channelType);
      }
    }
    return displayName;
  }
}
