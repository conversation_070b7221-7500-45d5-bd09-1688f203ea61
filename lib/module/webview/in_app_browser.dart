import 'package:and/l10n/l10n.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:url_launcher/url_launcher.dart';

enum AppBarType {
  normal, //   内容顶部
  coverTop, //  覆盖到内容上
}

class CustomInAppBrowser extends StatefulWidget {
  final Uri uri;
  final String? title;
  final bool? showOption;
  final AppBarType? appBarType;
  final String? callbackUrl;

  const CustomInAppBrowser(
      {super.key,
      required this.uri,
      this.title,
      this.callbackUrl,
      this.appBarType = AppBarType.normal,
      this.showOption});

  @override
  State<CustomInAppBrowser> createState() => _CustomInAppBrowserState();
}

class _CustomInAppBrowserState extends State<CustomInAppBrowser> {
  final GlobalKey webViewKey = GlobalKey();

  String url = '';
  String title = '';
  double progress = 0;
  bool? isSecure;
  InAppWebViewController? webViewController;

  AppBarType get appBarType => widget.appBarType ?? AppBarType.normal;

  @override
  void initState() {
    super.initState();
    url = widget.uri.toString();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
        canPop: true,
        onPopInvokedWithResult: (didPop, dynamic) async {
          if (didPop) {
            return;
          }
          final canGoBack = await webViewController?.canGoBack() ?? false;
          if (canGoBack) {
            webViewController?.goBack();
            return;
          } else {
            Navigator.pop(context);
          }
        },
        child: _buildScaffoldContent());
  }

  Widget _buildScaffoldContent() {
    if (appBarType == AppBarType.coverTop) {
      return Scaffold(
        body: SafeArea(
            child: Stack(children: [
          _buildContent(),
          Positioned(
              top: 0,
              left: 0,
              right: 0,
              child: Column(
                children: [_buildAppBar(), _buildProgressIndicator()],
              ))
        ])),
      );
    }

    return Scaffold(
      appBar: _buildAppBar(),
      body: SafeArea(
          child: Stack(children: [_buildContent(), _buildProgressIndicator()])),
    );
  }

  Widget _buildProgressIndicator() {
    return progress < 1.0
        ? LinearProgressIndicator(value: progress)
        : Container();
  }

  AppBar _buildAppBar() {
    return AppBar(
      automaticallyImplyLeading: false,
      leading: IconButton(
        icon: const Icon(Icons.close),
        onPressed: () {
          Navigator.pop(context);
        },
      ),
      title: Row(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: <Widget>[
          Expanded(
              child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text(
                widget.title ?? title,
                textAlign: TextAlign.center,
                overflow: TextOverflow.fade,
              ),
              Visibility(
                  visible: widget.title?.isEmpty ?? false,
                  child: Row(
                    mainAxisSize: MainAxisSize.max,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      isSecure != null
                          ? Icon(
                              isSecure == true ? Icons.lock : Icons.lock_open,
                              color:
                                  isSecure == true ? Colors.green : Colors.red,
                              size: 12)
                          : Container(),
                      const SizedBox(
                        width: 5,
                      ),
                      Flexible(
                          child: Text(
                        url,
                        style: const TextStyle(
                            fontSize: 12, color: Colors.white70),
                        overflow: TextOverflow.fade,
                      )),
                    ],
                  ))
            ],
          )),
        ],
      ),
      actions: [
        (widget.showOption ?? false)
            ? PopupMenuButton<int>(
                onSelected: (item) => handleClick(item),
                itemBuilder: (context) => [
                  PopupMenuItem<int>(
                      value: 0,
                      child: Row(
                        children: [
                          const Icon(Icons.refresh),
                          const SizedBox(
                            width: 5,
                          ),
                          Text(context.l10n.webviewRefresh)
                        ],
                      )),
                  PopupMenuItem<int>(
                      value: 1,
                      child: Row(
                        children: [
                          const Icon(Icons.open_in_browser),
                          const SizedBox(
                            width: 5,
                          ),
                          Text(context.l10n.webviewOpenInBrowser)
                        ],
                      )),
                  PopupMenuItem<int>(
                      value: 2,
                      child: Row(
                        children: [
                          const Icon(Icons.clear_all),
                          const SizedBox(
                            width: 5,
                          ),
                          Text(context.l10n.webviewCleanBrowsingData)
                        ],
                      )),
                ],
              )
            : const SizedBox()
      ],
    );
  }

  Widget _buildContent() {
    return InAppWebView(
      key: webViewKey,
      initialUrlRequest: URLRequest(url: WebUri(widget.uri.toString())),
      initialSettings: InAppWebViewSettings(
          transparentBackground: true,
          safeBrowsingEnabled: true,
          isFraudulentWebsiteWarningEnabled: true),
      onWebViewCreated: (controller) async {
        webViewController = controller;
        if (!kIsWeb && defaultTargetPlatform == TargetPlatform.android) {
          await controller.startSafeBrowsing();
        }
      },
      onLoadStart: (controller, url) {
        if (url != null) {
          setState(() {
            this.url = url.toString();
            isSecure = urlIsSecure(url);
          });
        }
      },
      onLoadStop: (controller, url) async {
        if (url != null) {
          setState(() {
            this.url = url.toString();
          });
        }

        final sslCertificate = await controller.getCertificate();
        setState(() {
          isSecure =
              sslCertificate != null || (url != null && urlIsSecure(url));
        });
      },
      onUpdateVisitedHistory: (controller, url, isReload) {
        if (url != null) {
          setState(() {
            this.url = url.toString();
          });
        }
      },
      onTitleChanged: (controller, title) {
        if (title != null) {
          setState(() {
            this.title = title;
          });
        }
      },
      onProgressChanged: (controller, progress) {
        setState(() {
          this.progress = progress / 100;
        });
      },
      shouldOverrideUrlLoading: (controller, navigationAction) async {
        final url = navigationAction.request.url;
        if (widget.callbackUrl != null) {
          if (url.toString().contains(widget.callbackUrl!)) {
            Navigator.pop(context, url.toString());
            return NavigationActionPolicy.CANCEL;
          }
        }
        if (navigationAction.isForMainFrame &&
            url != null &&
            !['http', 'https', 'file', 'chrome', 'data', 'javascript', 'about']
                .contains(url.scheme)) {
          if (await canLaunchUrl(url)) {
            launchUrl(url);
            return NavigationActionPolicy.CANCEL;
          }
        }
        return NavigationActionPolicy.ALLOW;
      },
    );
  }

  void handleClick(int item) async {
    switch (item) {
      case 0:
        await webViewController?.reload();
        break;
      case 1:
        await InAppBrowser.openWithSystemBrowser(url: WebUri(url));
        break;
      case 2:
        await webViewController?.clearCache();
        if (!kIsWeb && defaultTargetPlatform == TargetPlatform.android) {
          await webViewController?.clearHistory();
        }
        setState(() {});
        break;
    }
  }

  static bool urlIsSecure(Uri url) {
    return (url.scheme == "https") || isLocalizedContent(url);
  }

  static bool isLocalizedContent(Uri url) {
    return (url.scheme == "file" ||
        url.scheme == "chrome" ||
        url.scheme == "data" ||
        url.scheme == "javascript" ||
        url.scheme == "about");
  }
}
