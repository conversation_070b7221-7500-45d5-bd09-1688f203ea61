import 'package:and/module/videocall/videocall_argument.dart';
import 'package:and/module/videocall/videocall_logic.dart';
import 'package:and/module/videocall/widget/participant_view.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class CallFloatingView extends StatefulWidget {
  final VideoCallArgument argument;

  const CallFloatingView({
    super.key,
    required this.argument,
  });

  @override
  State<CallFloatingView> createState() => CallFloatingViewState();
}

class CallFloatingViewState extends State<CallFloatingView> {
  final VideoCallLogic logic = Get.find<VideoCallLogic>();

  @override
  Widget build(BuildContext context) {
    return Container(
      width: logic.floatingSize.width,
      height: logic.floatingSize.height,
      decoration: BoxDecoration(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(51),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Stack(
        children: [
          // 远程用户视图
          Positioned.fill(
            child: ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: logic.participants.isNotEmpty
                  ? ParticipantView(
                      participant: logic.participants.first,
                      isLocal: false,
                      isVideoCall: widget.argument.callType == CallType.video,
                      displayName: logic.participants.first.name,
                      channelId: widget.argument.channel.channelID,
                      channelType: widget.argument.channel.channelType,
                    )
                  : Container(color: Colors.transparent),
            ),
          ),
          // 点击切换回全屏
          Positioned.fill(
            child: GestureDetector(
              onTap: logic.toggleDisplayMode,
              child: Container(color: Colors.transparent),
            ),
          ),
        ],
      ),
    );
  }
}
