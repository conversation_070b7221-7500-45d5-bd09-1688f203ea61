import 'package:flutter/material.dart';

class AvatarRippleAnimation extends StatefulWidget {
  final Widget child;
  final Color rippleColor;
  final int numberOfRipples;
  final Duration duration;

  const AvatarRippleAnimation({
    super.key,
    required this.child,
    this.rippleColor = Colors.white,
    this.numberOfRipples = 3,
    this.duration = const Duration(seconds: 2),
  });

  @override
  State<AvatarRippleAnimation> createState() => _AvatarRippleAnimationState();
}

class _AvatarRippleAnimationState extends State<AvatarRippleAnimation>
    with TickerProviderStateMixin {
  late List<AnimationController> _controllers;
  late List<Animation<double>> _animations;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _controllers = List.generate(
      widget.numberOfRipples,
      (index) => AnimationController(
        vsync: this,
        duration: widget.duration,
      ),
    );

    _animations = _controllers.map((controller) {
      return Tween<double>(
        begin: 0.0,
        end: 1.0,
      ).animate(CurvedAnimation(
        parent: controller,
        curve: Curves.easeInOut,
      ));
    }).toList();

    // 错开动画开始时间
    for (var i = 0; i < _controllers.length; i++) {
      Future.delayed(
        Duration(milliseconds: (i * widget.duration.inMilliseconds) ~/ widget.numberOfRipples),
        () {
          if (mounted) {
            _controllers[i].repeat();
          }
        },
      );
    }
  }

  @override
  void dispose() {
    for (var controller in _controllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.center,
      children: [
        ..._animations.map((animation) {
          return AnimatedBuilder(
            animation: animation,
            builder: (context, child) {
              return Container(
                width: 1 + (animation.value * 140),
                height: 1 + (animation.value * 140),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: widget.rippleColor
                      .withOpacity(0.3 * (1 - animation.value)),
                ),
              );
            },
          );
        }).toList(),
        widget.child,
      ],
    );
  }
}