import 'dart:io';

import 'package:and/cache/cache_helper.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/model/app_version.dart';
import 'package:and/utils/http_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:package_info_plus/package_info_plus.dart';

import 'app_upgrade_dialog.dart';
import 'app_upgrade_page.dart';

class AppUpgradeUtils {
  static const MethodChannel _channel = MethodChannel('install_apk');

  static Future<void> checkAppUpdateHome(BuildContext context) async {
    AppVersion update;
    try {
      update = await HttpUtils.checkAppVersion();
    } catch (e) {
      return;
    }
    if (update.appVersion.isEmpty) {
      return;
    }
    if (update.isForce == 1) {
      Get.to(() => AppUpgradePage(appUpdate: update));
    } else {
      var lastVersion = CacheHelper.lastShowUpdateVersion;
      var time = CacheHelper.lastShowUpdateTime ?? 0;
      var now = DateTime.now().millisecondsSinceEpoch;
      if ((time - now).abs() < Duration(days: 3).inMilliseconds) {
        // 3天内相同版本不重复提示
        if (update.appVersion == lastVersion) {
          print("Same version, ignore");
          return;
        }
      }

      final packageInfo = await PackageInfo.fromPlatform();
      String version = packageInfo.version;
      if (!canUpdate(version, update.appVersion)) {
        print("No update");
        return;
      }

      CacheHelper.saveLastShowUpdateTime(now);
      CacheHelper.saveLastShowUpdateVersion(update.appVersion);
      showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) {
            return AppUpgradeDialog(appUpdate: update);
          });
    }
  }

  static Future<void> checkAppUpdateSetting(BuildContext context) async {
    AppVersion update;
    try {
      update = await HttpUtils.checkAppVersion();
    } catch (e) {
      EasyLoading.showToast(context.l10n.noUpgrade);
      return;
    }
    if (update.isForce == 1) {
      Get.to(() => AppUpgradePage(appUpdate: update));
    } else {
      final packageInfo = await PackageInfo.fromPlatform();
      String version = packageInfo.version;

      if (!canUpdate(version, update.appVersion)) {
        EasyLoading.showToast(context.l10n.noUpgrade);
        return;
      }

      showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) {
            return AppUpgradeDialog(appUpdate: update);
          });
    }
  }

  static bool canUpdate(String localVersion, String remoteVersion) {
    if (remoteVersion.isEmpty) {
      return false;
    }
    final local = localVersion.split('.').map(int.parse).toList();
    final store = remoteVersion.split('.').map(int.parse).toList();

    // Each consecutive field in the version notation is less significant than the previous one,
    // therefore only one comparison needs to yield `true` for it to be determined that the store
    // version is greater than the local version.
    for (var i = 0; i < store.length; i++) {
      // The store version field is newer than the local version.
      if (store[i] > local[i]) {
        return true;
      }

      // The local version field is newer than the store version.
      if (local[i] > store[i]) {
        return false;
      }
    }

    // The local and store versions are the same.
    return false;
  }

  static Future<void> installApk(File file) async {
    try {
      await _channel.invokeMethod('install', {'apkPath': file.path});
    } catch (e) {
      print("安装失败: $e");
    }
  }
}
