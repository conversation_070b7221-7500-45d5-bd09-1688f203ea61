import 'dart:io';

import 'package:and/common/environment/environment_config.dart';
import 'package:and/common/res/colours.dart';
import 'package:and/common/res/text_styles.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/model/app_version.dart';
import 'package:and/model/extension/app_version_ext.dart';
import 'package:and/utils/common_helper.dart';
import 'package:and/widget/submit_button.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:ota_update/ota_update.dart';

import 'app_upgrade_utils.dart';

class AppUpgradePage extends StatefulWidget {
  final AppVersion appUpdate;

  AppUpgradePage({super.key, required this.appUpdate}) {}

  @override
  State createState() => _AppUpgradePageState();
}

class _AppUpgradePageState extends State<AppUpgradePage> {
  OtaEvent? _currentEvent;
  bool _isDownloading = false;

  @override
  void initState() {
    super.initState();
    widget.appUpdate.deleteApkFile();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
        onWillPop: () async {
          return false;
        },
        child: Scaffold(
            body: Container(
                padding: EdgeInsets.fromLTRB(10, 32, 10, 20),
                child: _buildContent())));
  }

  Widget _buildContent() {
    return Column(
      children: [
        SizedBox(height: 100),
        Text(
          context.l10n.forceUpgradeTitle,
          style: TextStyles.fontSize18Bold.copyWith(fontSize: 30),
        ),
        SizedBox(height: 60),
        _buildUpdateInfo(),
        Spacer(),
        Visibility(visible: !_isDownloading, child: _buildActionButton()),
        Visibility(visible: _isDownloading, child: _buildDownloading()),
        SizedBox(height: 60),
      ],
    );
  }

  Widget _buildUpdateInfo() {
    return SizedBox(
        width: double.infinity,
        child: Column(
          children: [
            Text(
              widget.appUpdate.appVersion,
              textAlign: TextAlign.center,
              style: TextStyles.fontSize18Bold.copyWith(fontSize: 90),
            ),
            SizedBox(height: 10),
            Text(
              widget.appUpdate.updateDesc,
              textAlign: TextAlign.center,
              style: TextStyles.fontSize16Normal,
            ),
            SizedBox(height: 10),
          ],
        ));
  }

  Widget _buildActionButton() {
    return Column(
      children: [
        Text(
          context.l10n.forceUpgradeInfo,
          style: TextStyles.fontSize15Normal,
        ),
        SizedBox(height: 16),
        SizedBox(
            width: 180,
            child: SubmitButton(
                text: context.l10n.upgradeNow,
                onPressed: () {
                  if (defaultTargetPlatform == TargetPlatform.android) {
                    if (EnvironmentConfig.isGooglePlay()) {
                      CommonHelper.launchAppStore();
                    } else {
                      _downloadApp();
                    }
                  } else if (defaultTargetPlatform == TargetPlatform.iOS) {
                    CommonHelper.launchAppStore();
                  }
                })),
        SizedBox(height: 16),
      ],
    );
  }

  Widget _buildDownloading() {
    double progress = 0;
    if (_isDownloading) {
      try {
        progress = double.parse(_currentEvent?.value ?? "") / 100;
      } catch (e) {
        print("无法将字符串转换为数字: $e");
      }
    }
    return Column(
      children: [
        SizedBox(
            width: 180,
            child: Center(
                child: ClipRRect(
                    borderRadius: BorderRadius.circular(8.0), // 设置圆角半径
                    child: LinearProgressIndicator(
                      value: progress,
                      // 设置进度值
                      backgroundColor: Colors.grey[300],
                      // 设置背景颜色
                      valueColor:
                          AlwaysStoppedAnimation<Color>(DColor.primaryColor),
                      // 设置进度颜色
                      minHeight: 8.0, // 设置最小高度
                    )))),
        SizedBox(height: 16),
        Text(
          context.l10n.downloading,
          style: TextStyles.fontSize15Normal,
        ),
      ],
    );
  }

  void _downloadApp() async {
    try {
      var fileName = widget.appUpdate.fileName;
      File file = await widget.appUpdate.apkFile;
      if (file.existsSync()) {
        AppUpgradeUtils.installApk(file);
        return;
      }
      setState(() {
        _isDownloading = true;
      });
      OtaUpdate()
          .execute(
        CommonHelper.getFileUrl(widget.appUpdate.downloadUrl),
        // OPTIONAL
        destinationFilename: fileName,
      )
          .listen(
        (OtaEvent event) {
          _isDownloading = event.status == OtaStatus.DOWNLOADING;
          if (event.status == OtaStatus.CANCELED ||
              event.status == OtaStatus.DOWNLOAD_ERROR ||
              event.status == OtaStatus.CHECKSUM_ERROR) {
            file.delete();
          }
          setState(() => _currentEvent = event);
        },
      );
    } catch (e) {
      print('Failed to make OTA update. Details: $e');
    }
  }
}
