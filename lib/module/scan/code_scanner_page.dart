import 'package:and/l10n/l10n.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_scanner/mobile_scanner.dart';
import 'package:and/utils/image_utils.dart';
import 'package:oktoast/oktoast.dart';

class CodeScannerPage extends StatefulWidget {
  const CodeScannerPage({super.key});

  @override
  State<CodeScannerPage> createState() => _CodeScannerPageState();
}

class _CodeScannerPageState extends State<CodeScannerPage>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late MobileScannerController _scannerController;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 3),
    );
    _animationController.repeat();
    _scannerController = MobileScannerController();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _scannerController.dispose();
    super.dispose();
  }

  void _handleBarcode(BarcodeCapture barcodes) {
    if (mounted) {
      setState(() {
        var barcode = barcodes.barcodes.firstOrNull;
        if (barcode != null) {
          Get.back(result: barcode);
        } else {
          showToast(context.l10n.noValidQrcode);
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final scanAreaSize = size.width * 0.7;
    final scanAreaRect = Rect.fromCenter(
      center: Offset(size.width / 2, size.height / 2),
      width: scanAreaSize,
      height: scanAreaSize,
    );

    return Scaffold(
      appBar: AppBar(
        actions: [
          _buildPhotoIcon(),
        ],
      ),
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          MobileScanner(
            onDetect: _handleBarcode,
            controller: _scannerController,
          ),
          _buildScanArea(),
        ],
      ),
    );
  }

  Widget _buildPhotoIcon() {
    return IconButton(
      icon: const Icon(Icons.photo_library, color: Colors.black),
      onPressed: () async {
        final file = await ImageUtils.pickImage(context);
        if (file != null) {
          final barcodes = await _scannerController.analyzeImage(file.path);
          if (barcodes != null && barcodes.barcodes.isNotEmpty) {
            _handleBarcode(barcodes);
          } else {
            showToast(context.l10n.noValidQrcode);
          }
        }
      },
    );
  }

  Widget _buildScanArea() {
    return LayoutBuilder(builder: (context, constraints) {
      double maxWidth = constraints.maxWidth;
      double maxHeight = constraints.maxHeight;

      final scanAreaSize = maxWidth * 0.7;

      final scanAreaRect = Rect.fromCenter(
        center: Offset(maxWidth / 2, maxHeight / 2),
        width: scanAreaSize,
        height: scanAreaSize,
      );

      return Stack(
        children: [
          Center(child: _buildScanAnimated(scanAreaSize)),
          CustomPaint(
            painter: ScannerOverlayPainter(scanArea: scanAreaRect),
            child: const SizedBox.expand(),
          ),
        ],
      );
    });
  }

  Widget _buildScanAnimated(double scanAreaSize) {
    return Container(
      width: scanAreaSize,
      height: scanAreaSize,
      decoration: BoxDecoration(
        border: Border.all(color: Colors.white, width: 2),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Stack(
        children: [
          AnimatedBuilder(
            animation: _animationController,
            builder: (context, child) {
              return Positioned(
                top: _animationController.value * scanAreaSize,
                left: 0,
                right: 0,
                child: Container(
                  height: 2,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Colors.white.withOpacity(0),
                        Colors.white,
                        Colors.white.withOpacity(0),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }
}

class ScannerOverlayPainter extends CustomPainter {
  final Rect scanArea;

  ScannerOverlayPainter({required this.scanArea});

  @override
  void paint(Canvas canvas, Size size) {
    // 使用CustomPaint的实际大小来绘制遮罩层
    final paint = Paint()
      ..color = Colors.black.withOpacity(0.4)
      ..style = PaintingStyle.fill;

    final path = Path()
      ..addRect(Rect.fromLTWH(0, 0, size.width, size.height))
      ..addRRect(RRect.fromRectAndRadius(scanArea, const Radius.circular(12)))
      ..fillType = PathFillType.evenOdd;

    canvas.drawPath(path, paint);

    // // 绘制扫描框边框
    // final borderPaint = Paint()
    //   ..color = Colors.white
    //   ..style = PaintingStyle.stroke
    //   ..strokeWidth = 2;
    //
    // canvas.drawRRect(
    //     RRect.fromRectAndRadius(scanArea, const Radius.circular(12)),
    //     borderPaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
