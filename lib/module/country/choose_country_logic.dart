import 'package:and/common/utils/easy_loading_helper.dart';
import 'package:and/http/my_http.dart';
import 'package:and/io/common.dart';
import 'package:and/model/country.dart';
import 'package:and/module/common/controller/list_controller.dart';
import 'package:and/utils/pinyin_utils.dart';
import 'package:country_codes/country_codes.dart';
import 'package:get/get.dart';

class ChooseCountryLogic extends ListController<Country> {
  final RxMap<String, List<Country>> groupedCountries =
      <String, List<Country>>{}.obs;
  final RxList<String> letters = <String>[].obs;
  final filterKey = "".obs;

  ChooseCountryLogic() {
    ever(list, (value) {
      _group();
    });
    ever(filterKey, (value) {
      _group();
    });
  }

  @override
  void onReady() {
    super.onReady();
    startRefresh();
  }

  @override
  Future<List<Country>?> loadData() async {
    await CountryCodes.init();
    var localCountries = CountryCodes.countryCodes();
    Map<String, String?> dialCodeToCountryMap = {};

    for (var country in localCountries) {
      var dialCode = country.dialCode;
      if (dialCode != null) {
        var code = dialCode.replaceAll("+", "00");
        dialCodeToCountryMap[code] = country.localizedName;
      }
    }

    var countries = await CommonApi(MyHttp.dio).countries();
    var list = <Country>[];
    for (var country in countries) {
      var dialCode = country.code;
      var name = dialCodeToCountryMap[dialCode];
      if (name != null) {
        list.add(country.copyWith(
          name: name,
        ));
      }
    }

    return list;
  }

  void startRefresh() {
    EasyLoadingHelper.show(onAction: () async {
      await refreshData();
    });
  }

  void _group() {
    var countriesFilter = <Country>[];
    if (filterKey.value.isEmpty) {
      countriesFilter.addAll(list);
    } else {
      countriesFilter.addAll(list.where((e) =>
          e.name.contains(filterKey.value) ||
          e.code.contains(filterKey.value)));
    }
    // 按首字母排序
    countriesFilter.sort((a, b) {
      var aLetter = PinyinUtils.getFirstLetter(a.name);
      var bLetter = PinyinUtils.getFirstLetter(b.name);
      return aLetter.compareTo(bLetter);
    });
    // 分组
    groupedCountries.clear();
    for (var country in countriesFilter) {
      var firstLetter = PinyinUtils.getFirstLetter(country.name);
      groupedCountries.putIfAbsent(firstLetter, () => []).add(country);
    }
    // 更新字母列表
    letters.value = groupedCountries.keys.toList()..sort();
  }

  void onSearch(String key) {
    filterKey.value = key;
  }
}
