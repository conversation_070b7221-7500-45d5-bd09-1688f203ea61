import 'package:and/common/res/colours.dart';
import 'package:and/common/res/text_styles.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/model/country.dart';
import 'package:and/module/contact/widget/add_contact_menu_widget.dart';
import 'package:and/module/country/widget/ui_country_item.dart';
import 'package:and/module/search/widget/search_input_widget.dart';
import 'package:and/widget/letter_index_bar.dart';
import 'package:and/widget/refresh/refresh_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:super_sliver_list/super_sliver_list.dart';

import 'choose_country_logic.dart';

class ChooseCountryPage extends StatefulWidget {
  const ChooseCountryPage({super.key});

  @override
  State<StatefulWidget> createState() {
    return _ChooseCountryPageState();
  }
}

class _ChooseCountryPageState extends State<ChooseCountryPage> {
  final logic = Get.find<ChooseCountryLogic>();
  late final list = logic.list;
  final ScrollController scrollController = ScrollController();
  final ListController listController = ListController();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: Text(context.l10n.chooseCountry)
        ),
        body: Obx(() => SafeArea(child: _buildContent())));
  }

  Widget _buildContent() {
    return Column(children: [
      Container(
        margin: EdgeInsets.symmetric(horizontal: 15),
        child: SearchInputWidget(
          searchPlaceHolder: context.l10n.search,
          showImage: false,
          onKeyChanged: (key) {
            logic.onSearch(key);
          },
          onFieldSubmitted: (key) {
            logic.onSearch(key);
          },
        ),
      ),
      Expanded(child: _buildList())
    ]);
  }

  Widget _buildList() {
    if (logic.letters.isEmpty) {
      return buildLoadStateWidget(logic.loadStatus.value, () {
          logic.refreshData();
      });
    }
    return Stack(
      children: [
        CustomScrollView(
          controller: scrollController,
          slivers: [
            SliverPadding(
              padding: const EdgeInsets.symmetric(vertical: 15),
              sliver: SuperSliverList(
                listController: listController,
                delegate: SliverChildBuilderDelegate((context, index) {
                  String letter = logic.letters[index];
                  List<Country> countries =
                      logic.groupedCountries[letter] ?? [];
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 16, vertical: 8),
                        child: Text(
                          letter,
                          style: TextStyles.fontSize14Bold.copyWith(
                              color: DColor.primaryColor
                          ),
                        ),
                      ),
                      ...countries.map((country) => UiCountryItem(
                          country: country,
                          onTap: () {
                            Get.back(result: country.code);
                          })),
                    ],
                  );
                }, childCount: logic.letters.length),
              ),
            ),
          ],
        ),
        Positioned(
          right: 5,
          top: 0,
          bottom: 0,
          child: Center(
            child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 8),
                margin: const EdgeInsets.symmetric(horizontal: 8),
                child: AlphabetScrollbar(
                  selectedLetterSize: 30,
                  factor: 0,
                  selectedLetterAdditionalSpace: 30,
                  onLetterChange: (value) {
                    int position = logic.letters.indexWhere((e) => e == value);
                    if (position >= 0) {
                      listController.jumpToItem(
                        index: position,
                        scrollController: scrollController,
                        alignment: 0,
                      );
                    }
                  },
                )),
          ),
        ),
      ],
    );
  }
}
