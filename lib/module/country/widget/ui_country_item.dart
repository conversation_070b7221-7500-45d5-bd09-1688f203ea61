import 'package:and/common/res/text_styles.dart';
import 'package:and/model/country.dart';
import 'package:flutter/material.dart';

class UiCountryItem extends StatelessWidget {
  final Country country;
  final Function() onTap;

  const UiCountryItem({
    super.key,
    required this.country,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: onTap,
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.symmetric(horizontal: 10, vertical: 10),
        margin: EdgeInsets.only(right: 30),
        child: Text(
          "${country.icon} ${country.name} (+${country.code.substring(2)})",
          style: TextStyles.fontSize18Normal,
        ),
      ),
    );
  }
}
