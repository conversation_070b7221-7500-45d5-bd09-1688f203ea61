import 'package:and/common/res/colours.dart';
import 'package:and/common/res/text_styles.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/model/extension/wk_channel_ext.dart';
import 'package:and/module/contact/contact_page.dart';
import 'package:and/router/router.dart';
import 'package:and/widget/submit_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:wukongimfluttersdk/entity/channel.dart';

import 'choose_contact_logic.dart';

class ChooseContactArgument {
  bool isIncludeSystem; //是否显示系统频道
  List<String>? unSelectChannelIds; // 不能选择的uid
  bool isMultiSelect; //是否多选

  ChooseContactArgument(
      {this.isIncludeSystem = true,
      this.unSelectChannelIds,
      this.isMultiSelect = true});

  factory ChooseContactArgument.fromGet() {
    return (Get.arguments as ChooseContactArgument);
  }
}

class ChooseContactPage extends StatefulWidget {
  const ChooseContactPage({super.key});

  @override
  State<StatefulWidget> createState() {
    return _ChooseContactPageState();
  }

  static Future<List<WKChannel>?> open(
      {bool isIncludeSystem = false,
      List<String>? unSelectChannelIds,
      bool isMultiSelect = true}) async {
    var result = await Get.toNamed(RouteGet.chooseContact,
        preventDuplicates: false,
        arguments: ChooseContactArgument(
            isIncludeSystem: isIncludeSystem,
            unSelectChannelIds: unSelectChannelIds,
            isMultiSelect: isMultiSelect));

    return result;
  }
}

class _ChooseContactPageState extends State<ChooseContactPage> {
  late final ChooseContactArgument argument = ChooseContactArgument.fromGet();
  final logic = Get.find<ChooseContactLogic>();
  late RxList<WKChannel> selectedItems = logic.selectedChannels;
  final _contactViewKey = GlobalKey<ContactPageState>();
  final _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();

    Get.delete<ChooseContactLogic>();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: Text(context.l10n.chooseContact),
          actions: argument.isMultiSelect ? [Obx(() => _submitButton())] : null,
        ),
        body: Obx(() => _buildContent()));
  }

  Widget _submitButton() {
    var selectedCount = selectedItems.length;
    return Padding(
      padding: EdgeInsets.only(right: 15),
      child: SubmitButton(
          padding: EdgeInsets.symmetric(horizontal: 10, vertical: 6),
          enable: selectedCount > 0,
          onPressed: () {
            Get.back(result: selectedItems);
          },
          text: "${context.l10n.globalConfirm}($selectedCount)"),
    );
  }

  Widget _buildContent() {
    var selectedChannelIds =
        (argument.unSelectChannelIds ?? []) + logic.selectedChannelIds;
    return Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
      _buildSearchContainer(),
      Expanded(
          child: ContactPage(
        key: _contactViewKey,
        selectedChannelIds: selectedChannelIds,
        unSelectChannelIds: argument.unSelectChannelIds,
        isIncludeSystem: argument.isIncludeSystem,
        onTap: (channel) {
          if (argument.isMultiSelect) {
            var result = logic.select(channel);
            if (result) {
              _searchController.clear();
            }
          } else {
            Get.back(result: [channel]);
          }
        },
      ))
    ]);
  }

  Widget _buildSearchContainer() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 15),
      padding: EdgeInsets.symmetric(horizontal: 10, vertical: 4),
      width: double.infinity,
      constraints: BoxConstraints(maxHeight: 300),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(4),
      ),
      child: SingleChildScrollView(
        child: Wrap(
          alignment: WrapAlignment.start,
          crossAxisAlignment: WrapCrossAlignment.center,
          spacing: 2.0,
          runSpacing: 4.0,
          children: [
            ...selectedItems.map((item) => Chip(
                  label: Text(
                    item.displayName,
                    style: TextStyles.fontSize13Normal,
                  ),
                  backgroundColor: DColor.primaryColor,
                  onDeleted: () => logic.select(item),
                )),
            _buildSearchInput()
          ],
        ),
      ),
    );
  }

  Widget _buildSearchInput() {
    return SizedBox(
        width: 200,
        child: Focus(
            onKeyEvent: (FocusNode node, KeyEvent event) {
              if (event is KeyDownEvent) {
                if (event.logicalKey == LogicalKeyboardKey.backspace) {
                  print('退格键被按下');
                  var result = logic.backspace();
                  if (result) {
                    return KeyEventResult.handled;
                  }
                }
              }
              return KeyEventResult.ignored;
            },
            child: TextFormField(
              controller: _searchController,
              onChanged: (value) {
                _contactViewKey.currentState?.search(value);
              },
              onFieldSubmitted: (value) {
                _contactViewKey.currentState?.search(value);
              },
              textInputAction: TextInputAction.search,
              decoration: InputDecoration(
                border: InputBorder.none,
                hintText: context.l10n.search,
                hintStyle: TextStyles.fontSize15Normal
                    .copyWith(color: DColor.hintText.withOpacity(0.2)),
              ),
            )));
  }
}
