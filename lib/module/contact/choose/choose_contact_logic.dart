import 'package:and/model/extension/wk_channel_ext.dart';
import 'package:and/module/common/controller/list_controller.dart';
import 'package:and/module/contact/choose/choose_contact_page.dart';
import 'package:and/utils/pinyin_utils.dart';
import 'package:get/get.dart';
import 'package:wukongimfluttersdk/entity/channel.dart';
import 'package:wukongimfluttersdk/type/const.dart';
import 'package:wukongimfluttersdk/wkim.dart';

class ChooseContactLogic extends GetxController {
  final String key = "ui_choose_contact";
  final ChooseContactArgument argument;

  final selectedChannels = <WKChannel>[].obs;

  List<String> get selectedChannelIds =>
      selectedChannels.map((e) => e.channelID).toList();

  ChooseContactLogic({required this.argument});

  bool select(WKChannel channel) {
    var isSelected =
        selectedChannels.any((e) => e.channelID == channel.channelID);
    if (isSelected) {
      selectedChannels.removeWhere((e) => e.channelID == channel.channelID);
      return false;
    } else {
      selectedChannels.add(channel);
      return true;
    }
  }

  bool backspace() {
    if (selectedChannels.isNotEmpty) {
      selectedChannels.removeLast();
      return true;
    }
    return false;
  }
}
