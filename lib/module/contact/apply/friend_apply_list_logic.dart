import 'package:and/http/my_http.dart';
import 'package:and/io/friend.dart';
import 'package:and/model/friend_apply.dart';
import 'package:and/model/request/sure_friend_request.dart';
import 'package:and/module/common/controller/list_controller.dart';
import 'package:wukongimfluttersdk/wkim.dart';

class FriendApplyListLogic extends ListController<FriendApply> {
  final String key = 'apply_friends';

  FriendApplyListLogic();

  @override
  void onReady() {
    super.onReady();
    _initListener();
  }

  @override
  void onClose() {
    _removeListener();
    super.onClose();
  }

  @override
  Future<List<FriendApply>?> loadData() async {
    return FriendApi(MyHttp.dio).friendApplyList(1, 999);
  }

  _initListener() {
    // 监听刷新channel资料事件
    WKIM.shared.channelManager.addOnRefreshListener(key, (channel) {
      print("刷新channel资料事件");
      refreshData();
    });
  }

  _removeListener() {
    WKIM.shared.channelManager.removeOnRefreshListener(key);
  }


  void agreeApply(FriendApply item) {
    FriendApi(MyHttp.dio)
        .agreeFriendApply(SureFriendRequest(
      token: item.token,
    ))
        .then((value) {
      if (value.success) {
        refreshData();
      }
    });
  }
}
