import 'dart:async';

import 'package:and/app.dart';
import 'package:and/eventbus/contact_sync_event.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/model/friend_apply.dart';
import 'package:and/module/contact/widget/ui_friend_apply_item.dart';
import 'package:and/utils/http_utils.dart';
import 'package:and/widget/refresh/load_state.dart';
import 'package:and/widget/refresh/refresh_page.dart';
import 'package:and/widget/refresh/refresh_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:super_sliver_list/super_sliver_list.dart';

import 'friend_apply_list_logic.dart';

class FriendApplyListPage extends StatefulWidget {
  const FriendApplyListPage({super.key});

  @override
  State<StatefulWidget> createState() {
    return _FriendApplyListPageState();
  }
}

class _FriendApplyListPageState extends RefreshState<FriendApplyListPage> {
  final logic = Get.find<FriendApplyListLogic>();
  late final list = logic.list;
  final ScrollController scrollController = ScrollController();
  final ListController listController = ListController();

  late StreamSubscription _contactStatusEventSubscription;

  @override
  void initState() {
    super.initState();

    _contactStatusEventSubscription =
        eventBus.on<ContactSyncEvent>().listen((event) {
      if (mounted) {
        logic.refreshData();
      }
    });

    HttpUtils.deleteFriendApplyCount();
  }

  @override
  void dispose() {
    _contactStatusEventSubscription.cancel();
    super.dispose();
  }

  @override
  Rx<FitLoadStatus> getLoadState() {
    return logic.loadStatus;
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Scaffold(
        appBar: AppBar(title: Text(context.l10n.newFriends)),
        body: Obx(() => _buildRefreshList()));
  }

  Widget _buildRefreshList() {
    return buildRefreshWidget(
      refreshController: refreshController,
      loadStatus: logic.loadStatus.value,
      hasData: logic.list.isNotEmpty,
      builder: (physics) => _buildList(physics),
      onRefresh: () {
        logic.refreshData();
      },
    );
  }

  Widget _buildList(ScrollPhysics? physics) {
    return CustomScrollView(
      slivers: [
        SliverPadding(
          padding: const EdgeInsets.symmetric(vertical: 10),
          sliver: SuperSliverList(
            listController: listController,
            delegate: SliverChildBuilderDelegate((context, index) {
              FriendApply item = list[index];
              return UiFriendApplyItem(
                  item: item,
                  onAgreeApplyTap: () {
                    logic.agreeApply(item);
                  });
            }, childCount: list.length),
          ),
        )
      ],
    );
  }
}
