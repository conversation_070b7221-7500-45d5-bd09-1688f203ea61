import 'package:and/http/my_http.dart';
import 'package:and/io/group.dart';
import 'package:and/model/extension/group_ext.dart';
import 'package:and/module/common/controller/list_controller.dart';
import 'package:wukongimfluttersdk/entity/channel.dart';
import 'package:wukongimfluttersdk/wkim.dart';

class MyGroupsLogic extends ListController<WKChannel> {
  final String key = "ui_group";

  MyGroupsLogic();

  @override
  void onReady() {
    super.onReady();
    _initListener();
  }

  @override
  void onClose() {
    _removeListener();
    super.onClose();
  }

  @override
  Future<List<WKChannel>?> loadData() async {
    final groups = (await GroupApi(MyHttp.dio).getMyGroups());

    return groups.map((e) => e.toWKChannel()).toList();
  }

  _initListener() {
    // 监听刷新channel资料事件
    WKIM.shared.channelManager.addOnRefreshListener(key, (channel) {
      var index = list.indexWhere((msg) =>
          msg.channelID == channel.channelID &&
          msg.channelType == channel.channelType);
      if (index != -1) {
        list[index] = channel;
      }
    });
  }

  _removeListener() {
    WKIM.shared.channelManager.removeOnRefreshListener(key);
  }
}
