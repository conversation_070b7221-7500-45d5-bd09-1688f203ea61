import 'dart:async';

import 'package:and/app.dart';
import 'package:and/common/res/colours.dart';
import 'package:and/common/res/text_styles.dart';
import 'package:and/eventbus/contact_sync_event.dart';
import 'package:and/module/contact/widget/ui_channel_item.dart';
import 'package:and/module/user/user_info_page.dart';
import 'package:and/widget/letter_index_bar.dart';
import 'package:and/widget/refresh/load_state.dart';
import 'package:and/widget/refresh/refresh_page.dart';
import 'package:and/widget/refresh/refresh_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:super_sliver_list/super_sliver_list.dart';
import 'package:wukongimfluttersdk/entity/channel.dart';

import 'contact_logic.dart';

class ContactPage extends StatefulWidget {
  final Function(WKChannel)? onTap;
  final bool isIncludeSystem;
  final List<String>? selectedChannelIds;
  final List<String>? unSelectChannelIds;
  final Widget? topWidget;

  const ContactPage(
      {super.key,
      this.topWidget,
      this.selectedChannelIds,
      this.unSelectChannelIds,
      this.onTap,
      this.isIncludeSystem = true});

  @override
  State<StatefulWidget> createState() {
    return ContactPageState();
  }
}

class ContactPageState extends RefreshState<ContactPage> {
  final logic = Get.put<ContactLogic>(ContactLogic());
  late final list = logic.list;
  final ScrollController scrollController = ScrollController();
  final ListController listController = ListController();

  late StreamSubscription _contactStatusEventSubscription;

  @override
  void initState() {
    logic.isIncludeSystem = widget.isIncludeSystem;
    super.initState();

    _contactStatusEventSubscription =
        eventBus.on<ContactSyncEvent>().listen((event) {
      if (mounted) {
        logic.refreshData();
      }
    });
  }

  @override
  void dispose() {
    _contactStatusEventSubscription.cancel();
    super.dispose();

    Get.delete<ContactLogic>();
  }

  @override
  Rx<FitLoadStatus> getLoadState() {
    return logic.loadStatus;
  }

  @override
  void didUpdateWidget(covariant ContactPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (mounted) {
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Obx(() => _buildRefreshList());
  }

  Widget _buildRefreshList() {
    return Stack(children: [
      buildRefreshWidget(
        refreshController: refreshController,
        loadStatus: logic.loadStatus.value,
        hasData: logic.letters.isNotEmpty,
        builder: (physics) => _buildList(physics),
        onRefresh: () {
          logic.refreshData();
        },
      ),
      Positioned(
        right: 0,
        top: 0,
        bottom: 0,
        child: Center(
          child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
              margin: const EdgeInsets.symmetric(horizontal: 0),
              decoration: BoxDecoration(
                color: Colors.black.withAlpha(1),
                borderRadius: BorderRadius.circular(20),
              ),
              child: AlphabetScrollbar(
                selectedLetterSize: 30,
                factor: 0,
                selectedLetterAdditionalSpace: 30,
                onLetterChange: (value) {
                  int position = logic.letters.indexWhere((e) => e == value);
                  if (position >= 0) {
                    listController.jumpToItem(
                      index: position,
                      scrollController: scrollController,
                      alignment: 0,
                    );
                  }
                },
              )),
        ),
      ),
    ]);
  }

  Widget _buildList(ScrollPhysics? physics) {
    return CustomScrollView(
      controller: scrollController,
      physics: physics,
      slivers: [
        SliverToBoxAdapter(
          child: widget.topWidget ?? SizedBox(),
        ),
        SliverPadding(
          padding: const EdgeInsets.symmetric(vertical: 10),
          sliver: SuperSliverList(
            listController: listController,
            delegate: SliverChildBuilderDelegate((context, index) {
              String letter = logic.letters[index];
              List<WKChannel> channels = logic.groupedChannels[letter] ?? [];
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 20, vertical: 4),
                    child: Text(
                      letter,
                      style: TextStyles.fontSize14Bold
                          .copyWith(color: DColor.primaryColor),
                    ),
                  ),
                  ListView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: channels.length,
                    itemBuilder: (context, channelIndex) {
                      final channel = channels[channelIndex];
                      var isSelected = widget.selectedChannelIds
                              ?.contains(channel.channelID) ??
                          false;
                      return UiChannelItem(
                          channel: channel,
                          isSelected: isSelected,
                          onTap: () {
                            if (widget.onTap != null) {
                              //不可选中的
                              if (widget.unSelectChannelIds
                                      ?.contains(channel.channelID) ??
                                  false) {
                                return false;
                              }

                              widget.onTap!(channel);
                            } else {
                              UserInfoPage.open(
                                  channelID: channel.channelID,
                                  channelType: channel.channelType);
                            }
                          });
                    },
                  ),
                ],
              );
            }, childCount: logic.letters.length),
          ),
        )
      ],
    );
  }

  void search(String keyword) {
    logic.onSearch(keyword);
  }
}
