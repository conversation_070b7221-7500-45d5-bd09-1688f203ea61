import 'package:and/common/res/colours.dart';
import 'package:and/common/res/text_styles.dart';
import 'package:and/constant/wk_system_account.dart';
import 'package:and/model/extension/wk_channel_ext.dart';
import 'package:and/utils/image_path.dart';
import 'package:flutter/material.dart';
import 'package:wukongimfluttersdk/entity/channel.dart';

class UiChannelNameWidget extends StatefulWidget {
  final WKChannel channel;
  final double? textSize;

  const UiChannelNameWidget({super.key, required this.channel, this.textSize});

  @override
  State<StatefulWidget> createState() {
    return _UiChannelNameWidgetState();
  }
}

class _UiChannelNameWidgetState extends State<UiChannelNameWidget> {
  late Future<String?> _fromName;

  @override
  void initState() {
    super.initState();
    _fromName = widget.channel.displayNameAsync;
  }

  @override
  void didUpdateWidget(covariant UiChannelNameWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    _fromName = widget.channel.displayNameAsync;
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      child: FutureBuilder<String?>(
        future: _fromName,
        builder: (BuildContext context, AsyncSnapshot<String?> snapshot) {
          return Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildAnonymousTag(),
              Flexible(
                  child: Text(
                snapshot.data ?? '',
                style: TextStyles.fontSize16Normal.copyWith(
                  fontSize: widget.textSize ?? 17,
                ),
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              )),
              _buildCategoryTag()
            ],
          );
        },
      ),
    );
  }

  Widget _buildAnonymousTag() {
    if (widget.channel.anonymous == 1) {
      return Padding(
          padding: EdgeInsets.symmetric(horizontal: 2),
          child: Image.asset(
            ImagePath.ic_anonymous,
            width: 18,
            fit: BoxFit.contain,
            color: DColor.primaryColor,
          ));
    }
    return const SizedBox(width: 0);
  }

  Widget _buildCategoryTag() {
    if (widget.channel.category == WKSystemAccount.accountCategoryVisitor) {
      return Icon(
        Icons.person_outline,
        size: 20,
        color: Colors.grey,
      );
    } else if (widget.channel.category ==
        WKSystemAccount.accountCategorySystem) {
      return Icon(
        Icons.verified_user,
        size: 18,
        color: Colors.blue,
      );
    }
    return const SizedBox(width: 0);
  }
}
