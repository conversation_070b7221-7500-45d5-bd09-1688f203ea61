import 'package:and/common/res/colours.dart';
import 'package:and/common/res/text_styles.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/model/friend_apply.dart';
import 'package:and/module/user/user_info_page.dart';
import 'package:and/utils/common_helper.dart';
import 'package:and/widget/avatar_widget.dart';
import 'package:and/widget/submit_button.dart';
import 'package:flutter/material.dart';

class UiFriendApplyItem extends StatefulWidget {
  final FriendApply item;
  final Function() onAgreeApplyTap;

  const UiFriendApplyItem({
    super.key,
    required this.item,
    required this.onAgreeApplyTap,
  });

  @override
  State<StatefulWidget> createState() {
    return _UiFriendApplyItemState();
  }
}

class _UiFriendApplyItemState extends State<UiFriendApplyItem> {

  @override
  void didUpdateWidget(covariant UiFriendApplyItem oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.item != oldWidget.item) {
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
        color: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
        child: Row(
          children: [
            AvatarWidget(
              CommonHelper.getAvatarUrl(widget.item.toUid),
              size: 40,
              fontSize: 14,
            ),
            SizedBox(width: 10),
            Expanded(
                child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                  Text(
                    widget.item.toName,
                    style: TextStyles.fontSize16Normal,
                    maxLines: 1,
                  ),
                  Text(
                    (widget.item.remark.isNotEmpty)
                        ? widget.item.remark
                        : context.l10n.requestAddFriend,
                    style: TextStyles.fontSize13Normal.copyWith(
                      color: DColor.secondaryTextColor,
                    ),
                    maxLines: 1,
                  )
                ])),
            Visibility(
                visible: widget.item.status == FriendApplyStatus.agree,
                child: SubmitButton(
                  text: context.l10n.agreedApply,
                  onPressed: () async {
                    UserInfoPage.open(channelID: widget.item.toUid);
                  },
                )),
            Visibility(
                visible: widget.item.status == FriendApplyStatus.pending,
                child: SubmitButton(
                  text: context.l10n.agreeApply,
                  onPressed: () async {
                    widget.onAgreeApplyTap();
                  },
                )),
            Visibility(
                visible: widget.item.status == FriendApplyStatus.expired,
                child: SubmitButton(
                  text: context.l10n.applyExpired,
                  textColor: Colors.white,
                  backgroundColor: Colors.grey,
                  onPressed: () async {
                    UserInfoPage.open(channelID: widget.item.toUid);
                  },
                )),
          ],
        ));
  }
}
