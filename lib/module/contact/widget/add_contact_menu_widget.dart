import 'package:and/cache/cache_helper.dart';
import 'package:and/common/res/colours.dart';
import 'package:and/common/res/text_styles.dart';
import 'package:and/common/utils/easy_loading_helper.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/model/extension/wk_channel_ext.dart';
import 'package:and/module/chat/chat_page.dart';
import 'package:and/module/contact/choose/choose_contact_page.dart';
import 'package:and/router/router.dart';
import 'package:and/utils/common_helper.dart';
import 'package:and/utils/http_utils.dart';
import 'package:and/utils/image_path.dart';
import 'package:and/utils/scan_utils.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:wukongimfluttersdk/entity/channel.dart';

enum AddContactMenu {
  groupChat,
  scan,
  addFriend,
}

class AddContactMenuWidget extends StatelessWidget {
  const AddContactMenuWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return PopupMenuButton<AddContactMenu>(
      onSelected: handleClick,
      icon: const Icon(Icons.add),
      itemBuilder: (context) => [
        PopupMenuItem<AddContactMenu>(
            value: AddContactMenu.groupChat,
            child: Row(
              children: [
                Image.asset(ImagePath.ic_menu_chats,
                    height: 25,
                    width: 25,
                    color: DColor.primaryTextColor,
                    fit: BoxFit.fitHeight),
                // const Icon(Icons.refresh),
                const SizedBox(
                  width: 15,
                ),
                Text(context.l10n.startGroupChat,
                    style: TextStyles.fontSize16Normal)
              ],
            )),
        PopupMenuItem<AddContactMenu>(
            value: AddContactMenu.scan,
            child: Row(
              children: [
                Image.asset(ImagePath.ic_menu_scan,
                    height: 25,
                    width: 25,
                    color: DColor.primaryTextColor,
                    fit: BoxFit.fitHeight),
                // const Icon(Icons.refresh),
                const SizedBox(
                  width: 15,
                ),
                Text(context.l10n.scan, style: TextStyles.fontSize16Normal)
              ],
            )),
        PopupMenuItem<AddContactMenu>(
            value: AddContactMenu.addFriend,
            child: Row(
              children: [
                Image.asset(ImagePath.ic_menu_invite,
                    height: 25,
                    width: 25,
                    color: DColor.primaryTextColor,
                    fit: BoxFit.fitHeight),
                const SizedBox(
                  width: 15,
                ),
                Text(context.l10n.addFriend, style: TextStyles.fontSize16Normal)
              ],
            )),
      ],
    );
  }

  void handleClick(AddContactMenu item) async {
    switch (item) {
      case AddContactMenu.groupChat:
        _groupChat();
        break;
      case AddContactMenu.scan:
        _scanContact();
        break;
      case AddContactMenu.addFriend:
        _addFriend();
        break;
    }
  }

  void _groupChat() async {
    var chooseUsers =
        await ChooseContactPage.open() ?? <WKChannel>[];
    if (chooseUsers.isEmpty) {
      return;
    }
    var name = chooseUsers.map((e) => e.channelName).join(',');
    name = "${CacheHelper.userProfile?.name ?? ""},$name";
    var ids = chooseUsers.map((e) => e.channelID).toList();
    var names = chooseUsers.map((e) => e.channelName).toList();
    EasyLoadingHelper.show(onAction: () async {
      var channel = await HttpUtils.createGroup(name, ids, names);
      if (channel != null) {
        ChatPage.open(
            channelID: channel.channelID, channelType: channel.channelType);
      }
    });
  }

  void _scanContact() async {
    CommonHelper.scanContact();
  }

  void _addFriend() async {
    Get.toNamed(RouteGet.addFriend);
  }
}
