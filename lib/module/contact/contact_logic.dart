import 'package:and/model/extension/wk_channel_ext.dart';
import 'package:and/module/common/controller/list_controller.dart';
import 'package:and/utils/pinyin_utils.dart';
import 'package:get/get.dart';
import 'package:wukongimfluttersdk/entity/channel.dart';
import 'package:wukongimfluttersdk/type/const.dart';
import 'package:wukongimfluttersdk/wkim.dart';

class ContactLogic extends ListController<WKChannel> {
  final String key = "ui_contact";
  final RxMap<String, List<WKChannel>> groupedChannels =
      <String, List<WKChannel>>{}.obs;
  final RxList<String> letters = <String>[].obs;
  final filterKey = "".obs;

  var isIncludeSystem = true;

  ContactLogic() {
    ever(list, (value) {
      _group();
    });
    ever(filterKey, (value) {
      _group();
    });
  }

  @override
  void onReady() {
    super.onReady();
    _initListener();
  }

  @override
  void onClose() {
    _removeListener();
    super.onClose();
  }

  @override
  Future<List<WKChannel>?> loadData() async {
    return await WKIM.shared.channelManager
        .getWithFollowAndStatus(WKChannelType.personal, 1, 1);
  }

  void _group() {
    var filterItems = <WKChannel>[];
    filterItems.addAll(list.where((e) {
      if (e.isSystemChannel && !isIncludeSystem) {
        return false;
      }
      if (filterKey.isNotEmpty) {
        return e.displayName.contains(filterKey.value);
      }
      return true;
    }));
    // 按首字母排序
    filterItems.sort((a, b) {
      var aLetter = PinyinUtils.getFirstLetter(a.displayName);
      var bLetter = PinyinUtils.getFirstLetter(b.displayName);
      return aLetter.compareTo(bLetter);
    });
    // 分组
    groupedChannels.clear();
    for (var item in filterItems) {
      var firstLetter = PinyinUtils.getFirstLetter(item.displayName);
      groupedChannels.putIfAbsent(firstLetter, () => []).add(item);
    }
    // 更新字母列表
    letters.value = groupedChannels.keys.toList()..sort();
  }

  void onSearch(String key) {
    filterKey.value = key;
  }

  _initListener() {
    // 监听刷新channel资料事件
    WKIM.shared.channelManager.addOnRefreshListener(key, (channel) {
      var index = list.indexWhere((msg) =>
          msg.channelID == channel.channelID &&
          msg.channelType == channel.channelType);
      if (index != -1) {
        if (channel.follow == 0 || channel.isDeleted == 1) {
          list.removeAt(index);
        } else {
          list[index] = channel;
        }
      }
    });
  }

  _removeListener() {
    WKIM.shared.channelManager.removeOnRefreshListener(key);
  }
}
