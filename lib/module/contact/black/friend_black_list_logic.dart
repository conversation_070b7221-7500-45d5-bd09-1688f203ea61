import 'package:and/http/my_http.dart';
import 'package:and/io/friend.dart';
import 'package:and/model/friend_apply.dart';
import 'package:and/model/request/sure_friend_request.dart';
import 'package:and/module/common/controller/list_controller.dart';
import 'package:wukongimfluttersdk/entity/channel.dart';
import 'package:wukongimfluttersdk/type/const.dart' show WKChannelType;
import 'package:wukongimfluttersdk/wkim.dart';

class FriendBlackListLogic extends ListController<WKChannel> {
  final String key = 'black_friends';

  FriendBlackListLogic();

  @override
  void onReady() {
    super.onReady();
    _initListener();
  }

  @override
  void onClose() {
    _removeListener();
    super.onClose();
  }

  @override
  Future<List<WKChannel>?> loadData() async {
    return await WKIM.shared.channelManager
        .getWithFollowAndStatus(WKChannelType.personal, 1, 2);
  }

  _initListener() {
    // 监听刷新channel资料事件
    WKIM.shared.channelManager.addOnRefreshListener(key, (channel) {
      print("刷新channel资料事件");
      refreshData();
    });
  }

  _removeListener() {
    WKIM.shared.channelManager.removeOnRefreshListener(key);
  }
}
