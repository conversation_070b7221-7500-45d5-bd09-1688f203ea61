import 'dart:async';

import 'package:and/app.dart';
import 'package:and/eventbus/contact_sync_event.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/module/contact/widget/ui_friend_black_item.dart';
import 'package:and/module/user/user_info_page.dart';
import 'package:and/utils/http_utils.dart';
import 'package:and/widget/refresh/load_state.dart';
import 'package:and/widget/refresh/refresh_page.dart';
import 'package:and/widget/refresh/refresh_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:super_sliver_list/super_sliver_list.dart';
import 'package:wukongimfluttersdk/entity/channel.dart';

import 'friend_black_list_logic.dart';

class FriendBlackListPage extends StatefulWidget {
  const FriendBlackListPage({super.key});

  @override
  State<StatefulWidget> createState() {
    return _FriendBlackListPageState();
  }
}

class _FriendBlackListPageState extends RefreshState<FriendBlackListPage> {
  final logic = Get.find<FriendBlackListLogic>();
  late final list = logic.list;
  final ScrollController scrollController = ScrollController();
  final ListController listController = ListController();

  late StreamSubscription _contactStatusEventSubscription;

  @override
  void initState() {
    super.initState();

    _contactStatusEventSubscription =
        eventBus.on<ContactSyncEvent>().listen((event) {
      if (mounted) {
        logic.refreshData();
      }
    });
  }

  @override
  void dispose() {
    _contactStatusEventSubscription.cancel();
    super.dispose();
  }

  @override
  Rx<FitLoadStatus> getLoadState() {
    return logic.loadStatus;
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Scaffold(
        appBar: AppBar(title: Text(context.l10n.blackFriends)),
        body: Obx(() => _buildRefreshList()));
  }

  Widget _buildRefreshList() {
    return buildRefreshWidget(
      refreshController: refreshController,
      loadStatus: logic.loadStatus.value,
      hasData: logic.list.isNotEmpty,
      builder: (physics) => _buildList(physics),
      onRefresh: () {
        logic.refreshData();
      },
    );
  }

  Widget _buildList(ScrollPhysics? physics) {
    return CustomScrollView(
      slivers: [
        SliverPadding(
          padding: const EdgeInsets.symmetric(vertical: 10),
          sliver: SuperSliverList(
            listController: listController,
            delegate: SliverChildBuilderDelegate((context, index) {
              WKChannel item = list[index];
              return UiFriendBlackItem(
                  channel: item,
                  onTap: () {
                    UserInfoPage.open(channelID: item.channelID);
                  });
            }, childCount: list.length),
          ),
        )
      ],
    );
  }
}
