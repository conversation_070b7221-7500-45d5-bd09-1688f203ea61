import 'dart:async';

import 'package:and/app.dart';
import 'package:and/bloc/navigation/navi_count_bloc.dart';
import 'package:and/bloc/navigation/navi_count_state.dart';
import 'package:and/common/extension/common_ext.dart';
import 'package:and/common/res/colours.dart';
import 'package:and/common/res/text_styles.dart';
import 'package:and/eventbus/contact_sync_event.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/module/contact/contact_page.dart';
import 'package:and/module/contact/widget/ui_channel_item.dart';
import 'package:and/module/main/main_tab_page.dart';
import 'package:and/module/user/user_info_page.dart';
import 'package:and/router/router.dart';
import 'package:and/utils/image_path.dart';
import 'package:and/widget/letter_index_bar.dart';
import 'package:and/widget/refresh/load_state.dart';
import 'package:and/widget/refresh/refresh_page.dart';
import 'package:and/widget/refresh/refresh_widget.dart';
import 'package:and/widget/unread_count_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:get/get.dart';
import 'package:wukongimfluttersdk/entity/channel.dart';
import 'package:super_sliver_list/super_sliver_list.dart';

import 'contact_logic.dart';
import 'widget/add_contact_menu_widget.dart';

class HomeContactPage extends StatefulWidget {
  const HomeContactPage({super.key});

  @override
  State<StatefulWidget> createState() {
    return _HomeContactPageState();
  }
}

class _HomeContactPageState extends State<HomeContactPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: Text(context.l10n.tabbarContacts),
          titleSpacing: 20,
          actions: [
            Padding(
                padding: EdgeInsets.only(right: 15),
                child: AddContactMenuWidget())
          ],
        ),
        body: _buildList());
  }

  Widget _buildList() {
    return ContactPage(
      topWidget: Padding(
        padding: EdgeInsets.symmetric(horizontal: 15),
        child: Column(
          children: [
            _buildNewFriends(),
            SizedBox(height: 10),
            _buildSavedGroups(),
            SizedBox(height: 10),
            _buildBlackFriends(),
          ],
        ),
      ),
    );
  }

  Widget _buildNewFriends() {
    return BlocBuilder<NaviCountBloc, NaviCountState>(
      builder: (context, state) {
        var count = state.counts[TabType.contacts] ?? 0;
        return _buildItems(
          context.l10n.newFriends,
          ImagePath.ic_new_friends,
          count: count,
          onTap: () {
            Get.toNamed(RouteGet.applyFriend);
          },
        );
      },
    );
  }

  Widget _buildSavedGroups() {
    return _buildItems(
      context.l10n.savedGroups,
      ImagePath.ic_saved_grouds,
      onTap: () {
        Get.toNamed(RouteGet.myGroups);
      },
    );
  }

  Widget _buildBlackFriends() {
    return _buildItems(
        context.l10n.blackFriends,
        ImagePath.ic_black_friends,
        onTap: () {
          Get.toNamed(RouteGet.blackFriend);
        },
    );
  }

  Widget _buildItems(String name, String icon,
      {int count = 0, Function()? onTap}) {
    return InkWell(
      onTap: onTap,
      child: Row(
        children: [
          Image.asset(icon, height: 40),
          SizedBox(width: 10),
          Text(name, style: TextStyles.fontSize16Normal),
          SizedBox(width: 8),
          UnreadCountWidget(count)
        ],
      ),
    );
  }
}
