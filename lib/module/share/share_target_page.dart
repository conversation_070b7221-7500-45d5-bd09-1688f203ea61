import 'package:and/cache/cache_helper.dart';
import 'package:and/common/res/colours.dart';
import 'package:and/common/res/text_styles.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/module/search/globalSearch/global_search_logic.dart';
import 'package:and/module/search/globalSearch/global_search_page.dart';
import 'package:and/router/router.dart';
import 'package:and/utils/share_utils.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:oktoast/oktoast.dart';
import 'package:receive_sharing_intent/receive_sharing_intent.dart';
import 'package:wukongimfluttersdk/entity/channel.dart';
import 'package:wukongimfluttersdk/type/const.dart';

class ShareTargetArgument {
  final List<SharedMediaFile> sharedFiles;
  
  ShareTargetArgument({required this.sharedFiles});
  
  factory ShareTargetArgument.fromGet() {
    return Get.arguments as ShareTargetArgument;
  }
}

class ShareTargetPage extends StatelessWidget {
  const ShareTargetPage({Key? key}) : super(key: key);
  
  static Future<void> open(List<SharedMediaFile> sharedFiles) async {
    await Get.toNamed(RouteGet.shareTarget,
      arguments: ShareTargetArgument(sharedFiles: sharedFiles));
  }

  @override
  Widget build(BuildContext context) {
    final argument = ShareTargetArgument.fromGet();
    
    return Scaffold(
      appBar: AppBar(
        title: Text(context.l10n.sharedFromExternalApp),
        centerTitle: true,
      ),
      body: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            _buildOptionButton(
              context,
              title: context.l10n.shareToSelf,
              icon: Icons.person,
              onTap: () => _sendToSelf(context, argument.sharedFiles),
            ),
            const SizedBox(height: 20),
            _buildOptionButton(
              context,
              title: context.l10n.shareToFriendsOrGroups,
              icon: Icons.people,
              onTap: () => _sendToOthers(context, argument.sharedFiles),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildOptionButton(BuildContext context, {
    required String title,
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(10),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 5,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 24, color: DColor.primaryColor),
            const SizedBox(width: 10),
            Text(
              title,
              style: TextStyles.fontSize16SemiBold,
            ),
          ],
        ),
      ),
    );
  }
  
  // 发送给自己
  Future<void> _sendToSelf(BuildContext context, List<SharedMediaFile> sharedFiles) async {
    final selfChannel = await _getSelfChannel();
    if (selfChannel != null) {
      await _sendFilesToChannel(sharedFiles, [selfChannel]);
      Get.back();

      showToast(context.l10n.shareSuccess);
    }
  }
  
  // 发送给好友或群
  Future<void> _sendToOthers(BuildContext context, List<SharedMediaFile> sharedFiles) async {
    await GlobalSearchPage.open(
      isMultiSelect: false,
      searchType: GlobalSearchType.recentConversation,
      title: context.l10n.chooseContact,
      onSelectedChannel: (channels) async {
        if (channels.isNotEmpty) {
          await _sendFilesToChannel(sharedFiles, channels);
          Get.back();
          showToast(context.l10n.shareSuccess);
        }
      }
    );
  }
  
  Future<WKChannel?> _getSelfChannel() async {
    final uid = await _getSelfUID();
    if (uid != null) {
      return WKChannel(uid, WKChannelType.personal);
    }
    return null;
  }
  
  // 获取自己的UID
  Future<String?> _getSelfUID() async {
    return CacheHelper.uid;
  }
  
  // 发送文件到指定频道
  Future<void> _sendFilesToChannel(List<SharedMediaFile> sharedFiles, List<WKChannel> channels) async {
    if (channels.isEmpty || sharedFiles.isEmpty) return;

    await ShareUtils.sendFilesToChannels(sharedFiles, channels);
  }
}