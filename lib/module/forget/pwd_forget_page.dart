import 'package:and/common/res/colours.dart';
import 'package:and/common/res/text_styles.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/model/request/send_mail_request.dart';
import 'package:and/model/request/send_sms_request.dart';
import 'package:and/router/router.dart';
import 'package:and/utils/http_utils.dart';
import 'package:and/utils/image_path.dart';
import 'package:and/widget/input_form_widget.dart';
import 'package:and/widget/submit_button.dart';
import 'package:flutter/material.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:get/get.dart';

import 'pwd_forget_logic.dart';

class PwdForgetArgument {
  PwdForgetArgument();

  factory PwdForgetArgument.fromGet() {
    return (Get.arguments as PwdForgetArgument);
  }
}

class PwdForgetPage extends StatefulWidget {
  const PwdForgetPage({super.key});

  @override
  State<StatefulWidget> createState() {
    return _PwdForgetPageState();
  }
}

class _PwdForgetPageState extends State<PwdForgetPage>
    with SingleTickerProviderStateMixin {
  final argument = PwdForgetArgument.fromGet();
  final logic = Get.find<PwdForgetLogic>();
  late var resetType = logic.resetType;
  late Rx<int> secondsRemaining = logic.secondsRemaining;

  final _phoneTextController = TextEditingController();
  final _emailTextController = TextEditingController();
  final _codeTextController = TextEditingController();
  final _pwdTextController = TextEditingController();

  final GlobalKey<FormState> _phoneKey = GlobalKey<FormState>();
  final GlobalKey<FormState> _emailKey = GlobalKey<FormState>();
  final GlobalKey<FormState> _codeKey = GlobalKey<FormState>();
  final GlobalKey<FormState> _pwdKey = GlobalKey<FormState>();
  late TabController _tabController;

  @override
  void initState() {
    super.initState();

    _tabController = TabController(length: 2, vsync: this);
    _tabController.addListener(() {
      if (_tabController.index == 0) {
        logic.resetType.value = ResetPwdType.phone;
      } else {
        logic.resetType.value = ResetPwdType.email;
      }
    });
  }

  @override
  void dispose() {
    // Clean up the controller when the widget is disposed.
    _phoneTextController.dispose();
    _codeTextController.dispose();
    _emailTextController.dispose();
    _pwdTextController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Colors.white,
        body: Container(
          color: Colors.white,
          padding: const EdgeInsets.all(30),
          child: Obx(() => _buildContent()),
        ));
  }

  Widget _buildContent() {
    return SafeArea(
        child: SizedBox.expand(
      child: SingleChildScrollView(
        child: Column(
          children: [
            Image.asset(ImagePath.ic_logo, height: 120),
            const SizedBox(height: 40),
            _buildResetSwitch(),
            const SizedBox(height: 24),
            AnimatedCrossFade(
              duration: const Duration(milliseconds: 300),
              crossFadeState: resetType.value == ResetPwdType.phone
                  ? CrossFadeState.showFirst
                  : CrossFadeState.showSecond,
              firstChild: _buildPhone(),
              secondChild: _buildEmail(),
            ),
            const SizedBox(height: 20),
            _buildPassword(),
            const SizedBox(height: 20),
            Obx(() => _buildCode()),
            const SizedBox(height: 20),
            _buildResetButton(),
            const SizedBox(height: 20),
            _buildMore(),
          ],
        ),
      ),
    ));
  }

  Widget _buildResetSwitch() {
    return Container(
      height: 40,
      padding: EdgeInsets.symmetric(horizontal: 0, vertical: 0), // 适当缩小 padding
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(12),
      ),
      child: TabBar(
        controller: _tabController,
        tabs: [
          Tab(text: context.l10n.loginByPhone),
          Tab(text: context.l10n.loginByEmail),
        ],
        indicator: BoxDecoration(
          color: DColor.primaryColor,
          borderRadius: BorderRadius.circular(12),
        ),
        indicatorSize: TabBarIndicatorSize.tab,
        indicatorPadding: EdgeInsets.symmetric(horizontal: 0, vertical: 0),
        // 缩小 indicator
        labelPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 4),
        // 缩小 labelPadding
        labelColor: Colors.white,
        unselectedLabelColor: Colors.black54,
      ),
    );
  }

  Widget _buildMore() {
    return InkWell(
      onTap: () {
        Get.back();
      },
      child: Text(context.l10n.loginLoginButton,
          style:
              TextStyles.fontSize16Normal.copyWith(color: DColor.primaryColor)),
    );
  }

  Widget _buildResetButton() {
    return SubmitButton(
        enable: true,
        text: context.l10n.resetPasswordResetButton,
        textColor: Colors.white,
        backgroundColor: Colors.black,
        textSize: 20,
        onPressed: () async {
          var isPhoneType = logic.resetType.value == ResetPwdType.phone;
          var allValidList = [
            if (isPhoneType) _phoneKey else _emailKey,
            _pwdKey,
          ];

          var isValid = allValidList
              .every((element) => element.currentState?.validate() ?? false);
          if (isValid) {
            if (isPhoneType) {
              await HttpUtils.pwdForgetSms(
                  _phoneTextController.text,
                  logic.areaCode.value,
                  _pwdTextController.text,
                  _codeTextController.text);
            } else {
              await HttpUtils.pwdForgetEmail(_emailTextController.text,
                  _pwdTextController.text, _codeTextController.text);
            }
          }
        });
  }

  Widget _buildPhone() {
    return InputTextForm(
        formKey: _phoneKey,
        validator: FormBuilderValidators.phoneNumber(
            errorText: context.l10n.signupPhoneErrorFormat),
        hint: context.l10n.signupPhonePlaceholder,
        value: "",
        keyboardType: TextInputType.phone,
        controller: _phoneTextController,
        prefixIcon: InkWell(
          onTap: () async {
            var code = await Get.toNamed(RouteGet.chooseCountry);
            logic.setAreaCode(code);
          },
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 8),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(logic.areaCodeDisplay),
                Icon(Icons.arrow_drop_down)
              ],
            ),
          ),
        ),
        onChanged: (value) {
          setState(() {});
        });
  }

  Widget _buildEmail() {
    return InputTextForm(
        formKey: _emailKey,
        validator: FormBuilderValidators.email(
            errorText: context.l10n.signupEmailErrorFormat),
        hint: context.l10n.signupEmailPlaceholder,
        value: _emailTextController.text,
        keyboardType: TextInputType.emailAddress,
        controller: _emailTextController,
        onChanged: (value) {
          setState(() {});
        });
  }

  Widget _buildPassword() {
    return InputTextForm(
        formKey: _pwdKey,
        validator: FormBuilderValidators.minLength(6,
            errorText: context.l10n.signupPasswordLengthError(6)),
        hint: context.l10n.signupPasswordPlaceholder,
        value: "",
        obscureText: true,
        keyboardType: TextInputType.text,
        controller: _pwdTextController,
        onChanged: (value) {
          setState(() {});
        });
  }

  Widget _buildCode() {
    var remainTime = secondsRemaining.value;

    var isEnable = ((logic.resetType.value == ResetPwdType.phone &&
                _phoneTextController.text.isNotEmpty) ||
            (logic.resetType.value == ResetPwdType.email &&
                _emailTextController.text.isNotEmpty)) &&
        remainTime <= 0;
    return Row(
      children: [
        Expanded(
            child: InputTextForm(
                formKey: _codeKey,
                validator: FormBuilderValidators.required(
                    errorText: context.l10n.signupVerifyCodeHint),
                hint: context.l10n.signupVerifyCodeHint,
                value: "",
                keyboardType: TextInputType.text,
                controller: _codeTextController,
                onChanged: (value) {
                  setState(() {});
                })),
        SizedBox(width: 10),
        SubmitButton(
            enable: isEnable,
            text: remainTime <= 0
                ? context.l10n.sendCode
                : context.l10n.secondsRemaining(remainTime),
            textColor: Colors.white,
            backgroundColor: Colors.black,
            textSize: 14,
            onPressed: () async {
              var isPhoneType = logic.resetType.value == ResetPwdType.phone;
              var allValidList = [
                if (isPhoneType) _phoneKey else _emailKey
              ];

              var isValid = allValidList.every(
                  (element) => element.currentState?.validate() ?? false);
              if (isValid) {
                if (isPhoneType) {
                  await HttpUtils.sendForgetSms(
                      zone: logic.areaCode.value,
                      phone: _phoneTextController.text,
                      onSuccess: () {
                        logic.startCountdown();
                      });
                } else {
                  await HttpUtils.sendMail(_emailTextController.text,
                      onSuccess: () {
                    logic.startCountdown();
                  }, mailType: SendMailType.resetPwd);
                }
              }
            })
      ],
    );
  }
}
