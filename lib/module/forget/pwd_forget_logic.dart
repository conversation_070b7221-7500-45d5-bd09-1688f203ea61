import 'dart:async';

import 'package:and/app.dart';
import 'package:and/common/utils/easy_loading_helper.dart';
import 'package:and/http/my_http.dart';
import 'package:and/io/share.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/l10n/language_utils.dart';
import 'package:and/model/request/send_mail_request.dart';
import 'package:country_codes/country_codes.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';

enum ResetPwdType {
  phone,
  email,
}
class PwdForgetLogic extends GetxController {
  final areaCode = "0086".obs;
  String get areaCodeDisplay => "+${areaCode.value.substring(2)}";
  final resetType = ResetPwdType.phone.obs;

  Timer? _timer;
  final Rx<int> secondsRemaining = Rx<int>(-1);

  @override
  void onReady() async{
    super.onReady();
    await CountryCodes.init();
    final CountryDetails details = CountryCodes.detailsForLocale(LanguageUtils.getDeviceLocale());
    var dialCode = details.dialCode;
    if (dialCode != null) {
      areaCode.value = dialCode.replaceAll("+", "00");
    }
  }

  void startCountdown() {
    secondsRemaining.value = 60;
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (secondsRemaining.value > 0) {
        secondsRemaining.value--;
      } else {
        _timer?.cancel();
      }
    });
  }

  void stopCountdown() {
    _timer?.cancel();
  }

  void setAreaCode(String? code) {
    if (code == null) return;
    areaCode.value = code;
  }

  @override
  void dispose() {
    stopCountdown();
    super.dispose();
  }
}
