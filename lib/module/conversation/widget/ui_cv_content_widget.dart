
import 'package:and/cache/cache_helper.dart';
import 'package:and/common/res/colours.dart';
import 'package:and/common/res/text_styles.dart';
import 'package:and/constant/wk_channel_member_role.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/model/extension/wk_channel_ext.dart';
import 'package:and/model/extension/wk_msg_ext.dart';
import 'package:and/utils/lottie_path.dart';
import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';
import 'package:wukongimfluttersdk/entity/conversation.dart';
import 'package:wukongimfluttersdk/entity/reminder.dart';
import 'package:wukongimfluttersdk/type/const.dart';
import 'package:wukongimfluttersdk/wkim.dart';

class UiCvContentWidget extends StatefulWidget {
  final WKUIConversationMsg msg;

  const UiCvContentWidget({
    super.key,
    required this.msg,
  });

  @override
  State<StatefulWidget> createState() {
    return _UiCvContentWidgetState();
  }
}

class _UiCvContentWidgetState extends State<UiCvContentWidget> {
  late Future<List<InlineSpan>> _contentFuture;

  @override
  void initState() {
    super.initState();
    _contentFuture = getContentText();
  }

  @override
  void didUpdateWidget(covariant UiCvContentWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    _contentFuture = getContentText();
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<List<InlineSpan>>(
      future: _contentFuture,
      builder: (BuildContext context, AsyncSnapshot<List<InlineSpan>> snapshot) {
        return RichText(
          textScaler: MediaQuery.of(context).textScaler,
          overflow: TextOverflow.ellipsis,
          maxLines: 1,
          text: TextSpan(
            children: snapshot.data ?? [],
          ),
        );
      },
    );
  }

  Future<List<InlineSpan>> getContentText() async {
    var msg = widget.msg;
    String draft = "";
    String approveContent = "";
    bool mention = false;

    var reminders = await msg.getReminderList();
    if (reminders != null && reminders.isNotEmpty) {
      for (var reminder in reminders) {
        if (!mention &&
            reminder.type == WKMentionType.wkReminderTypeMentionMe &&
            reminder.done == 0) {
          //存在@
          mention = true;
        }
        if (reminder.type == WKMentionType.wkApplyJoinGroupApprove &&
            reminder.done == 0) {
          //存在审批
          approveContent = context.l10n.applyJoinGroup;
        }
      }
    }
    draft = msg.getRemoteMsgExtra()?.draft ?? '';
    var isSetChatPwd = (await msg.getWkChannel())?.chatPwdOn == 1;
    if (isSetChatPwd) {
      if (draft.isNotEmpty) {
        draft = "❊❊❊❊❊❊❊❊❊❊❊❊❊";
      }
    }

    var textSpans = <InlineSpan>[];
    if (mention) {
      textSpans.add(TextSpan(
          text: "${context.l10n.lastMsgRemind} ",
          style:
              TextStyles.fontSize14Bold.copyWith(color: DColor.primaryColor)));
    }
    if (draft.isNotEmpty) {
      textSpans.add(TextSpan(
          text: "${context.l10n.lastMsgDraft} ",
          style: TextStyles.fontSize15Normal
              .copyWith(color: DColor.primaryColor)));

      textSpans.add(TextSpan(
          text: draft,
          style: TextStyles.fontSize15Normal.copyWith(color: Colors.grey)));
    } else {
      var wkMsg = await msg.getWkMsg();
      var content = await wkMsg?.convContent;
      if (wkMsg?.status == WKSendMsgResult.sendFail) {
        textSpans.add(WidgetSpan(
             child: Lottie.asset(LottiePath.error, repeat: false, height: 16)));
      }
      textSpans.add(TextSpan(
          text: content,
          style: TextStyles.fontSize15Normal.copyWith(color: Colors.grey)));
    }

    if (approveContent.isNotEmpty) {
      textSpans.add(TextSpan(
          text: approveContent,
          style: TextStyles.fontSize15Normal
              .copyWith(color: DColor.primaryColor)));
    }

    return textSpans;
  }
}
