import 'package:and/model/extension/wk_channel_ext.dart';
import 'package:and/widget/avatar_widget.dart';
import 'package:flutter/cupertino.dart';
import 'package:wukongimfluttersdk/entity/channel.dart';
import 'package:wukongimfluttersdk/entity/conversation.dart';

class UiCvChannelAvatarWidget extends StatelessWidget {
  final WKChannel? channel;

  const UiCvChannelAvatarWidget({
    super.key,
    required this.channel,
  });

  @override
  Widget build(BuildContext context) {
    return AvatarWidget(
      channel?.avatarUrl,
      online: channel?.online == 1,
      lastOffline: channel?.lastOffline,
      size: 50,
      fontSize: 14,
    );
  }
}
