import 'package:and/common/res/text_styles.dart';
import 'package:and/model/extension/wk_channel_ext.dart';
import 'package:and/module/contact/widget/ui_channel_name_widget.dart';
import 'package:and/module/conversation/widget/ui_cv_content_widget.dart';
import 'package:and/utils/image_path.dart';
import 'package:and/utils/svg_path.dart';
import 'package:and/utils/time_utils.dart';
import 'package:and/widget/unread_count_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:wukongimfluttersdk/entity/channel.dart';
import 'package:wukongimfluttersdk/entity/conversation.dart';
import 'package:wukongimfluttersdk/type/const.dart';
import 'package:wukongimfluttersdk/wkim.dart';

import 'ui_cv_channel_avatar_widget.dart';

class UiConversationItem extends StatefulWidget {
  final WKUIConversationMsg msg;
  final Function() onTap;
  final Function(LongPressStartDetails) onLongPressTap;

  const UiConversationItem({
    super.key,
    required this.msg,
    required this.onTap,
    required this.onLongPressTap,
  });

  @override
  State<StatefulWidget> createState() {
    return _UiConversationItemState();
  }
}

class _UiConversationItemState extends State<UiConversationItem> {
  late Future<WKChannel> _channelFuture;

  @override
  void initState() {
    super.initState();
    _channelFuture = getChannel();
  }

  @override
  void didUpdateWidget(covariant UiConversationItem oldWidget) {
    super.didUpdateWidget(oldWidget);
    _channelFuture = getChannel();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: widget.onTap,
      onLongPressStart: widget.onLongPressTap,
      child: FutureBuilder<WKChannel>(
        future: _channelFuture,
        builder: (BuildContext context, AsyncSnapshot<WKChannel> snapshot) {
          return _buildRow(snapshot.data ??
              WKChannel(widget.msg.channelID, widget.msg.channelType));
        },
      ),
    );
  }

  Widget _buildRow(WKChannel channel) {
    var isTop = channel.top == 1;
    var isMute = channel.mute == 1;
    return Container(
      color: isTop ? Colors.grey[200] : Colors.white,
      padding: const EdgeInsets.all(10),
      child: Row(
        children: [
          UiCvChannelAvatarWidget(channel: channel),
          const SizedBox(width: 10),
          Expanded(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: <Widget>[
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: <Widget>[
                    _buildGroupIcon(channel),
                    Expanded(
                      child: Row(
                        children: <Widget>[
                          Flexible(
                              child: UiChannelNameWidget(
                            channel: channel,
                          )),
                          SizedBox(width: 4),
                          if (isMute)
                            Image.asset(
                              ImagePath.ic_list_mute,
                              height: 15,
                              color: Colors.grey,
                            )
                        ],
                      ),
                    ),
                    const SizedBox(width: 5),
                    Text(
                      TimeUtils.getNewChatTime(widget.msg.lastMsgTimestamp),
                      style: TextStyles.fontSize16Normal
                          .copyWith(color: Colors.grey),
                      textAlign: TextAlign.right,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
                Row(
                  children: [
                    Expanded(child: UiCvContentWidget(msg: widget.msg)),
                    UnreadCountWidget(
                      widget.msg.unreadCount,
                      backgroundColor: isMute ? Colors.grey : Colors.red,
                    ),
                  ],
                )
              ],
            ),
          )
        ],
      ),
    );
  }

  Future<WKChannel> getChannel() async {
    var channel = await widget.msg.getWkChannel();
    if (channel == null || channel.displayName.isEmpty) {
      WKIM.shared.channelManager
          .fetchChannelInfo(widget.msg.channelID, widget.msg.channelType);
    }
    return channel ?? WKChannel(widget.msg.channelID, widget.msg.channelType);
  }

  Widget _buildGroupIcon(WKChannel? channel) {
    if (channel?.channelType == WKChannelType.group) {
      return SvgPicture.asset(
        SvgPath.group_tag,
        width: 18,
        fit: BoxFit.contain,
      );
    }
    return SizedBox(width: 0);
  }
}
