import 'package:and/app.dart';
import 'package:and/common/res/colours.dart';
import 'package:and/common/res/text_styles.dart';
import 'package:and/common/utils/easy_loading_helper.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/module/chat/chat_page.dart';
import 'package:and/utils/http_utils.dart';
import 'package:and/widget/input_form_widget.dart';
import 'package:and/widget/submit_button.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:oktoast/oktoast.dart';
import 'package:wukongimfluttersdk/type/const.dart';

class SayHelloPage extends StatefulWidget {
  final String channelId;
  final String? vercode;
  final String? remark;

  const SayHelloPage(
      {super.key, required this.channelId, this.vercode, this.remark});

  @override
  State<StatefulWidget> createState() {
    return _SayHelloPageState();
  }
}

class _SayHelloPageState extends State<SayHelloPage> {
  final _remarkTextController = TextEditingController();
  final GlobalKey<FormState> _remarkKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    _remarkTextController.text = widget.remark ?? "";
  }

  @override
  void dispose() {
    // Clean up the controller when the widget is disposed.
    _remarkTextController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: Text(context.l10n.sayHello),
          actions: [
            Padding(
              padding: EdgeInsets.only(right: 15),
              child: _buildSubmitAction(),
            )
          ],
        ),
        body: Padding(
          padding: EdgeInsets.all(15),
          child: _buildContent(),
        ));
  }

  Widget _buildSubmitAction() {
    return SubmitButton(
        enable: _remarkTextController.text.isNotEmpty,
        padding: EdgeInsets.symmetric(horizontal: 10, vertical: 6),
        onPressed: () {
          _sayHello();
        },
        text: context.l10n.globalConfirm);
  }

  Widget _buildContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(context.l10n.sayHelloTip,
            style:
                TextStyles.fontSize15Normal.copyWith(color: DColor.hintText)),
        SizedBox(height: 10),
        InputTextForm(
            formKey: _remarkKey,
            value: widget.remark ?? "",
            minLines: 4,
            borderRadius: 2,
            showClear: false,
            keyboardType: TextInputType.emailAddress,
            controller: _remarkTextController,
            onChanged: (value) {
              setState(() {});
            })
      ],
    );
  }

  void _sayHello() async {
    EasyLoadingHelper.show(onAction: () async {
      var applyResult = await HttpUtils.talkToAnyOne(
              widget.channelId, _remarkTextController.text)
          .catchError((e) {
        showToast(context.l10n.msgSendFail);
        return false;
      });
      if (applyResult) {
        Get.back();

        ChatPage.open(
            channelID: widget.channelId, channelType: WKChannelType.personal);
      } else {
        showToast(context.l10n.msgSendFail);
      }
    });
  }
}
