import 'dart:io';
import 'dart:ui' as ui;
import 'package:and/cache/cache_helper.dart';
import 'package:and/common/res/colours.dart';
import 'package:and/common/res/text_styles.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/module/user/qr/user_qr_logic.dart';
import 'package:and/utils/common_helper.dart';
import 'package:and/utils/image_utils.dart';
import 'package:and/widget/avatar_widget.dart';
import 'package:extended_image/extended_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';
import 'package:pretty_qr_code/pretty_qr_code.dart';
import 'package:path_provider/path_provider.dart';

class UserQrPage extends StatefulWidget {
  const UserQrPage({super.key});

  @override
  State<StatefulWidget> createState() {
    return _UserQrPageState();
  }
}

class _UserQrPageState extends State<UserQrPage> {
  late UserQrLogic logic = Get.find<UserQrLogic>();
  late final item = logic.data;
  final GlobalKey _qrCodeKey = GlobalKey();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: Text(context.l10n.myQrcode),
          actions: [
            Padding(
              padding: EdgeInsets.only(right: 8.0),
              child: IconButton(
                icon: Icon(Icons.save_alt),
                onPressed: () => _saveQrCode(),
              ),
            ),
          ],
        ),
        body: Obx(() => SafeArea(
                child: RepaintBoundary(
              key: _qrCodeKey,
              child: Center(
                child: _buildContent(),
              ),
            ))));
  }

  Widget _buildContent() {
    return Container(
      color: Colors.white,
      padding: EdgeInsets.symmetric(vertical: 25),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          AvatarWidget(CommonHelper.getMyAvatarUrl(), size: 50),
          SizedBox(height: 10),
          Text(CacheHelper.userProfile?.shortNo ?? "",
              style: TextStyles.fontSize16Normal),
          SizedBox(height: 30),
          GestureDetector(
            onLongPress: () => _showSaveOptions(),
            child: _buildUserQr(),
          ),
          SizedBox(height: 20),
          Text(context.l10n.qrDesc(context.l10n.appName),
              style: TextStyles.fontSize15Normal
                  .copyWith(color: DColor.secondaryTextColor)),
        ],
      ),
    );
  }

  Widget _buildUserQr() {
    bool hasQrCode = item.value?.data.isNotEmpty ?? false;

    return Center(
      child: SizedBox(
        height: 250,
        child: hasQrCode
            ? PrettyQrView.data(
                data: item.value?.data ?? "",
                decoration: const PrettyQrDecoration(
                  shape: PrettyQrSmoothSymbol(roundFactor: 0),
                ),
              )
            : LoadingAnimationWidget.hexagonDots(
                color: DColor.primaryColor,
                size: 50,
              ),
      ),
    );
  }

  void _showSaveOptions() {
    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              ListTile(
                title: Text(context.l10n.saveToPhotoAlbum,
                    style: TextStyle(
                        color: Colors.black, fontWeight: FontWeight.w500),
                    textAlign: TextAlign.center),
                onTap: () {
                  Navigator.pop(context);
                  _saveQrCode();
                },
              ),
              ListTile(
                title: Text(context.l10n.globalCancel,
                    style: TextStyle(
                        color: Colors.grey, fontWeight: FontWeight.w500),
                    textAlign: TextAlign.center),
                onTap: () => Navigator.pop(context),
              ),
            ],
          ),
        );
      },
    );
  }

  Future<void> _saveQrCode() async {
    try {
      RenderRepaintBoundary boundary = _qrCodeKey.currentContext!
          .findRenderObject() as RenderRepaintBoundary;
      ui.Image image = await boundary.toImage(pixelRatio: 3.0);
      ByteData? byteData =
          await image.toByteData(format: ui.ImageByteFormat.png);

      if (byteData != null) {
        final tempDir = await getTemporaryDirectory();
        final file = File('${tempDir.path}/qrcode.png');
        await file.writeAsBytes(byteData.buffer.asUint8List());

        ImageUtils.saveToAlbum(file);
      }
    } catch (e) {
      print('Error saving QR code: $e');
    }
  }
}
