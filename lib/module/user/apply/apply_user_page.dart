import 'package:and/common/res/colours.dart';
import 'package:and/common/res/text_styles.dart';
import 'package:and/common/utils/easy_loading_helper.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/utils/http_utils.dart';
import 'package:and/widget/input_form_widget.dart';
import 'package:and/widget/submit_button.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:oktoast/oktoast.dart';

class ApplyUserPage extends StatefulWidget {
  final String channelId;
  final String? vercode;
  final String? remark;

  const ApplyUserPage(
      {super.key, required this.channelId, this.vercode, this.remark});

  @override
  State<StatefulWidget> createState() {
    return _ApplyUserPageState();
  }
}

class _ApplyUserPageState extends State<ApplyUserPage> {
  final _remarkTextController = TextEditingController();
  final GlobalKey<FormState> _remarkKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    _remarkTextController.text = widget.remark ?? "";
  }

  @override
  void dispose() {
    // Clean up the controller when the widget is disposed.
    _remarkTextController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: Text(context.l10n.applyFriend),
          actions: [
            Padding(
              padding: EdgeInsets.only(right: 15),
              child: _buildSubmitAction(),
            )
          ],
        ),
        body: Padding(
          padding: EdgeInsets.all(15),
          child: _buildContent(),
        ));
  }

  Widget _buildSubmitAction() {
    return SubmitButton(
        enable: _remarkTextController.text.isNotEmpty,
        padding: EdgeInsets.symmetric(horizontal: 10, vertical: 6),
        onPressed: () {
          _applyFriend();
        },
        text: context.l10n.globalConfirm);
  }

  Widget _buildContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(context.l10n.applyGroupFriendTip,
            style:
                TextStyles.fontSize15Normal.copyWith(color: DColor.hintText)),
        SizedBox(height: 10),
        InputTextForm(
            formKey: _remarkKey,
            value: widget.remark ?? "",
            minLines: 4,
            borderRadius: 2,
            showClear: false,
            keyboardType: TextInputType.emailAddress,
            controller: _remarkTextController,
            onChanged: (value) {
              setState(() {});
            })
      ],
    );
  }

  void _applyFriend() async {
    EasyLoadingHelper.show(onAction: () async {
      var applyResult = await HttpUtils.applyFriend(widget.channelId,
              widget.vercode ?? '', _remarkTextController.text)
          .catchError((e) {
        showToast(context.l10n.applyFriendFailed);
        return false;
      });
      if (applyResult) {
        showToast(context.l10n.applyed);
        Get.back();
      } else {
        showToast(context.l10n.applyFriendFailed);
      }
    });
  }
}
