import 'package:and/common/utils/easy_loading_helper.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/model/extension/wk_channel_ext.dart';
import 'package:and/module/chat/chat_logic.dart';
import 'package:and/module/chat/chat_page.dart';
import 'package:and/module/common/controller/simple_controller.dart';
import 'package:and/module/contact/choose/choose_contact_page.dart';
import 'package:and/module/search/globalSearch/global_search_logic.dart';
import 'package:and/module/search/globalSearch/global_search_page.dart';
import 'package:and/module/user/chat/chat_detail_page.dart';
import 'package:and/utils/dialog_utils.dart';
import 'package:and/utils/http_utils.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:wukongimfluttersdk/entity/channel.dart';
import 'package:wukongimfluttersdk/type/const.dart';
import 'package:wukongimfluttersdk/wkim.dart';

class ChatDetailLogic extends SimpleController<WKChannel> {
  final String key = 'chat_detail';
  final ChatDetailArgument argument;

  final filterKey = "".obs;

  ChatDetailLogic({required this.argument});

  @override
  void onReady() async {
    data.value = WKChannel(argument.channelId, WKChannelType.personal);
    super.onReady();

    startRefresh();
    _initListener();
  }

  void startRefresh() {
    EasyLoadingHelper.show(onAction: () async {
      await refreshData();
    });
  }

  @override
  void onClose() {
    _removeListener();
    super.onClose();
  }

  @override
  Future<WKChannel?> loadData() async {
    return await WKIM.shared.channelManager
        .getChannel(argument.channelId, WKChannelType.personal);
  }

  _initListener() {
    // 监听刷新channel资料事件
    WKIM.shared.channelManager.addOnRefreshListener(key, (channel) {
      print("刷新channel资料事件");
      if (channel.channelID == argument.channelId &&
          channel.channelType == WKChannelType.personal) {
        data.value = channel;
      }
    });
  }

  _removeListener() {
    WKIM.shared.channelManager.removeOnRefreshListener(key);
  }

  Future<void> addGroupMembers() async {
    var chooseUsers = await ChooseContactPage.open(
            unSelectChannelIds: [argument.channelId]) ??
        <WKChannel>[];
    if (chooseUsers.isEmpty) {
      return;
    }
    EasyLoadingHelper.show(onAction: () async {
      chooseUsers.insert(0, data.value!);

      var name = chooseUsers.map((e) => e.displayName).join(',');
      var ids = chooseUsers.map((e) => e.channelID).toList();
      var names = chooseUsers.map((e) => e.displayName).toList();

      var group = await HttpUtils.createGroup(name, ids, names);
      if (group != null) {
        ChatPage.open(
            channelID: group.channelID, channelType: group.channelType);
      }
    });
  }

  Future<bool> updateUserSetting(String key, dynamic value) async {
    var result = false;
    await EasyLoadingHelper.show(onAction: () async {
      result =
          await HttpUtils.updateUserSetting(argument.channelId, key, value);
    });

    return result;
  }

  Future<void> clearHistory(BuildContext context) async {
    bool clearBoth = false;
    bool? result = await Get.dialog<bool>(
      AlertDialog(
        title: Text(context.l10n.clearHistory),
        content: StatefulBuilder(
          builder: (context, setState) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  context.l10n.clearHistoryTip(data.value?.displayName ?? ''),
                ),
                CheckboxListTile(
                    controlAffinity: ListTileControlAffinity.leading,
                    contentPadding: EdgeInsets.zero,
                    title: Text(context.l10n.clearHistoryBoth),
                    value: clearBoth, onChanged:  (value) {
                  setState(() {
                    clearBoth = value ?? false;
                  });
                })
              ],
            );
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: Text(context.l10n.globalCancel),
          ),
          TextButton(
            onPressed: () => Get.back(result: true),
            child: Text(context.l10n.globalConfirm),
          ),
        ],
      ),
    );
    if (result == true) {
      EasyLoadingHelper.show(onAction: () async {
        await HttpUtils.clearHistory(
          argument.channelId,
          WKChannelType.personal,
          clearBoth: clearBoth
        );
      });
    }
  }

  void searchHistory(BuildContext context) {
    GlobalSearchPage.open(
        searchType: GlobalSearchType.channelMsg,
        channel: WKChannel(argument.channelId, WKChannelType.personal));
  }
}
