import 'package:and/module/group/group_detail_page.dart';
import 'package:and/module/user/chat/chat_detail_page.dart';
import 'package:get/get.dart';

import 'chat_detail_logic.dart';

class ChatDetailBinding extends Bindings {
  @override
  void dependencies() {
    var arguments = ChatDetailArgument.fromGet();
    Get.lazyPut(() => ChatDetailLogic(argument: arguments), tag: arguments.getTag());
  }
}
