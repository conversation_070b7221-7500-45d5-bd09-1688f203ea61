import 'package:and/app.dart';
import 'package:and/common/utils/easy_loading_helper.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/model/extension/wk_channel_ext.dart';
import 'package:and/model/user_info.dart';
import 'package:and/module/common/controller/simple_controller.dart';
import 'package:and/module/user/user_info_page.dart';
import 'package:and/utils/common_dialog.dart';
import 'package:and/utils/dialog_utils.dart';
import 'package:and/utils/http_utils.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:oktoast/oktoast.dart';
import 'package:wukongimfluttersdk/entity/channel.dart';
import 'package:wukongimfluttersdk/wkim.dart';

class UserInfoLogic extends SimpleController<WKChannel> {
  final userInfo = Rx<UserInfo?>(null);

  final UserInfoArgument argument;

  String get channelID => argument.channelID;

  int get channelType => argument.channelType;

  String? get groupNo => argument.groupNo;

  String? get vercode => argument.vercode;

  UserInfoLogic({required this.argument});

  @override
  void onReady() {
    super.onReady();
    EasyLoadingHelper.show(
        delay: Duration(milliseconds: 300),
        onAction: () async {
          await refreshData();
        });

    _initListener();
  }

  @override
  void onClose() {
    _removeListener();
    super.onClose();
  }

  @override
  Future<WKChannel?> loadData() async {
    var channel =
        await WKIM.shared.channelManager.getChannel(channelID, channelType);
    if (channel == null) {
      await _fetchChannelInfo();
    } else {
      _fetchChannelInfo();
    }

    if (channel?.isSystemChannel == false) {
      _fetchUserInfo();
    }
    return channel;
  }

  Future<void> _fetchChannelInfo() async {
    WKIM.shared.channelManager.fetchChannelInfo(channelID, channelType);
  }

  Future<void> _fetchUserInfo() async {
    userInfo.value = await HttpUtils.getUserInfo(channelID, groupNo: groupNo);
  }

  bool isSystemChannel() {
    return data.value?.isSystemChannel ?? false;
  }

  void deleteFriend(WKChannel channel) async {
    var context = globalContext;
    if (context == null) return;
    var result = (await DialogUtils.showConfirmDialog(
            context, context.l10n.deleteFriendsTips(channel.displayName),
            title: context.l10n.deleteFriends,
            confirmText: context.l10n.globalDelete)) ??
        false;
    if (result) {
      await EasyLoadingHelper.show(onAction: () async {
        //删除好友
        await HttpUtils.deleteFriend(channel.channelID);
        await HttpUtils.offsetMsg(channel.channelID, channel.channelType);

        //清除消息
        WKIM.shared.messageManager
            .clearWithChannel(channel.channelID, channel.channelType);
        //清除会话
        WKIM.shared.conversationManager
            .deleteMsg(channel.channelID, channel.channelType);
        //更新channel，设置为未关注
        channel.follow = 0;
        WKIM.shared.channelManager.addOrUpdateChannel(channel);
      });
      Get.back();
    }
  }

  void addBlackList(WKChannel channel) async {
    var context = globalContext;
    if (context == null) return;
    var isInBlack = channel.isInBlack;

    var title =
        isInBlack ? context.l10n.pullOutBlackList : context.l10n.pushBlackList;
    var content = isInBlack
        ? context.l10n.pullOutBlackListTips
        : context.l10n.joinBlackListTips;
    var result =
        (await DialogUtils.showConfirmDialog(context, content, title: title) ??
            false);
    if (result) {
      EasyLoadingHelper.show(onAction: () async {
        await HttpUtils.addOrRemoveBlackList(channel.channelID, !isInBlack);
        WKIM.shared.channelManager.fetchChannelInfo(channelID, channelType);
        refreshData();
      });
    }
  }

  void updateRemark(BuildContext context, WKChannel channel) async {
    String? result = await DialogUtils.showInputDialog(context,
        title: context.l10n.contactRemark, hint: context.l10n.inputRemark);
    if (result != null) {
      EasyLoadingHelper.show(onAction: () async {
        var applyResult =
            await HttpUtils.updateRemark(channel.channelID, result) ?? false;
        if (applyResult) {
          showToast(context.l10n.contactRemarkSuccess);
        }
      });
    }
  }

  _initListener() {
    // 监听刷新channel资料事件
    WKIM.shared.channelManager.addOnRefreshListener(channelID, (channel) {
      print("刷新channel资料事件");
      if (channel.channelID == channelID &&
          channel.channelType == channelType) {
        data.value = channel;
      }
    });
  }

  _removeListener() {
    WKIM.shared.channelManager.removeOnRefreshListener(channelID);
  }
}
