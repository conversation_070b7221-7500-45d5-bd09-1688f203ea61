import 'package:and/cache/cache_helper.dart';
import 'package:and/constant/user_info_keys.dart';
import 'package:and/model/user_login_info.dart';
import 'package:and/utils/http_utils.dart';
import 'package:shared_preferences/shared_preferences.dart';

class MySettingManager {
  // 私有构造函数，确保外部无法直接实例化
  MySettingManager._();

  static final MySettingManager _instance = MySettingManager._();

  static MySettingManager get instance => _instance;

  /// **设置开关状态**
  Future<void> _setting(String key, bool value) async {
    _saveSetting(key, value);

    HttpUtils.updateMySetting(key, value ? 1 : 0);
  }

  void _saveSetting(String key, bool value) async {
    var userProfile = CacheHelper.userProfile;
    Map<String, dynamic>? setting = userProfile?.setting?.toJson();
    setting?[key] = value ? 1 : 0;
    if (setting == null || userProfile == null) return;
    var newInfo = userProfile.copyWith(setting: Setting.fromJson(setting));
    CacheHelper.saveUserProfile(newInfo);
  }

  /// **获取开关状态**
  bool _getSetting(String key) {
    Map<String, dynamic>? setting = CacheHelper.userProfile?.setting?.toJson();
    return setting?[key] == 1 || setting?[key] == null;
  }

  /// **新消息通知**
  Future<void> setNewMsgNotice(bool on) =>
      _setting(UserInfoKeys.newMsgNotice, on);

  bool get newMsgNotice => _getSetting(UserInfoKeys.newMsgNotice);

  /// **通知是否显示详情**
  Future<void> setMsgShowDetail(bool on) =>
      _setting(UserInfoKeys.msgShowDetail, on);

  bool get msgShowDetail => _getSetting(UserInfoKeys.msgShowDetail);

  /// **开启声音**
  Future<void> setVoiceOn(bool on) => _setting(UserInfoKeys.voiceOn, on);

  bool get voiceOn => _getSetting(UserInfoKeys.voiceOn);

  /// **开启震动**
  Future<void> setShockOn(bool on) => _setting(UserInfoKeys.shockOn, on);

  bool get shockOn => _getSetting(UserInfoKeys.shockOn);

  /// **允许手机号搜索**
  Future<void> setSearchByPhone(bool on) =>
      _setting(UserInfoKeys.searchByPhone, on);

  bool get searchByPhone => _getSetting(UserInfoKeys.searchByPhone);

  /// **允许短号搜索**
  Future<void> setSearchByShort(bool on) =>
      _setting(UserInfoKeys.searchByShort, on);

  bool get searchByShort => _getSetting(UserInfoKeys.searchByShort);

  /// **离线保护**
  Future<void> setOfflineProtection(bool on) =>
      _setting(UserInfoKeys.offlineProtection, on);

  bool get offlineProtection => _getSetting(UserInfoKeys.offlineProtection);

  /// **应用静音**
  Future<void> setMuteOfApp(bool on) => _setting(UserInfoKeys.muteOfApp, on);

  bool get muteOfApp => _getSetting(UserInfoKeys.muteOfApp);
}
