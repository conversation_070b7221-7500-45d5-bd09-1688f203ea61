import 'dart:io';

import 'package:and/l10n/l10n.dart';
import 'package:and/module/user/mine/my_info_logic.dart';
import 'package:and/utils/common_helper.dart';
import 'package:and/utils/image_path.dart';
import 'package:and/utils/image_utils.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:oktoast/oktoast.dart';

class MyAvatarPage extends StatefulWidget {
  const MyAvatarPage({super.key});

  @override
  _MyAvatarPageState createState() => _MyAvatarPageState();
}

class _MyAvatarPageState extends State<MyAvatarPage> {
  late MyInfoLogic logic = Get.find<MyInfoLogic>();
  File? _imageFile;

  // 从相册选择图片
  Future<void> _pickImageFromGallery() async {
    File? file = await ImageUtils.pickImage(context,
        imageSource: ImageSource.gallery);
    if (file != null) {
      _fileProcessing(file);
    }
  }

  // 拍照选择图片
  Future<void> _pickImageFromCamera() async {
    File? file = await ImageUtils.pickImage(context,
        imageSource: ImageSource.camera);
    if (file != null) {
      _fileProcessing(file);
    }
  }

  // 对图片进行处理：裁剪->压缩->上传
  Future<void> _fileProcessing(File pickedFile) async {
    final uploadedFile = await logic.imageProcessing(context, pickedFile);
    if (uploadedFile != null) {
      if (mounted) {
        setState(() {
          _imageFile = uploadedFile;
        });
      }
    }
  }

  // 保存头像到相册
  void _saveImage() async {
    if (_imageFile != null) {
      ImageUtils.saveToAlbum(_imageFile!);
      return;
    }
    final cacheManager = CachedNetworkImageProvider.defaultCacheManager;
    final cachedImage =
        await cacheManager.getFileFromCache(CommonHelper.getMyAvatarUrl());
    if (cachedImage == null) {
      showToast(context.l10n.noAvatar);
      return;
    }

    ImageUtils.saveToAlbum(cachedImage.file);
  }

  /// 点击右上角更多操作
  void _moreAction(BuildContext context) {
    final actions = [
      context.l10n.takePhoto,
      context.l10n.selectFromPhotoAlbum,
      context.l10n.saveToPhotoAlbum
    ];
    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
            color: Colors.white,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListView.separated(
                shrinkWrap: true,
                physics: NeverScrollableScrollPhysics(),
                itemCount: actions.length,
                separatorBuilder: (context, index) =>
                    Divider(height: 0.5, color: Colors.grey[50]),
                itemBuilder: (BuildContext context, int index) {
                  return ListTile(
                    title: Text(actions[index], textAlign: TextAlign.center),
                    onTap: () {
                      switch (index) {
                        case 0:
                          _pickImageFromCamera();
                          break;
                        case 1:
                          _pickImageFromGallery();
                          break;
                        case 2:
                          _saveImage();
                          break;
                        default:
                          break;
                      }
                      Navigator.pop(context);
                    },
                  );
                },
              ),
              Divider(height: 0.5, color: Colors.grey[50]),
              SafeArea(
                  child: ListTile(
                title: Text(context.l10n.globalCancel,
                    style: TextStyle(
                        color: Colors.grey, fontWeight: FontWeight.w500),
                    textAlign: TextAlign.center),
                onTap: () => Navigator.pop(context),
              )),
            ],
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(context.l10n.avatarEditing),
        actions: [
          InkWell(
            child: Image.asset(
              ImagePath.ic_more_black,
              width: 40,
              height: 20,
              fit: BoxFit.contain,
            ),
            onTap: () => _moreAction(context),
          )
        ],
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // 显示当前头像
            _imageFile != null
                ? Image.file(_imageFile!)
                : CachedNetworkImage(
                    imageUrl: CommonHelper.getMyAvatarUrl(),
                    width: MediaQuery.of(context).size.width,
                    height: MediaQuery.of(context).size.width,
                    fit: BoxFit.fitWidth)
          ],
        ),
      ),
    );
  }
}
