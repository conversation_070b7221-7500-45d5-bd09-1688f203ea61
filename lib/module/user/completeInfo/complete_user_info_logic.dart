import 'dart:async';

import 'package:and/cache/cache_helper.dart';
import 'package:and/common/utils/easy_loading_helper.dart';
import 'package:and/http/my_http.dart';
import 'package:and/io/user.dart';
import 'package:and/l10n/language_utils.dart';
import 'package:and/model/request/send_mail_request.dart';
import 'package:and/model/request/update_email_request.dart';
import 'package:and/model/request/update_phone_request.dart';
import 'package:and/model/user_login_info.dart';
import 'package:and/router/router.dart';
import 'package:and/utils/http_utils.dart';
import 'package:country_codes/country_codes.dart';
import 'package:get/get.dart';

class CompleteUserInfoLogic extends GetxController {
  Timer? _timer;
  final Rx<int> secondsRemaining = Rx<int>(-1);
  final areaCode = "0086".obs;

  String get areaCodeDisplay => "+${areaCode.value.substring(2)}";

  @override
  void onReady() async{
    super.onReady();
    await CountryCodes.init();
    final CountryDetails details = CountryCodes.detailsForLocale(LanguageUtils.getDeviceLocale());
    var dialCode = details.dialCode;
    if (dialCode != null) {
      areaCode.value = dialCode.replaceAll("+", "00");
    }
  }

  void setAreaCode(String? code) {
    if (code == null) return;
    areaCode.value = code;
  }

  Future<void> updatePhone(
      {required String phone,
      required String code,
      required String zone}) async {
    UpdatePhoneRequest request = UpdatePhoneRequest(
      phone: phone,
      code: code,
      zone: zone,
    );
    EasyLoadingHelper.show(onAction: () async {
      var response = await UserApi(MyHttp.dio).updatePhone(request);
      if (response.success) {
        var userInfo = CacheHelper.userProfile;
        if (userInfo != null) {
          userInfo = userInfo.copyWith(phone: phone);
          CacheHelper.saveUserProfile(userInfo);
          Get.offAllNamed(RouteGet.main);
        }
      }
    });
  }

  Future<void> updateEmail(
      {required String email,
        required String code}) async {
    UpdateEmailRequest request = UpdateEmailRequest(
      email: email,
      code: code
    );
    EasyLoadingHelper.show(onAction: () async {
      var response = await UserApi(MyHttp.dio).updateEmail(request);
      if (response.success) {
        var userInfo = CacheHelper.userProfile;
        if (userInfo != null) {
          userInfo = userInfo.copyWith(email: email);
          CacheHelper.saveUserProfile(userInfo);
          Get.back();
        }
      }
    });
  }

  Future<void> sendEmail(
      {required String email,
      required Function() onSuccess}) async {
    HttpUtils.sendMail(email, onSuccess: () {
      onSuccess();
    }, mailType: SendMailType.updateEmail);
  }

  void startCountdown() {
    secondsRemaining.value = 60;
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (secondsRemaining.value > 0) {
        secondsRemaining.value--;
      } else {
        _timer?.cancel();
      }
    });
  }

  void stopCountdown() {
    _timer?.cancel();
  }

  @override
  void dispose() {
    stopCountdown();
    super.dispose();
  }
}
