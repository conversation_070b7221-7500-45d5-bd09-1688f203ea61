import 'package:and/l10n/l10n.dart';
import 'package:and/model/request/send_sms_request.dart';
import 'package:and/router/router.dart';
import 'package:and/utils/common_helper.dart';
import 'package:and/utils/dialog_utils.dart';
import 'package:and/utils/http_utils.dart';
import 'package:and/widget/input_form_widget.dart';
import 'package:and/widget/submit_button.dart';
import 'package:flutter/material.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:get/get.dart';

import 'complete_user_info_logic.dart';

class CompleteUserInfoPage extends StatefulWidget {
  final bool isEmailMode;

  const CompleteUserInfoPage({super.key, this.isEmailMode = false});

  @override
  State<StatefulWidget> createState() {
    return _CompleteUserInfoPageState();
  }
}

class _CompleteUserInfoPageState extends State<CompleteUserInfoPage>
    with SingleTickerProviderStateMixin {
  final logic = Get.put(CompleteUserInfoLogic());
  late Rx<int> secondsRemaining = logic.secondsRemaining;

  final _emailTextController = TextEditingController();
  final _phoneTextController = TextEditingController();
  final _codeTextController = TextEditingController();

  final GlobalKey<FormState> _phoneKey = GlobalKey<FormState>();
  final GlobalKey<FormState> _emailKey = GlobalKey<FormState>();
  final GlobalKey<FormState> _codeKey = GlobalKey<FormState>();

  @override
  void dispose() {
    // Clean up the controller when the widget is disposed.
    _codeTextController.dispose();
    _emailTextController.dispose();
    _phoneTextController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
          leading: null,
          titleSpacing: 20,
          title: Text(widget.isEmailMode
              ? context.l10n.bindEmail
              : context.l10n.bindPhoneNumber),
        ),
        body: Container(
          color: Colors.white,
          padding: const EdgeInsets.only(left: 30, right: 30),
          child: Obx(() => _buildContent()),
        ));
  }

  Widget _buildContent() {
    return SafeArea(
      child: SizedBox.expand(
        child: Container(
          color: Colors.white,
          child: SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            child: Column(
              children: [
                const SizedBox(height: 40),
                widget.isEmailMode ? _buildEmail() : _buildPhone(),
                const SizedBox(height: 20),
                _buildCode(),
                const SizedBox(height: 20),
                _buildConfirmButton(),
                if (!widget.isEmailMode) ...[
                  const SizedBox(height: 20),
                  _buildLogoutButton(),
                ],
                const SizedBox(height: 120),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildConfirmButton() {
    return SubmitButton(
        enable: true,
        text: context.l10n.globalConfirm,
        textColor: Colors.white,
        backgroundColor: Colors.black,
        textSize: 20,
        onPressed: () async {
          var allValidList = [
            widget.isEmailMode ? _emailKey : _phoneKey,
            _codeKey,
          ];

          var isValid = allValidList
              .every((element) => element.currentState?.validate() ?? false);
          if (isValid) {
            if (widget.isEmailMode) {
              logic.updateEmail(
                  email: _emailTextController.text,
                  code: _codeTextController.text);
            } else {
              logic.updatePhone(
                  zone: logic.areaCode.value,
                  code: _codeTextController.text,
                  phone: _phoneTextController.text);
            }
          }
        });
  }

  Widget _buildLogoutButton() {
    return SubmitButton(
        enable: true,
        text: context.l10n.switchAccount,
        textColor: Colors.white,
        textSize: 20,
        onPressed: () async {
          var result = (await DialogUtils.showConfirmDialog(
                  context, context.l10n.logoutTip,
                  title: context.l10n.makeSureLogout)) ??
              false;
          if (result) {
            CommonHelper.exitLogin();
          }
        });
  }

  Widget _buildEmail() {
    return InputTextForm(
        formKey: _emailKey,
        validator: FormBuilderValidators.email(
            errorText: context.l10n.signupEmailErrorFormat),
        hint: context.l10n.signupEmailPlaceholder,
        value: "",
        keyboardType: TextInputType.emailAddress,
        controller: _emailTextController,
        onChanged: (value) {
          setState(() {});
        });
  }

  Widget _buildPhone() {
    return InputTextForm(
        formKey: _phoneKey,
        validator: FormBuilderValidators.phoneNumber(
            errorText: context.l10n.signupPhoneErrorFormat),
        hint: context.l10n.signupPhonePlaceholder,
        value: "",
        keyboardType: TextInputType.phone,
        controller: _phoneTextController,
        prefixIcon: InkWell(
          onTap: () async {
            var code = await Get.toNamed(RouteGet.chooseCountry);
            logic.setAreaCode(code);
          },
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 8),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(logic.areaCodeDisplay),
                Icon(Icons.arrow_drop_down)
              ],
            ),
          ),
        ),
        onChanged: (value) {
          setState(() {});
        });
  }

  Widget _buildCode() {
    var remainTime = secondsRemaining.value;

    var isEnable = (widget.isEmailMode
            ? _emailTextController.text.isNotEmpty
            : _phoneTextController.text.isNotEmpty) &&
        remainTime <= 0;
    return Row(
      children: [
        Expanded(
            child: InputTextForm(
                formKey: _codeKey,
                validator: FormBuilderValidators.required(
                    errorText: context.l10n.signupVerifyCodeHint),
                hint: context.l10n.signupVerifyCodeHint,
                value: "",
                keyboardType: TextInputType.text,
                controller: _codeTextController,
                onChanged: (value) {
                  setState(() {});
                })),
        SizedBox(width: 10),
        SubmitButton(
            enable: isEnable,
            text: remainTime <= 0
                ? context.l10n.sendCode
                : context.l10n.secondsRemaining(remainTime),
            textColor: Colors.white,
            backgroundColor: Colors.black,
            textSize: 14,
            onPressed: () async {
              var allValidList = [
                widget.isEmailMode ? _emailKey : _phoneKey,
              ];
              var isValid = allValidList.every(
                  (element) => element.currentState?.validate() ?? false);
              if (isValid) {
                if (widget.isEmailMode) {
                  logic.sendEmail(
                      email: _emailTextController.text,
                      onSuccess: () {
                        logic.startCountdown();
                      });
                } else {
                  HttpUtils.sendSMS(
                      zone: logic.areaCode.value,
                      phone: _phoneTextController.text,
                      codeType: SendCodeType.updatePhone,
                      onSuccess: () {
                        logic.startCountdown();
                      });
                }
              }
            })
      ],
    );
  }
}
