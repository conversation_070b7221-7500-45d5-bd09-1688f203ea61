import 'package:and/cache/cache_helper.dart';
import 'package:and/common/res/colours.dart';
import 'package:and/common/res/text_styles.dart';
import 'package:and/http/http_config.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/l10n/language_utils.dart';
import 'package:and/model/extension/wk_channel_ext.dart';
import 'package:and/model/user_info.dart';
import 'package:and/module/chat/chat_page.dart';
import 'package:and/module/user/user_info_logic.dart';
import 'package:and/module/user/user_info_page.dart';
import 'package:and/utils/common_helper.dart';
import 'package:and/widget/avatar_widget.dart';
import 'package:and/widget/simple_setting_item_widget.dart';
import 'package:and/widget/submit_button.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:wukongimfluttersdk/entity/channel.dart';
import 'package:wukongimfluttersdk/type/const.dart';
import 'package:wukongimfluttersdk/wkim.dart';

class NormalUserPage extends StatefulWidget {
  final WKChannel channel;
  final UserInfo? userInfo;
  final String? groupNo;

  const NormalUserPage(
      {super.key, required this.channel, this.userInfo, this.groupNo});

  @override
  State<StatefulWidget> createState() {
    return _NormalUserPageState();
  }
}

class _NormalUserPageState extends State<NormalUserPage> {
  late UserInfoArgument argument = UserInfoArgument.fromGet();
  late UserInfoLogic logic = Get.find<UserInfoLogic>(tag: argument.getTag());

  @override
  Widget build(BuildContext context) {
    return Scaffold(appBar: AppBar(), body: _buildContent());
  }

  Widget _buildContent() {
    return SingleChildScrollView(
      child: Column(
        children: [
          _buildInfo(),
          Container(height: 10),
          _buildRemarkAndSource(),
          Container(height: 10),
          _buildAction(),
          _buildBlackInfo()
        ],
      ),
    );
  }

  Widget _buildBlackInfo() {
    if (widget.channel.isInBlack) {
      return Container(
          padding: EdgeInsets.symmetric(horizontal: 15, vertical: 15),
          child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.error, color: Colors.red, size: 18),
                SizedBox(width: 4),
                Text(context.l10n.blackListDesc,
                    style: TextStyles.fontSize12Medium)
              ]));
    }
    return Container();
  }

  Widget _buildAction() {
    if (widget.channel.follow == 1) {
      return Column(
        children: [_buildFriends(), Container(height: 10), _buildSendMessage()],
      );
    }
    if (widget.channel.follow == 0) {
      return Column(
        children: [
          Container(color: Colors.white, child: _buildAddToBlack()),
          Container(height: 10),
          Padding(
              padding: EdgeInsets.symmetric(horizontal: 15),
              child: Row(
                children: [
                  Expanded(child: _buildAddFriend()),
                  Container(width: 10),
                  Expanded(child: _buildSayHello())
                ],
              ))
        ],
      );
    }

    return Container();
  }

  Widget _buildInfo() {
    return Container(
      color: Colors.white,
      padding: EdgeInsets.symmetric(horizontal: 15, vertical: 15),
      child: Row(children: [
        AvatarWidget(
          widget.channel.avatarUrl,
          size: 60,
          fontSize: 14,
        ),
        SizedBox(width: 10),
        Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          Text(widget.channel.displayName, style: TextStyles.fontSize18Medium),
          if (widget.channel.channelRemark.isNotEmpty)
            Text(context.l10n.nickname(widget.channel.channelName),
                style: TextStyles.fontSize15Normal),
        ])
      ]),
    );
  }

  Widget _buildRemarkAndSource() {
    return Container(
      color: Colors.white,
      child: Column(
        children: [
          SimpleSettingItemWidget(
            context.l10n.contactRemark,
            onTap: () {
              logic.updateRemark(context, widget.channel);
            },
          ),
          _buildContactSource(),
          _buildGroupSource(),
          SimpleSettingItemWidget(context.l10n.report, onTap: () {
            Map<String, String> params = {
              "channel_id": argument.channelID,
              "channel_type": WKChannelType.personal.toString(),
              "uid": CacheHelper.uid ?? '',
              "token": CacheHelper.token ?? '',
            };
            CommonHelper.report(params);
          }),
        ],
      ),
    );
  }

  Widget _buildContactSource() {
    var sourceDesc =
        widget.userInfo?.sourceDesc ?? widget.channel.sourceDesc ?? '';
    if (sourceDesc.isEmpty) {
      return Container();
    }

    return SimpleSettingItemWidget(
      context.l10n.contactSource,
      value: sourceDesc,
    );
  }

  Widget _buildGroupSource() {
    var joinGroupInviteName = widget.userInfo?.joinGroupInviteName;
    var joinGroupTime = widget.userInfo?.joinGroupTime;
    if (argument.groupNo == null || joinGroupInviteName == null) {
      return Container();
    }

    var sourceDesc =
        "$joinGroupTime $joinGroupInviteName${context.l10n.inviteGroup}";

    return SimpleSettingItemWidget(
      context.l10n.groupSource,
      value: sourceDesc,
    );
  }

  Widget _buildFriends() {
    return Container(
      color: Colors.white,
      child: Column(
        children: [
          SimpleSettingItemWidget(
            context.l10n.deleteFriends,
            onTap: () async {
              logic.deleteFriend(widget.channel);
            },
          ),
          _buildAddToBlack(),
        ],
      ),
    );
  }

  Widget _buildAddToBlack() {
    return SimpleSettingItemWidget(
      widget.channel.isInBlack
          ? context.l10n.pullOutBlackList
          : context.l10n.pushBlackList,
      onTap: () {
        logic.addBlackList(widget.channel);
      },
    );
  }

  Widget _buildSendMessage() {
    return InkWell(
        onTap: () {
          ChatPage.open(
              channelID: logic.channelID,
              channelType: logic.channelType);
        },
        child: Container(
            color: Colors.white,
            padding: EdgeInsets.symmetric(horizontal: 15, vertical: 15),
            child: Column(children: [
              Center(
                  child: Text(context.l10n.sendMessage,
                      style: TextStyles.fontSize18Medium
                          .copyWith(color: DColor.secondaryTextColor))),
            ])));
  }

  Widget _buildAddFriend() {
    return SubmitButton(
        backgroundColor: DColor.greyE7,
        textColor: DColor.submitPrimaryColor,
        onPressed: () async {
          CommonHelper.addFriend(context,
              channelID: argument.channelID,
              vercode: argument.vercode ?? widget.userInfo?.vercode ?? '',
              groupNo: argument.groupNo);
        },
        text: context.l10n.applyFriend);
  }

  Widget _buildSayHello() {
    return SubmitButton(
        onPressed: () {
          CommonHelper.sayHello(context,
              channelID: argument.channelID,
              vercode: argument.vercode ?? widget.userInfo?.vercode ?? '',
              groupNo: argument.groupNo);
        },
        text: context.l10n.sayHello);
  }
}
