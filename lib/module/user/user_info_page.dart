import 'package:and/cache/cache_helper.dart';
import 'package:and/router/router.dart';
import 'package:and/widget/refresh/load_state.dart';
import 'package:and/widget/refresh/refresh_page.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:wukongimfluttersdk/entity/channel.dart';
import 'package:wukongimfluttersdk/type/const.dart';

import 'normal/normal_user_page.dart';
import 'system/system_user_page.dart';
import 'user_info_logic.dart';

class UserInfoArgument {
  String channelID;
  int channelType;
  String? groupNo;
  String? vercode;

  UserInfoArgument(
      {required this.channelID,
      required this.channelType,
      this.groupNo,
      this.vercode});

  factory UserInfoArgument.fromGet() {
    return (Get.arguments as UserInfoArgument);
  }

  String getTag() {
    return "${channelID}_$channelType";
  }

  static getTagFromGet() {
    return UserInfoArgument.fromGet().getTag();
  }
}

class UserInfoPage extends StatefulWidget {
  const UserInfoPage({super.key});

  @override
  State<StatefulWidget> createState() {
    return _UserInfoPageState();
  }

  static void open(
      {required String channelID,
      int channelType = WKChannelType.personal,
      String? groupNo,
      String? vercode}) {
    if (channelID == CacheHelper.userProfile?.uid) {
      Get.toNamed(RouteGet.myInfo);
      return;
    }
    Get.toNamed(RouteGet.userInfo,
        preventDuplicates: false,
        arguments: UserInfoArgument(
          channelID: channelID,
          channelType: channelType,
          groupNo: groupNo,
          vercode: vercode,
        ));
  }
}

class _UserInfoPageState extends State<UserInfoPage> {
  late UserInfoArgument argument = UserInfoArgument.fromGet();
  late UserInfoLogic logic = Get.find<UserInfoLogic>(tag: argument.getTag());
  late var item = logic.data;
  late var userInfo = logic.userInfo;

  @override
  Widget build(BuildContext context) {
    return Scaffold(body: Obx(() => _buildContent()));
  }

  @override
  void dispose() {
    super.dispose();

    Get.delete(tag: argument.getTag());
  }

  Widget _buildContent() {
    var channel = item.value;
    if (channel == null) {
      return Container();
    }
    if (logic.isSystemChannel()) {
      return SystemUserPage(
        channel: channel,
      );
    }

    return NormalUserPage(
        channel: channel, userInfo: userInfo.value, groupNo: argument.groupNo);
  }
}
