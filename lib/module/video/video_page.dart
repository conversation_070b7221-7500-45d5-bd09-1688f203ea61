import 'dart:async';
import 'dart:io';

import 'package:and/common/extension/common_ext.dart';
import 'package:and/module/file/widget/file_menu_widget.dart';
import 'package:background_downloader/background_downloader.dart';
import 'package:better_player_plus/better_player_plus.dart';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class VideoPage extends StatefulWidget {
  final Uri uri;
  final Uri? coverUri;

  const VideoPage({super.key, required this.uri, this.coverUri});

  @override
  State<StatefulWidget> createState() {
    return _VideoPageState();
  }
}

class _VideoPageState extends State<VideoPage> with TickerProviderStateMixin {
  late BetterPlayerController _betterPlayerController;
  late BetterPlayerDataSource _betterPlayerDataSource;
  final StreamController<bool> _playController = StreamController.broadcast();

  @override
  void initState() {
    initializePlayer();
    super.initState();
  }

  @override
  void dispose() {
    _betterPlayerController.dispose();
    super.dispose();
  }

  Future<void> initializePlayer() async {
    BetterPlayerConfiguration betterPlayerConfiguration =
        BetterPlayerConfiguration(
          autoPlay: true,
          looping: true,
          fit: BoxFit.contain,
          showPlaceholderUntilPlay: true,
          placeholder: _buildPlaceholder(),
          autoDetectFullscreenDeviceOrientation: true,
          autoDetectFullscreenAspectRatio: true,
          allowedScreenSleep: false,
    );
    _betterPlayerDataSource = BetterPlayerDataSource(
      widget.uri.isFileUri
          ? BetterPlayerDataSourceType.file
          : BetterPlayerDataSourceType.network,
      widget.uri.toString(),
      cacheConfiguration: BetterPlayerCacheConfiguration(
        useCache: true,
        preCacheSize: 100 * 1024 * 1024,
        maxCacheSize: 100 * 1024 * 1024,
        maxCacheFileSize: 100 * 1024 * 1024,

        ///Android only option to use cached video between app sessions
        key: widget.uri.hashCode.toString(),
      ),
    );
    _betterPlayerController = BetterPlayerController(betterPlayerConfiguration);
    _betterPlayerController.setupDataSource(_betterPlayerDataSource);
    _betterPlayerController.addEventsListener((event) {
      if (event.betterPlayerEventType == BetterPlayerEventType.play) {
        _playController.add(false);
      }
    });

    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(leading: CloseButton(), actions: [_buildAction()]),
        body: SafeArea(child: _buildContent()));
  }

  Widget _buildAction() {
    var uri = widget.uri;
    if (uri.isFileUri && !File.fromUri(uri).existsSync()) {
      return Container();
    }
    return Padding(
      padding: EdgeInsets.only(right: 10),
      child: FileMenuWidget(uri: uri),
    );
  }

  Widget _buildContent() {
    return InteractiveViewer(
      panEnabled: false,
      scaleEnabled: true,
      child: BetterPlayer(
        controller: _betterPlayerController,
      ),
    );
  }

  Widget? _buildPlaceholder() {
    if (widget.coverUri == null) return null;
    var isHttp = widget.coverUri!.isHttpUri;
    return StreamBuilder<bool>(
      stream: _playController.stream,
      builder: (context, snapshot) {
        bool showPlaceholder = snapshot.data ?? true;
        return AnimatedOpacity(
          duration: Duration(milliseconds: 500),
          opacity: showPlaceholder ? 1.0 : 0.0,
          child: isHttp
              ? Image.network(
                  widget.coverUri.toString(),
                  fit: BoxFit.contain,
                )
              : Image.file(
                  File.fromUri(widget.coverUri!),
                  fit: BoxFit.contain,
                ),
        );
      },
    );
  }
}
