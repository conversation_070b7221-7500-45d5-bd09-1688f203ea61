import 'package:and/cache/cache_helper.dart';
import 'package:and/module/login/login_page.dart';
import 'package:and/module/main/main_tab_page.dart';
import 'package:and/module/user/completeInfo/complete_user_info_page.dart';
import 'package:and/utils/common_helper.dart';
import 'package:flutter/material.dart';

class MainPage extends StatefulWidget {
  const MainPage({super.key});

  @override
  State<StatefulWidget> createState() {
    return _MainState();
  }
}

class _MainState extends State<MainPage> {
  @override
  Widget build(BuildContext context) {
    if (!CommonHelper.isLogin()) {
      return LoginPage();
    }

    var userInfo = CacheHelper.userProfile;
    var hasPhone = userInfo?.phone?.isNotEmpty ?? false;
    if (!hasPhone) {
      return CompleteUserInfoPage(isEmailMode: false,);
    }

    return MainTabPage();
  }
}
