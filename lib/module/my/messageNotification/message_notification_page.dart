import 'package:and/l10n/l10n.dart';
import 'package:and/module/user/mine/my_setting_manager.dart';
import 'package:flutter/material.dart';

class MessageNotificationPage extends StatefulWidget {
  const MessageNotificationPage({super.key});

  @override
  State<StatefulWidget> createState() {
    return _MessageNotificationPageState();
  }
}

class _MessageNotificationPageState extends State<MessageNotificationPage> {
  bool _messageNotifications = true;
  bool _soundEnabled = true;
  bool _vibrateEnabled = true;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  /// 加载用户设置
  Future<void> _loadSettings() async {
    setState(() {
      _messageNotifications = MySettingManager.instance.newMsgNotice;
      _soundEnabled = MySettingManager.instance.voiceOn;
      _vibrateEnabled = MySettingManager.instance.shockOn;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text(context.l10n.newMessageNotification)),
      body: ListView(
        children: [
          _buildSwitchTile(
            title: context.l10n.newMessageNotification,
            value: _messageNotifications,
            onChanged: (value) {
              setState(() {
                _messageNotifications = value;
                MySettingManager.instance.setNewMsgNotice(value);
              });
            },
          ),
          _buildSwitchTile(
            title: context.l10n.settingSound,
            value: _soundEnabled,
            onChanged: (value) {
              setState(() {
                _soundEnabled = value;
                MySettingManager.instance.setVoiceOn(value);
              });
            },
            enabled: _messageNotifications, // 依赖消息通知开关
          ),
          _buildSwitchTile(
            title: context.l10n.settingShock,
            value: _vibrateEnabled,
            onChanged: (value) {
              setState(() {
                _vibrateEnabled = value;
                MySettingManager.instance.setShockOn(value);
              });
            },
            enabled: _messageNotifications, // 依赖消息通知开关
          ),
        ],
      ),
    );
  }

  /// 创建通用的 SwitchTile 组件
  Widget _buildSwitchTile({
    required String title,
    required bool value,
    required ValueChanged<bool> onChanged,
    bool enabled = true,
  }) {
    return Opacity(
      opacity: enabled ? 1.0 : 0.5, // 变灰表示不可用
      child: SwitchListTile(
        title: Text(title),
        value: value,
        onChanged: enabled ? onChanged : null,
      ),
    );
  }
}
