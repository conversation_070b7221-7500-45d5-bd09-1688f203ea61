import 'package:and/cache/cache_helper.dart';
import 'package:and/l10n/generated/app_localizations.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/l10n/language.dart';
import 'package:and/l10n/language_utils.dart';
import 'package:and/utils/http_utils.dart';
import 'package:flutter/material.dart';

class LanguagePage extends StatefulWidget {
  const LanguagePage({super.key});

  @override
  State<StatefulWidget> createState() {
    return _LanguagePageState();
  }
}

class _LanguagePageState extends State<LanguagePage> {
  var _currentLocale = CacheHelper.currentCountryCode;
  List<Locale> locales = AppLocalizations.supportedLocales;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: Text(context.l10n.mulitLanguage),
        ),
        body: _buildContent());
  }

  Widget _buildContent() {
    return Column(
      children: [_buildBaseInfo()],
    );
  }

  Widget _buildBaseInfo() {
    return Expanded(
        child: ListView(
      children: _buildWidgets(),
    ));
  }

  bool _isSelectedLocal(Language language) {
    return language.countryCode == _currentLocale;
  }

  List<Widget> _buildWidgets() {
    List<Widget> widgets = [];
    for (var language in Language.values) {
      var code = language.countryCode;
      var isSelected = _isSelectedLocal(language);
      var languageContent = LanguageUtils.getLanguage(context, code);
      if (!isSelected && language.countryCode != Language.fsLan.countryCode) {
        languageContent = "$languageContent - ${language.title}";
      }
      widgets.add(ListTile(
          title: Text(languageContent),
          trailing: _isSelectedLocal(language)
              ? Icon(Icons.check, color: Colors.blue)
              : null,
          onTap: () {
            if (_currentLocale == language.countryCode) {
              return;
            }
            _currentLocale = language.countryCode;
            LanguageUtils.updateLocale(language.countryCode);

            var fcmToken = CacheHelper.deviceToken;
            if (fcmToken?.isNotEmpty ?? false) {
              HttpUtils.uploadDeviceToken(fcmToken);
            }
          }));
    }

    return widgets;
  }
}
