import 'dart:io';
import 'dart:math';
import 'package:and/constant/common_keys.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/module/webview/in_app_browser.dart';
import 'package:and/router/router.dart';
import 'package:and/utils/common_helper.dart';
import 'package:and/utils/http_utils.dart';
import 'package:and/utils/notification_utils.dart';
import 'package:and/widget/simple_setting_item_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:get/get.dart';
import 'package:oktoast/oktoast.dart';
import 'package:package_info_plus/package_info_plus.dart';

class SecurityPrivacyPage extends StatefulWidget {
  const SecurityPrivacyPage({super.key});

  @override
  State<StatefulWidget> createState() {
    return _SecurityPrivacyPageState();
  }
}

class _SecurityPrivacyPageState extends State<SecurityPrivacyPage> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: Text(context.l10n.securityAndPrivacy),
        ),
        body: _buildContent());
  }

  Widget _buildContent() {
    return Column(
      children: [_buildBaseInfo()],
    );
  }

  Widget _buildBaseInfo() {
    final List<Widget> items = [
      // SimpleSettingItemWidget(context.l10n.resetPassword, onTap: () {
      //   Get.toNamed(RouteGet.forgotPwd);
      // }),

      // SimpleSettingItemWidget(context.l10n.personalInfoCollection,
      //     onTap: () {
      //       Get.toNamed(RouteGet.infoCollection);
      //     }),

      SimpleSettingItemWidget(context.l10n.userAgreement, onTap: () {
        CommonHelper.launchInWebView(CommonKeys.userAgreement,
            title: context.l10n.userAgreement, appBarType: AppBarType.normal);
      }),

      SimpleSettingItemWidget(context.l10n.privacyPolicy, onTap: () {
        CommonHelper.launchInWebView(CommonKeys.privacyPolicy,
            title: context.l10n.privacyPolicy, appBarType: AppBarType.normal);
      }),

      SimpleSettingItemWidget(context.l10n.destroyAccount, onTap: () {
        _confirmDestoryAccount();
      }),
    ];

    return Expanded(
        child: ListView.builder(
            itemCount: items.length,
            itemBuilder: (context, index) {
              return items[index];
            }));
  }

  /// 确认注销账号
  void _confirmDestoryAccount() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(context.l10n.makeSureDestoryAccount),
        content: Text(context.l10n.destoryAccountTip),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(context.l10n.globalCancel),
          ),
          TextButton(
            onPressed: () {
              Get.offAndToNamed(RouteGet.destroyAccount);
            },
            child:
                Text(context.l10n.destory, style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }
}
