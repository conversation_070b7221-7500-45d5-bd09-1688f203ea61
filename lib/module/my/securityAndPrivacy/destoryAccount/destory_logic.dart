import 'dart:async';

import 'package:and/app.dart';
import 'package:and/cache/cache_helper.dart';
import 'package:and/common/utils/easy_loading_helper.dart';
import 'package:and/http/my_http.dart';
import 'package:and/io/user.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/utils/common_helper.dart';
import 'package:and/utils/http_utils.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:oktoast/oktoast.dart';

class DestoryLogic extends GetxController {
  Timer? _timer;
  final Rx<int> secondsRemaining = Rx<int>(-1);

  String get areaCodeDisplay =>
      "+${CacheHelper.userProfile?.zone} ${CacheHelper.userProfile?.phone}";

  void sendSMS() async {
    var response = await HttpUtils.sendDestroyAccountSMS();
    if (response && globalContext != null) {
      startCountdown();
      EasyLoading.showSuccess(
          globalContext!.l10n.signupVerifyCodeDesc(areaCodeDisplay));
    } else {
      EasyLoading.dismiss();
    }
  }

  Future<void> destroy(String code) async {
    final result = await HttpUtils.destroyAccount(code);
    if (result) {
      if (globalContext != null) {
        showToast(globalContext!.l10n.destorySuccess);
      }
      CommonHelper.exitLogin();
    }
  }

  void startCountdown() {
    secondsRemaining.value = 60;
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (secondsRemaining.value > 0) {
        secondsRemaining.value--;
      } else {
        _timer?.cancel();
      }
    });
  }

  void stopCountdown() {
    _timer?.cancel();
  }

  @override
  void dispose() {
    stopCountdown();
    super.dispose();
  }
}
