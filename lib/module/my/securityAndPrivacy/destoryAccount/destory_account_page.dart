import 'package:and/l10n/l10n.dart';
import 'package:and/module/my/securityAndPrivacy/destoryAccount/destory_logic.dart';
import 'package:and/utils/http_utils.dart';
import 'package:and/utils/image_path.dart';
import 'package:and/widget/input_form_widget.dart';
import 'package:and/widget/submit_button.dart';
import 'package:flutter/material.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:get/get.dart';
import 'package:oktoast/oktoast.dart';

class DestoryAccountPage extends StatefulWidget {
  const DestoryAccountPage({super.key});

  @override
  State<StatefulWidget> createState() {
    return _DestoryAccountPageState();
  }
}

class _DestoryAccountPageState extends State<DestoryAccountPage>
    with SingleTickerProviderStateMixin {
  final logic = Get.find<DestoryLogic>();
  late Rx<int> secondsRemaining = logic.secondsRemaining;

  final _codeTextController = TextEditingController();

  final GlobalKey<FormState> _codeKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    // logic.sendSMS();
  }
  @override
  void dispose() {
    // Clean up the controller when the widget is disposed.
    _codeTextController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
          title: Text(context.l10n.destroyAccount),
        ),
        body: Container(
          color: Colors.white,
          padding: const EdgeInsets.all(30),
          child: _buildContent(),
        ));
  }

  Widget _buildContent() {
    return SafeArea(
        child: SizedBox.expand(
      child: SingleChildScrollView(
        child: Column(
          children: [
            Obx(() => _buildCode()),
            const SizedBox(height: 60),
            _buildDestoryAccountButton(),
            const SizedBox(height: 20),
          ],
        ),
      ),
    ));
  }

  Widget _buildDestoryAccountButton() {
    return SizedBox(
        width: double.infinity,
        child: OutlinedButton(
          style: OutlinedButton.styleFrom(
            foregroundColor: Colors.red,
            side: const BorderSide(color: Colors.red),
          ),
          onPressed: _destoryUser,
          child: Text(context.l10n.destroyAccount,
              style: const TextStyle(fontSize: 16)),
        ));
  }

  void _destoryUser() {
    if (_codeTextController.text.isEmpty) {
      showToast(context.l10n.inputVerifyCode);
      return;
    }
    logic.destroy((_codeTextController.text));
  }

  Widget _buildCode() {
    var remainTime = secondsRemaining.value;

    var isEnable = remainTime <= 0;
    return Row(
      children: [
        Expanded(
            child: InputTextForm(
                formKey: _codeKey,
                validator: FormBuilderValidators.required(
                    errorText: context.l10n.destoryVerifyCodeHint),
                hint: context.l10n.destoryVerifyCodeHint,
                value: "",
                keyboardType: TextInputType.text,
                controller: _codeTextController,
                onChanged: (value) {
                  setState(() {});
                })),
        SizedBox(width: 10),
        SubmitButton(
            enable: isEnable,
            text: remainTime <= 0
                ? context.l10n.sendCode
                : context.l10n.secondsRemaining(remainTime),
            textColor: Colors.white,
            backgroundColor: Colors.black,
            textSize: 14,
            onPressed: () async {
              logic.sendSMS();
            })
      ],
    );
  }
}
