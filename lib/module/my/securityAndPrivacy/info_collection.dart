import 'package:and/l10n/l10n.dart';
import 'package:flutter/material.dart';

class InfoCollectionPage extends StatefulWidget {
  const InfoCollectionPage({super.key});

  @override
  State<StatefulWidget> createState() {
    return _InfoCollectionPageState();
  }
}

class _InfoCollectionPageState extends State<InfoCollectionPage> {

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: Text(context.l10n.securityAndPrivacy),
        ),
        body: _buildContent());
  }

  Widget _buildContent() {
    return Column(
      children: [_buildBaseInfo()],
    );
  }

  Widget _buildBaseInfo() {
    final List<Widget> items = [

    ];

    return Expanded(
        child: ListView.builder(
            itemCount: items.length,
            itemBuilder: (context, index) {
              return items[index];
            }));
  }
}
