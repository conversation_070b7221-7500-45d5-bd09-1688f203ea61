import 'dart:io';
import 'dart:math';
import 'package:and/constant/common_keys.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/module/webview/in_app_browser.dart';
import 'package:and/router/router.dart';
import 'package:and/utils/common_helper.dart';
import 'package:and/utils/http_utils.dart';
import 'package:and/utils/notification_utils.dart';
import 'package:and/widget/input_form_widget.dart';
import 'package:and/widget/simple_setting_item_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:get/get.dart';
import 'package:oktoast/oktoast.dart';
import 'package:package_info_plus/package_info_plus.dart';

class ModifyPasswordPage extends StatefulWidget {
  const ModifyPasswordPage({super.key});

  @override
  State<StatefulWidget> createState() {
    return _ModifyPasswordPageState();
  }
}

class _ModifyPasswordPageState extends State<ModifyPasswordPage> {

  final _pwdTextController = TextEditingController();
  final _newPwdTextController = TextEditingController();

  final GlobalKey<FormState> _pwdKey = GlobalKey<FormState>();
  final GlobalKey<FormState> _newPwdKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    // Clean up the controller when the widget is disposed.
    _pwdTextController.dispose();
    _newPwdTextController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: Text(context.l10n.resetPassword),
        ),
        body: Container(
          color: Colors.white,
          padding: const EdgeInsets.all(30),
          child: Obx(() => _buildContent()),
        ));
  }

  Widget _buildContent() {
    return SafeArea(
      child: SizedBox.expand(
        child: Stack(
          children: [
            Container(
              color: Colors.white,
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                child: Column(
                  children: [
                    _buildPassword(),
                    const SizedBox(height: 20),
                    _buildNewPassword(),
                    const SizedBox(height: 20),
                  ],
                ),
              ),
            ),

          ],
        ),
      ),
    );
  }

  Widget _buildPassword() {
    return InputTextForm(
        formKey: _pwdKey,
        validator: FormBuilderValidators.minLength(6,
            errorText: context.l10n.signupPasswordLengthError(6)),
        hint: context.l10n.signupPasswordPlaceholder,
        value: "",
        obscureText: true,
        keyboardType: TextInputType.text,
        controller: _pwdTextController,
        onChanged: (value) {
          setState(() {});
        });
  }

  Widget _buildNewPassword() {
    return InputTextForm(
        formKey: _newPwdKey,
        validator: FormBuilderValidators.minLength(6,
            errorText: context.l10n.signupPasswordLengthError(6)),
        hint: context.l10n.signupPasswordPlaceholder,
        value: "",
        obscureText: true,
        keyboardType: TextInputType.text,
        controller: _pwdTextController,
        onChanged: (value) {
          setState(() {});
        });
  }
}
