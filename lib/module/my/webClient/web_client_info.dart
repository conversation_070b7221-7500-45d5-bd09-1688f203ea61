import 'package:and/common/res/colours.dart';
import 'package:and/constant/common_keys.dart';
import 'package:and/http/http_config.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/module/user/mine/my_info_logic.dart';
import 'package:and/router/router.dart';
import 'package:and/utils/image_path.dart';
import 'package:and/utils/lottie_path.dart';
import 'package:flutter/material.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:lottie/lottie.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:and/widget/simple_setting_item_widget.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:oktoast/oktoast.dart';

class WebClientPage extends StatefulWidget {
  const WebClientPage({super.key});

  @override
  State<StatefulWidget> createState() {
    return _WebClientPageState();
  }
}

class _WebClientPageState extends State<WebClientPage> {
  late MyInfoLogic logic = Get.find<MyInfoLogic>();
  late var item = logic.data;
  var _appName = 'Gleezy';

  @override
  void initState() {
    super.initState();

    _getaAppName();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: const Text("网页端"),
        ),
        body: _buildContent());
  }

  Widget _buildContent() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Lottie.asset(LottiePath.qrcode_web,
              repeat: true, width: 150, height: 150),
          const SizedBox(height: 12),
          Text(
            context.l10n.appWeb(_appName),
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 24),
          Row(
            children: [
              const Icon(Icons.desktop_windows,
                  color: Colors.green, size: 24),
              const SizedBox(width: 5),
              Text(
                context.l10n.webUrl,
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.normal),
              ),
              IconButton(
                icon: const Icon(Icons.copy, color: Colors.blueAccent),
                onPressed: () {
                  Clipboard.setData(ClipboardData(text: CommonKeys.webUrl));
                  showToast(context.l10n.copySuccess);
                },
              ),
              Expanded(child:
              Text(
                CommonKeys.webUrl,
                style: const TextStyle(color: Colors.grey, fontSize: 12),
                maxLines: 2,
                textAlign: TextAlign.start,
              )),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            context.l10n.visitWebTip(_appName),
            style: const TextStyle(color: Colors.grey, fontSize: 14),
            textAlign: TextAlign.start,
          ),
          const SizedBox(height: 12),
          InkWell(
            child: Row(
              children: [
                Image.asset(ImagePath.ic_scan, width: 24, height: 24,),
                const SizedBox(width: 5),
                Text(context.l10n.qrScanLogin,
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.normal),),
                Spacer(),
                Icon(
                  Icons.arrow_forward_ios,
                  size: 16,
                  color: DColor.grey50,
                )
              ],
            ),
            onTap: () {
              Get.toNamed(RouteGet.scan);
            },
          )
        ],
      ),
    );
  }

  Future<void> _getaAppName() async {
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    if (mounted) {
      setState(() {
        _appName = packageInfo.appName;
      });
    }
  }
}
