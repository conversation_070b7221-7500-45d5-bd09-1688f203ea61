import 'package:and/common/res/colours.dart';
import 'package:and/constant/common_keys.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/model/request/send_sms_request.dart';
import 'package:and/module/webview/in_app_browser.dart';
import 'package:and/router/router.dart';
import 'package:and/utils/common_helper.dart';
import 'package:and/utils/http_utils.dart';
import 'package:and/utils/image_path.dart';
import 'package:and/widget/input_form_widget.dart';
import 'package:and/widget/submit_button.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:get/get.dart';

import 'register_logic.dart';

class RegisterPage extends StatefulWidget {
  const RegisterPage({super.key});

  @override
  State<StatefulWidget> createState() {
    return _RegisterPhonePageState();
  }
}

class _RegisterPhonePageState extends State<RegisterPage>
    with SingleTickerProviderStateMixin {
  final logic = Get.find<RegisterLogic>();
  late Rx<int> secondsRemaining = logic.secondsRemaining;

  final _emailTextController = TextEditingController();
  final _phoneTextController = TextEditingController();
  final _codeTextController = TextEditingController();
  final _pwdTextController = TextEditingController();
  final _inviteCodeTextController = TextEditingController();

  final GlobalKey<FormState> _emailKey = GlobalKey<FormState>();
  final GlobalKey<FormState> _phoneKey = GlobalKey<FormState>();
  final GlobalKey<FormState> _codeKey = GlobalKey<FormState>();
  final GlobalKey<FormState> _pwdKey = GlobalKey<FormState>();
  final GlobalKey<FormState> _inviteKey = GlobalKey<FormState>();

  @override
  void dispose() {
    // Clean up the controller when the widget is disposed.
    _codeTextController.dispose();
    _emailTextController.dispose();
    _phoneTextController.dispose();
    _pwdTextController.dispose();
    _inviteCodeTextController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Colors.white,
        body: Container(
          color: Colors.white,
          padding: const EdgeInsets.all(30),
          child: Obx(() => _buildContent()),
        ));
  }

  Widget _buildContent() {
    return SafeArea(
      child: SizedBox.expand(
        child: Stack(
          children: [
            Column(
              children: [
                Spacer(),
                _buildMore(),
                const SizedBox(height: 20),
              ],
            ),
            Container(
              color: Colors.white,
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                child: Column(
                  children: [
                    Image.asset(ImagePath.ic_logo, height: 120),
                    const SizedBox(height: 40),
                    _buildPhone(),
                    const SizedBox(height: 20),
                    _buildPassword(),
                    const SizedBox(height: 20),
                    _buildCode(),
                    const SizedBox(height: 20),
                    _buildInviteCode(),
                    const SizedBox(height: 20),
                    _buildRegisterButton(),
                    const SizedBox(height: 20),
                    _buildLoginButton(),
                    const SizedBox(height: 120),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoginButton() {
    return Center(
      child: GestureDetector(
        onTap: () {
          Get.back();
        },
        child: Text.rich(
          TextSpan(
            text: context.l10n.hadAccount,
            style: TextStyle(color: Colors.black87),
            children: [
              TextSpan(
                text: context.l10n.loginTitle,
                style: TextStyle(
                    color: DColor.primaryColor, fontWeight: FontWeight.bold),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 用户协议 & 隐私政策
  Widget _buildMore() {
    return Center(
      child: Text.rich(
        TextSpan(
          text: context.l10n.tapAndAgree,
          style: TextStyle(fontSize: 12, color: Colors.grey),
          children: [
            TextSpan(
              text: " ${context.l10n.userAgreement} ",
              style: TextStyle(color: DColor.primaryColor),
              recognizer: TapGestureRecognizer()
                ..onTap = () {
                  CommonHelper.launchInWebView(CommonKeys.userAgreement,
                      title: context.l10n.userAgreement,
                      appBarType: AppBarType.normal);
                },
            ),
            TextSpan(text: context.l10n.and),
            TextSpan(
              text: " ${context.l10n.privacyPolicy}",
              style: TextStyle(color: DColor.primaryColor),
              recognizer: TapGestureRecognizer()
                ..onTap = () {
                  CommonHelper.launchInWebView(CommonKeys.privacyPolicy,
                      title: context.l10n.privacyPolicy,
                      appBarType: AppBarType.normal);
                },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRegisterButton() {
    return SubmitButton(
        enable: true,
        text: context.l10n.signupSignupButton,
        textColor: Colors.white,
        backgroundColor: Colors.black,
        textSize: 20,
        onPressed: () async {
          var allValidList = [
            _phoneKey,
            _pwdKey,
            _codeKey,
          ];

          var isValid = allValidList
              .every((element) => element.currentState?.validate() ?? false);
          if (isValid) {
            await HttpUtils.phoneRegister(
                zone: logic.areaCode.value,
                phone: _phoneTextController.text,
                password: _pwdTextController.text,
                code: _codeTextController.text,
                inviteCode: _inviteCodeTextController.text);
          }
        });
  }

  Widget _buildEmail() {
    return InputTextForm(
        formKey: _emailKey,
        validator: FormBuilderValidators.email(
            errorText: context.l10n.signupEmailErrorFormat),
        hint: context.l10n.signupEmailPlaceholder,
        value: "",
        keyboardType: TextInputType.emailAddress,
        controller: _emailTextController,
        onChanged: (value) {
          setState(() {});
        });
  }

  Widget _buildPhone() {
    return InputTextForm(
        formKey: _phoneKey,
        validator: FormBuilderValidators.phoneNumber(
            errorText: context.l10n.signupPhoneErrorFormat),
        hint: context.l10n.signupPhonePlaceholder,
        value: "",
        keyboardType: TextInputType.phone,
        controller: _phoneTextController,
        prefixIcon: InkWell(
          onTap: () async {
            var code = await Get.toNamed(RouteGet.chooseCountry);
            logic.setAreaCode(code);
          },
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 8),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(logic.areaCodeDisplay),
                Icon(Icons.arrow_drop_down)
              ],
            ),
          ),
        ),
        onChanged: (value) {
          setState(() {});
        });
  }

  Widget _buildPassword() {
    return InputTextForm(
        formKey: _pwdKey,
        validator: FormBuilderValidators.minLength(6,
            errorText: context.l10n.signupPasswordLengthError(6)),
        hint: context.l10n.signupPasswordPlaceholder,
        value: "",
        obscureText: true,
        keyboardType: TextInputType.text,
        controller: _pwdTextController,
        onChanged: (value) {
          setState(() {});
        });
  }

  Widget _buildCode() {
    var remainTime = secondsRemaining.value;

    var isEnable = _phoneTextController.text.isNotEmpty && remainTime <= 0;
    return Row(
      children: [
        Expanded(
            child: InputTextForm(
                formKey: _codeKey,
                validator: FormBuilderValidators.required(
                    errorText: context.l10n.signupVerifyCodeHint),
                hint: context.l10n.signupVerifyCodeHint,
                value: "",
                keyboardType: TextInputType.text,
                controller: _codeTextController,
                onChanged: (value) {
                  setState(() {});
                })),
        SizedBox(width: 10),
        SubmitButton(
            enable: isEnable,
            text: remainTime <= 0
                ? context.l10n.sendCode
                : context.l10n.secondsRemaining(remainTime),
            textColor: Colors.white,
            backgroundColor: Colors.black,
            textSize: 14,
            onPressed: () async {
              var allValidList = [
                _phoneKey,
              ];
              var isValid = allValidList.every(
                  (element) => element.currentState?.validate() ?? false);
              if (isValid) {
                await HttpUtils.sendSMS(
                    zone: logic.areaCode.value,
                    phone: _phoneTextController.text,
                    codeType: SendCodeType.register,
                    onSuccess: () {
                      logic.startCountdown();
                    });
              }
            })
      ],
    );
  }

  Widget _buildInviteCode() {
    return InputTextForm(
        formKey: _inviteKey,
        validator: FormBuilderValidators.compose([
          FormBuilderValidators.minLength(4,
              errorText: context.l10n.moreThanCharacters(4)),
          FormBuilderValidators.maxLength(8,
              errorText: context.l10n.lessThanCharacters(8)),
          FormBuilderValidators.match(
            RegExp(r'^[a-zA-Z0-9]{4,8}$'),
            errorText: context.l10n.onlyCharactersAndNumbers,
          ),
        ]),
        hint: context.l10n.signupInvitationPlaceholder,
        value: "",
        keyboardType: TextInputType.text,
        controller: _inviteCodeTextController,
        onChanged: (value) {
          setState(() {});
        });
  }
}
