import 'package:and/app.dart';
import 'package:and/common/utils/easy_loading_helper.dart';
import 'package:and/http/my_http.dart';
import 'package:and/io/group.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/model/group_detail.dart';
import 'package:and/module/chat/chat_page.dart';
import 'package:and/module/common/controller/simple_controller.dart';
import 'package:and/module/group/join/group_join_page.dart';
import 'package:and/router/router.dart';
import 'package:and/utils/http_utils.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:wukongimfluttersdk/manager/conversation_manager.dart';
import 'package:wukongimfluttersdk/type/const.dart';
import 'package:wukongimfluttersdk/wkim.dart';

class GroupJoinLogic extends SimpleController<GroupDetail> {
  final String key = 'group_join';
  final GroupJoinArgument argument;

  GroupJoinLogic({required this.argument});

  @override
  void onReady() async {
    super.onReady();

    startRefresh();
  }

  void startRefresh() {
    EasyLoadingHelper.show(onAction: () async {
      await refreshData();
    });
  }

  @override
  Future<GroupDetail?> loadData() async {
    var channel = HttpUtils.getGroupDetail(argument.groupNo);
    return channel;
  }

  void joinGroup() async {
    EasyLoadingHelper.show(onAction: () async {
      var result = await GroupApi(MyHttp.dio)
          .scanJoin(argument.groupNo, argument.authCode);
      if (result.success) {
        Get.back();
        ChatPage.open(
            channelID: argument.groupNo, channelType: WKChannelType.group);
      } else {
        EasyLoading.showError(globalContext!.l10n.joinGroupFail);
      }
    }, onError: (e) {
      EasyLoading.showError(globalContext!.l10n.joinGroupFail);
    });
  }
}
