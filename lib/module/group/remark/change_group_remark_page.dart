import 'package:and/common/res/colours.dart';
import 'package:and/common/res/text_styles.dart';
import 'package:and/constant/group_info_keys.dart';
import 'package:and/constant/group_setting_keys.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/model/extension/wk_channel_ext.dart';
import 'package:and/module/group/group_detail_logic.dart';
import 'package:and/widget/avatar_widget.dart';
import 'package:and/widget/input_form_widget.dart';
import 'package:and/widget/submit_button.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:wukongimfluttersdk/wkim.dart';

class ChangeGroupRemarkPage extends StatefulWidget {
  final String tag;

  const ChangeGroupRemarkPage({super.key, required this.tag});

  @override
  State<StatefulWidget> createState() {
    return _ChangeGroupRemarkPageState();
  }
}

class _ChangeGroupRemarkPageState extends State<ChangeGroupRemarkPage> {
  late GroupDetailLogic logic = Get.find<GroupDetailLogic>(tag: widget.tag);
  late final channel = logic.channel;

  final _remarkTextController = TextEditingController();

  final GlobalKey<FormState> _remarkKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    _remarkTextController.text = channel.value.channelRemark ?? '';
  }

  @override
  void dispose() {
    _remarkTextController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(),
        body: Padding(
          padding: EdgeInsets.symmetric(horizontal: 15),
          child: Column(
            children: [
              SizedBox(height: 20),
              Text(context.l10n.remark,
                  style: TextStyles.fontSize18Bold.copyWith(fontSize: 24)),
              SizedBox(
                height: 20,
              ),
              Text(context.l10n.remarkDesc, style: TextStyles.fontSize15Normal),
              SizedBox(height: 5),
              Divider(color: DColor.divider),
              Row(
                children: [
                  AvatarWidget(channel.value.avatarUrl, size: 50),
                  SizedBox(
                    width: 10,
                  ),
                  Expanded(child: _buildRemarkInput())
                ],
              ),
              Divider(color: DColor.divider),
              SizedBox(height: 5),
              Row(
                children: [
                  Flexible(
                      flex: 1,
                      child: Text(
                          "${context.l10n.groupName}: ${channel.value.channelName}",
                          style: TextStyles.fontSize13Normal
                              .copyWith(color: DColor.secondaryTextColor))),
                  SizedBox(width: 10),
                  InkWell(
                      onTap: () {
                        _remarkTextController.text = channel.value.channelName;
                      },
                      child: Text(context.l10n.enterGroupName,
                          style: TextStyles.fontSize13Normal
                              .copyWith(color: DColor.primaryColor)))
                ],
              ),
              SizedBox(height: 30),
              _buildSave()
            ],
          ),
        ));
  }

  Widget _buildSave() {
    return SubmitButton(
        enable: channel.value.channelRemark != _remarkTextController.text &&
            _remarkTextController.text.isNotEmpty,
        onPressed: () async {
          var result = await logic.updateGroupSetting(
              GroupSettingKeys.remark, _remarkTextController.text);

          if (result) {
            channel.value.channelRemark = _remarkTextController.text;
            WKIM.shared.channelManager.addOrUpdateChannel(channel.value);
            Get.back();
          }
        },
        text: context.l10n.globalSave);
  }

  Widget _buildRemarkInput() {
    return TextFormField(
      key: _remarkKey,
      controller: _remarkTextController,
      autofocus: true,
      onChanged: (value) {
        setState(() {

        });
      },
      onFieldSubmitted: (value) {},
      textInputAction: TextInputAction.done,
      decoration: InputDecoration(
        border: InputBorder.none,
        hintText: context.l10n.remark,
        hintStyle: TextStyles.fontSize15Normal
            .copyWith(color: DColor.hintText.withOpacity(0.2)),
      ),
    );
  }
}
