import 'package:and/common/res/text_styles.dart';
import 'package:and/constant/group_info_keys.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/model/extension/wk_channel_ext.dart';
import 'package:and/module/group/group_detail_logic.dart';
import 'package:and/widget/input_form_widget.dart';
import 'package:and/widget/submit_button.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:wukongimfluttersdk/wkim.dart';

class ChangeGroupNamePage extends StatefulWidget {
  final String tag;

  const ChangeGroupNamePage({super.key, required this.tag});

  @override
  State<StatefulWidget> createState() {
    return _ChangeGroupNamePagePageState();
  }
}

class _ChangeGroupNamePagePageState extends State<ChangeGroupNamePage> {
  late GroupDetailLogic logic = Get.find<GroupDetailLogic>(tag: widget.tag);
  late final channel = logic.channel;

  final _nameTextController = TextEditingController();

  final GlobalKey<FormState> _nameKey = GlobalKey<FormState>();

  @override
  void dispose() {
    _nameTextController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: Text(context.l10n.groupCard),
          actions: [
            Padding(
              padding: EdgeInsets.only(right: 15),
              child: _buildSave(),
            )
          ],
        ),
        body: Padding(
          padding: EdgeInsets.symmetric(horizontal: 15),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(context.l10n.groupName, style: TextStyles.fontSize16Normal),
              SizedBox(height: 10),
              _buildNameInput()
            ],
          ),
        ));
  }

  Widget _buildSave() {
    return SubmitButton(
        enable: channel.value.displayName != _nameTextController.text &&
            _nameTextController.text.isNotEmpty,
        padding: EdgeInsets.symmetric(horizontal: 10, vertical: 6),
        onPressed: () async {
          var result = await logic.updateGroupInfo(
              GroupInfoKeys.name, _nameTextController.text);

          if (result) {
            channel.value.channelName = _nameTextController.text;
            WKIM.shared.channelManager.addOrUpdateChannel(channel.value);
            Get.back();
          }
        },
        text: context.l10n.globalSave);
  }

  Widget _buildNameInput() {
    return InputTextForm(
        formKey: _nameKey,
        hint: context.l10n.groupName,
        value: channel.value.displayName,
        autofocus: true,
        keyboardType: TextInputType.text,
        controller: _nameTextController,
        textInputAction: TextInputAction.done,
        contentPadding: const EdgeInsets.all(10),
        maxLines: 1,
        onFieldSubmitted: (value) {},
        onChanged: (value) {
          setState(() {});
        });
  }
}
