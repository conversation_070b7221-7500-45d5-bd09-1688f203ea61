import 'package:and/constant/wk_channel_member_role.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/module/group/group_detail_logic.dart';
import 'package:and/module/group/group_detail_page.dart';
import 'package:and/module/group/widget/ui_channel_member_grid_item.dart';
import 'package:and/module/search/widget/search_input_widget.dart';
import 'package:and/utils/image_path.dart';
import 'package:and/widget/refresh/load_state.dart';
import 'package:and/widget/refresh/refresh_page.dart';
import 'package:and/widget/refresh/refresh_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:get/get.dart';

class GroupMembersPage extends StatefulWidget {
  final GroupDetailArgument argument;

  const GroupMembersPage({super.key, required this.argument});

  @override
  State<StatefulWidget> createState() {
    return _GroupMembersPageState();
  }
}

class _GroupMembersPageState extends RefreshState<GroupMembersPage> {
  late GroupDetailLogic logic =
      Get.find<GroupDetailLogic>(tag: widget.argument.getTag());
  late final list = logic.filterMembers;
  late var currentMember = logic.currentMember;

  @override
  void initState() {
    super.initState();
    logic.search("");
  }

  @override
  Rx<FitLoadStatus> getLoadState() {
    return logic.loadStatus;
  }

  @override
  void didUpdateWidget(covariant GroupMembersPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Scaffold(
        appBar: AppBar(title: Obx(() => _buildTitle())),
        body: Column(
          children: [
            Container(
                margin: EdgeInsets.symmetric(horizontal: 15),
                child: _buildSearchInput()),
            SizedBox(height: 10),
            Obx(() => Expanded(child: _buildRefreshList()))
          ],
        ));
  }

  Widget _buildTitle() {
    var title = context.l10n.groupMembers;
    if (list.isNotEmpty) {
      title += "(${list.length})";
    }

    return Text(title);
  }

  Widget _buildSearchInput() {
    return SearchInputWidget(
      searchPlaceHolder: context.l10n.search,
      showImage: false,
      autofocus: false,
      onKeyChanged: (key) {
        logic.search(key);
      },
      onFieldSubmitted: (key) {
        logic.search(key);
      },
    );
  }

  Widget _buildRefreshList() {
    return buildRefreshWidget(
      refreshController: refreshController,
      loadStatus: logic.loadStatus.value,
      hasData: list.isNotEmpty,
      builder: (physics) => _buildList(physics),
      onRefresh: () {
        logic.refreshData();
      },
    );
  }

  Widget _buildList(ScrollPhysics? physics) {
    var count = list.length + 1;
    if (currentMember.value?.role != WKChannelMemberRole.normal) {
      count++;
    }
    return CustomScrollView(
      slivers: [
        SliverPadding(
            padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 15),
            sliver: SliverMasonryGrid(
                gridDelegate: SliverSimpleGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 6,
                ),
                delegate: SliverChildBuilderDelegate(
                    (BuildContext context, int index) {
                  return _buildItem(index);
                }, childCount: count)))
      ],
    );
  }

  Widget _buildItem(int index) {
    var isAddCount = index == list.length;
    if (isAddCount) {
      return _buildAddCount();
    }
    var isDeleteCount = index == list.length + 1;
    if (isDeleteCount) {
      return _buildDeleteCount();
    }

    var item = list[index];
    return UiChannelMemberGridItem(
        item: item, groupNo: widget.argument.groupNo);
  }

  Widget _buildAddCount() {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        logic.addGroupMembers();
      },
      child: Column(
        children: [
          Image.asset(ImagePath.ic_chat_add, height: 45),
          SizedBox(height: 30),
        ],
      ),
    );
  }

  Widget _buildDeleteCount() {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        logic.deleteGroupMembers();
      },
      child: Column(
        children: [
          Image.asset(ImagePath.ic_chat_delete, height: 45),
          SizedBox(height: 30),
        ],
      ),
    );
  }
}
