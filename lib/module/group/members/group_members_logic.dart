import 'package:and/module/common/controller/list_controller.dart';
import 'package:and/module/group/group_detail_page.dart';
import 'package:get/get.dart';
import 'package:wukongimfluttersdk/entity/channel_member.dart';
import 'package:wukongimfluttersdk/type/const.dart';
import 'package:wukongimfluttersdk/wkim.dart';

class GroupMembersLogic extends ListController<WKChannelMember> {
  final String key = "ui_group_members";

  final GroupDetailArgument argument;
  final filterKey = "".obs;

  GroupMembersLogic({required this.argument});

  @override
  void onReady() {
    super.onReady();
    _initListener();
  }

  @override
  void onClose() {
    _removeListener();
    super.onClose();
  }

  @override
  Future<List<WKChannelMember>?> loadData() async {
    var members = await WKIM.shared.channelMemberManager
            .getMembers(argument.groupNo, WKChannelType.group) ??
        [];
    if (filterKey.isNotEmpty) {
      members = members.where((e) {
        return e.memberName.contains(filterKey.value);
      }).toList();
    }
    members.sort((a, b) {
      return b.role - a.role;
    });

    return members;
  }

  void search(String key) {
    filterKey.value = key;
    refreshData();
  }

  _initListener() {
    // 监听刷新member事件
    WKIM.shared.channelMemberManager.addOnRefreshMemberListener(key,
        (member, isEnd) {
      print("刷新member事件");

      if (member.channelID == argument.groupNo) {
        refreshData();
      }
    });

    // 监听新增member事件
    WKIM.shared.channelMemberManager.addOnNewMemberListener(key, (members) {
      print("新增member事件");

      var isAdd = members.any((e) => e.channelID == argument.groupNo);
      if (isAdd) {
        refreshData();
      }
    });

    // 监听删除member事件
    WKIM.shared.channelMemberManager.addOnDeleteMemberListener(key, (members) {
      print("删除member事件");

      var isDelete = members.any((e) => e.channelID == argument.groupNo);
      if (isDelete) {
        refreshData();
      }
    });
  }

  _removeListener() {
    WKIM.shared.channelMemberManager.removeRefreshMemberListener(key);
    WKIM.shared.channelMemberManager.removeNewMemberListener(key);
    WKIM.shared.channelMemberManager.removeDeleteMemberListener(key);
  }
}
