import 'dart:io';
import 'dart:ui' as ui;

import 'package:and/common/res/colours.dart';
import 'package:and/common/res/text_styles.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/model/extension/wk_channel_ext.dart';
import 'package:and/module/group/qr/group_qr_logic.dart';
import 'package:and/utils/image_utils.dart';
import 'package:and/widget/avatar_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pretty_qr_code/pretty_qr_code.dart';

class GroupQrPage extends StatefulWidget {
  const GroupQrPage({super.key});

  @override
  State<StatefulWidget> createState() {
    return _GroupQrPageState();
  }
}

class _GroupQrPageState extends State<GroupQrPage> {
  late GroupQrLogic logic = Get.find<GroupQrLogic>();
  late final item = logic.data;
  late final channel = logic.channel;
  final GlobalKey _qrCodeKey = GlobalKey();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: Text(context.l10n.groupQr),
          actions: [
            Padding(
              padding: EdgeInsets.only(right: 8.0),
              child: IconButton(
                icon: Icon(Icons.save_alt),
                onPressed: () => _saveQrCode(),
              ),
            ),
          ],
        ),
        body: Obx(() => SafeArea(
            child: RepaintBoundary(
                key: _qrCodeKey,
                child: Center(
                  child: _buildContent(),
                )))));
  }

  Widget _buildContent() {
    return Container(
      color: Colors.white,
      padding: EdgeInsets.symmetric(vertical: 25),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          AvatarWidget(channel.value.avatarUrl, size: 50),
          SizedBox(height: 10),
          Text(channel.value.channelName, style: TextStyles.fontSize16Normal),
          SizedBox(height: 30),
          GestureDetector(
            onLongPress: () => _showSaveOptions(),
            child: _buildUserQr(),
          ),
          SizedBox(height: 20),
          // Visibility(
          //     visible: item.value != null,
          //     child: Text(
          //         context.l10n.groupQrDesc(
          //             item.value?.expire ?? "", item.value?.day ?? ""),
          //         style: TextStyles.fontSize15Normal
          //             .copyWith(color: DColor.secondaryTextColor))),
        ],
      ),
    );
  }

  Widget _buildUserQr() {
    bool hasQrCode = item.value?.qrcode.isNotEmpty ?? false;

    return Center(
      child: SizedBox(
        height: 250,
        child: hasQrCode
            ? PrettyQrView.data(
                data: item.value?.qrcode ?? "",
                decoration: const PrettyQrDecoration(
                  shape: PrettyQrSmoothSymbol(roundFactor: 0),
                ),
              )
            : LoadingAnimationWidget.hexagonDots(
                color: DColor.primaryColor,
                size: 50,
              ),
      ),
    );
  }

  void _showSaveOptions() {
    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              ListTile(
                title: Text(context.l10n.saveToPhotoAlbum,
                    style: TextStyle(
                        color: Colors.black, fontWeight: FontWeight.w500),
                    textAlign: TextAlign.center),
                onTap: () {
                  Navigator.pop(context);
                  _saveQrCode();
                },
              ),
              ListTile(
                title: Text(context.l10n.globalCancel,
                    style: TextStyle(
                        color: Colors.grey, fontWeight: FontWeight.w500),
                    textAlign: TextAlign.center),
                onTap: () => Navigator.pop(context),
              ),
            ],
          ),
        );
      },
    );
  }

  Future<void> _saveQrCode() async {
    try {
      RenderRepaintBoundary boundary = _qrCodeKey.currentContext!
          .findRenderObject() as RenderRepaintBoundary;
      ui.Image image = await boundary.toImage(pixelRatio: 3.0);
      ByteData? byteData =
          await image.toByteData(format: ui.ImageByteFormat.png);

      if (byteData != null) {
        final tempDir = await getTemporaryDirectory();
        final file = File('${tempDir.path}/qrcode.png');
        await file.writeAsBytes(byteData.buffer.asUint8List());

        ImageUtils.saveToAlbum(file);
      }
    } catch (e) {
      print('Error saving QR code: $e');
    }
  }
}
