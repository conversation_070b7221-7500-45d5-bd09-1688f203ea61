import 'package:and/cache/cache_helper.dart';
import 'package:and/http/my_http.dart';
import 'package:and/io/group.dart';
import 'package:and/io/user.dart';
import 'package:and/model/group.dart';
import 'package:and/model/group_qr.dart';
import 'package:and/model/user_login_info.dart';
import 'package:and/model/user_qr.dart';
import 'package:and/module/common/controller/simple_controller.dart';
import 'package:get/get.dart';
import 'package:wukongimfluttersdk/entity/channel.dart';
import 'package:wukongimfluttersdk/type/const.dart';
import 'package:wukongimfluttersdk/wkim.dart';

class GroupQrLogic extends SimpleController<GroupQr> {
  final String key = 'group_qr';

  final String groupNo;
  late var channel = WKChannel(groupNo, WKChannelType.group).obs;

  GroupQrLogic(this.groupNo);
  
  GroupQrLogic.fromChannel(WKChannel existingChannel)
      : groupNo = existingChannel.channelID {
    channel.value = existingChannel;
  }

  @override
  void onReady() {
    super.onReady();
    _initListener();
    refreshData();
  }

  @override
  Future<GroupQr?> loadData() async {
    return GroupApi(MyHttp.dio).getGroupQr(groupNo);
  }

  @override
  void onClose() {
    _removeListener();
    super.onClose();
  }

  _initListener() {
    // 监听刷新channel资料事件
    WKIM.shared.channelManager.addOnRefreshListener(key, (channel) {
      print("刷新channel资料事件");
      if (channel.channelID == groupNo &&
          channel.channelType == WKChannelType.group) {
        this.channel.value = channel;
      }

      refreshData();
    });
  }

  _removeListener() {
    WKIM.shared.channelManager.removeOnRefreshListener(key);
  }
}
