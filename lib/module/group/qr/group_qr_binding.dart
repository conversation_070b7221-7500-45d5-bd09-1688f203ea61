import 'package:and/module/group/qr/group_qr_logic.dart';
import 'package:get/get.dart';
import 'package:wukongimfluttersdk/entity/channel.dart';

class GroupQrBinding extends Bindings {
  @override
  void dependencies() {
    // 支持两种参数传递方式：WKChannel 对象或 groupNo 字符串
    dynamic arguments = Get.arguments;
    if (arguments is WKChannel) {
      // 如果传递的是 WKChannel 对象，直接使用
      Get.lazyPut(() => GroupQrLogic.fromChannel(arguments));
    } else {
      // 向后兼容：如果传递的是 groupNo 字符串
      String groupNo = arguments;
      Get.lazyPut(() => GroupQrLogic(groupNo));
    }
  }
}
