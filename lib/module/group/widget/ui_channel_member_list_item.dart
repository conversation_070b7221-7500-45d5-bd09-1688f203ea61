import 'package:and/common/res/colours.dart';
import 'package:and/common/res/text_styles.dart';
import 'package:and/constant/wk_channel_member_role.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/model/extension/wx_channel_member_ext.dart';
import 'package:and/model/friend_apply.dart';
import 'package:and/module/group/widget/ui_channel_member_avatar_widget.dart';
import 'package:and/module/group/widget/ui_channel_member_name_widget.dart';
import 'package:and/module/user/user_info_page.dart';
import 'package:and/utils/common_helper.dart';
import 'package:and/widget/avatar_widget.dart';
import 'package:and/widget/submit_button.dart';
import 'package:flutter/material.dart';
import 'package:wukongimfluttersdk/entity/channel_member.dart';

class UiChannelMemberListItem extends StatelessWidget {
  final WKChannelMember item;
  final String? groupNo;
  final bool isSelected;
  final Function(bool) onChanged;

  const UiChannelMemberListItem(
      {super.key,
      required this.item,
      this.groupNo,
      required this.onChanged,
      this.isSelected = false});

  @override
  Widget build(BuildContext context) {
    return Padding(
        padding: EdgeInsets.symmetric(vertical: 5),
        child: CheckboxListTile(
            contentPadding: EdgeInsets.zero,
            value: isSelected,
            controlAffinity: ListTileControlAffinity.leading,
            onChanged: (value) {
              onChanged(value ?? false);
            },
            dense: true,
            title: Row(
              children: [
                UiChannelMemberAvatarWidget(item: item, groupNo: groupNo),
                SizedBox(width: 10),
                UiChannelMemberNameWidget(
                  item: item
                )
              ],
            )));
  }
}
