import 'package:and/common/res/text_styles.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/model/extension/wx_channel_member_ext.dart';
import 'package:flutter/material.dart';
import 'package:wukongimfluttersdk/entity/channel_member.dart';
import 'package:wukongimfluttersdk/type/const.dart';
import 'package:wukongimfluttersdk/wkim.dart';

class UiChannelMemberNameWidget extends StatefulWidget {
  final WKChannelMember item;
  final double? textSize;
  final bool isShowNickName;

  const UiChannelMemberNameWidget({
    super.key,
    required this.item,
    this.textSize,
    this.isShowNickName = false,
  });

  @override
  State<StatefulWidget> createState() {
    return _UiChannelMemberNameWidgetState();
  }
}

class _UiChannelMemberNameWidgetState extends State<UiChannelMemberNameWidget> {
  late Future<String> _displayNameFuture;

  @override
  void initState() {
    super.initState();
    _displayNameFuture = getDisplayName();
  }

  @override
  void didUpdateWidget(covariant UiChannelMemberNameWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    _displayNameFuture = getDisplayName();
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<String>(
      future: _displayNameFuture,
      builder: (BuildContext context, AsyncSnapshot<String> snapshot) {
        var displayName = snapshot.data ?? widget.item.displayName;
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              displayName,
              style: TextStyles.fontSize13Normal
                  .copyWith(fontSize: widget.textSize ?? 13),
              maxLines: 1,
            ),
            if (widget.isShowNickName && displayName != widget.item.displayName)
              Text(context.l10n.groupNickname(widget.item.displayName),
                  style: TextStyles.fontSize12Normal),
          ],
        );
      },
    );
  }

  Future<String> getDisplayName() async {
    var channelDisplay = widget.item.displayName;
    var channelFrom = await WKIM.shared.channelManager
        .getChannel(widget.item.memberUID, WKChannelType.personal);
    if (channelFrom?.channelRemark.isNotEmpty ?? false) {
      channelDisplay = channelFrom?.channelRemark ?? '';
      return channelDisplay;
    }
    return channelDisplay;
  }
}
