import 'package:and/common/res/colours.dart';
import 'package:and/common/res/text_styles.dart';
import 'package:and/constant/wk_channel_member_role.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/model/extension/wx_channel_member_ext.dart';
import 'package:and/model/friend_apply.dart';
import 'package:and/module/user/user_info_page.dart';
import 'package:and/utils/common_helper.dart';
import 'package:and/widget/avatar_widget.dart';
import 'package:and/widget/submit_button.dart';
import 'package:flutter/material.dart';
import 'package:wukongimfluttersdk/entity/channel_member.dart';

class UiChannelMemberAvatarWidget extends StatelessWidget {
  final WKChannelMember item;
  final String? groupNo;
  final double? size;

  const UiChannelMemberAvatarWidget({
    super.key,
    required this.item,
    this.groupNo,
    this.size,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        GestureDetector(
          behavior: HitTestBehavior.translucent,
          onTap: () {
            UserInfoPage.open(channelID: item.memberUID, groupNo: groupNo);
          },
          child: AvatarWidget(
            CommonHelper.getAvatarUrl(item.memberUID),
            size: size ?? 45,
            fontSize: 14,
          ),
        ),
        Positioned(
          bottom: 0,
          right: 0,
          child: _buildRole(context),
        ),
      ],
    );
  }

  Widget _buildRole(BuildContext context) {
    if (item.role == WKChannelMemberRole.admin) {
      return Container(
        padding: EdgeInsets.symmetric(horizontal: 2, vertical: 2),
        decoration: BoxDecoration(
          color: Colors.yellow,
          borderRadius: BorderRadius.circular(4),
        ),
        child: Text(
          context.l10n.groupOwner,
          style: TextStyles.fontSize13Normal.copyWith(fontSize: 10),
        ),
      );
    } else if (item.role == WKChannelMemberRole.manager) {
      return Container(
        padding: EdgeInsets.symmetric(horizontal: 2, vertical: 2),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(4),
        ),
        child: Text(
          context.l10n.groupManager,
          style: TextStyles.fontSize13Normal.copyWith(fontSize: 10),
        ),
      );
    }
    return Container();
  }
}
