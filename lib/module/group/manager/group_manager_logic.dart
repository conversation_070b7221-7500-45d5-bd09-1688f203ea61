import 'dart:convert';

import 'package:and/constant/wk_channel_member_role.dart';
import 'package:and/http/my_http.dart';
import 'package:and/io/group.dart';
import 'package:and/model/extension/wx_channel_member_ext.dart';
import 'package:and/model/request/group_member_request.dart';
import 'package:and/utils/pinyin_utils.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:wukongimfluttersdk/wkim.dart';
import 'package:wukongimfluttersdk/type/const.dart';
import 'package:wukongimfluttersdk/entity/channel_member.dart';

enum GroupManagerOperation {
  view,
  add,
  remove,
}

class GroupManagerArgument {
  final String groupId;
  final GroupManagerOperation operation;

  GroupManagerArgument(
      {required this.groupId, this.operation = GroupManagerOperation.view});

  factory GroupManagerArgument.fromGet() {
    return (Get.arguments as GroupManagerArgument);
  }

  String getTag() {
    return groupId;
  }
}

class GroupManagerLogic extends GetxController {
  final GroupManagerArgument argument;

  GroupManagerLogic({required this.argument});

  late final String groupId;
  final Rx<GroupManagerOperation> currentOperation =
      GroupManagerOperation.view.obs;
  final RxList<WKChannelMember> members =
      <WKChannelMember>[].obs; // 用于显示成员列表，根据操作类型填充
  final RxList<String> selectedMemberIds = <String>[].obs; // 用于添加/删除模式下的勾选
  final RxMap<String, List<WKChannelMember>> groupedMembers = <String, List<WKChannelMember>>{}.obs;
  final RxList<String> letters = <String>[].obs;
  final filterKey = "".obs;
  /// 所有成员
  List<WKChannelMember> allMembers = [];

  @override
  void onInit() {
    super.onInit();
    groupId = argument.groupId;
    currentOperation.value = argument.operation;
    loadMembersBasedOnOperation();
  }

  Future<void> loadMembersBasedOnOperation() async {
    members.clear();
    selectedMemberIds.clear();
    try {
      if (allMembers.isEmpty) {
        allMembers = await WKIM.shared.channelMemberManager.getMembers(groupId, WKChannelType.group) ?? [];
      }

      if (currentOperation.value == GroupManagerOperation.view ||
          currentOperation.value == GroupManagerOperation.remove) {
        // 查看或删除模式：加载管理员列表 (不包括群主)
        members.value = allMembers
            .where((m) => m.role == WKChannelMemberRole.manager)
            .toList();
      } else if (currentOperation.value == GroupManagerOperation.add) {
        // 添加模式：加载所有群成员（除群主外）
        members.value = allMembers
            .where((m) => m.role != WKChannelMemberRole.admin)
            .toList();
      }
    } catch (e) {
      print('加载成员列表失败: $e');
      members.value = [];
    }
    _group();
  }

  void toggleSelection(String userId) {
    if (selectedMemberIds.contains(userId)) {
      selectedMemberIds.remove(userId);
    } else {
      selectedMemberIds.add(userId);
    }
  }

  void changeOperation(GroupManagerOperation newOperation) {
    currentOperation.value = newOperation;
    loadMembersBasedOnOperation();
  }

  Future<void> addSelectedManagers() async {
    if (selectedMemberIds.isEmpty) return;
    try {
      EasyLoading.show();
      String body = jsonEncode(selectedMemberIds);
      await GroupApi(MyHttp.dio).addGroupManagers(groupId, body);
      
      // 更新本地数据
      for (var memberId in selectedMemberIds) {
        var member = allMembers.firstWhere((m) => m.memberUID == memberId);
        member.role = WKChannelMemberRole.manager;
      }
      
      selectedMemberIds.clear();
      await Future.delayed(const Duration(milliseconds: 500), () {
        changeOperation(GroupManagerOperation.view);
      });

      EasyLoading.dismiss();
    } catch (e) {
      EasyLoading.dismiss();
      print('添加管理员失败: $e');
    }
  }

  Future<void> removeSelectedManagers() async {
    if (selectedMemberIds.isEmpty) return;
    try {
      EasyLoading.show();
      String body = jsonEncode(selectedMemberIds);
      await GroupApi(MyHttp.dio).deleteGroupManagers(groupId, body);
      
      // 更新本地数据
      for (var memberId in selectedMemberIds) {
        var member = allMembers.firstWhere((m) => m.memberUID == memberId);
        member.role = WKChannelMemberRole.normal;
      }
      
      selectedMemberIds.clear();
      await Future.delayed(const Duration(milliseconds: 500), () {
        changeOperation(GroupManagerOperation.view);
      });
      EasyLoading.dismiss();
    } catch (e) {
      EasyLoading.dismiss();
      print('移除管理员失败: $e');
    }
  }

  void _group() {
    var filterItems = <WKChannelMember>[];
    filterItems.addAll(members.where((e) {
      if (filterKey.isEmpty) return true;
      
      // 转换为小写进行比较
      String searchKey = filterKey.value.toLowerCase();
      String memberName = e.memberName.toLowerCase();
      String displayName = e.displayName.toLowerCase();
      String pinyin = PinyinUtils.getPinyin(e.displayName).toLowerCase();
      String firstLetter = PinyinUtils.getFirstLetter(e.displayName).toLowerCase();
      
      // 匹配成员名、显示名、拼音和首字母
      return memberName.contains(searchKey) ||
             displayName.contains(searchKey) ||
             pinyin.contains(searchKey) ||
             firstLetter.contains(searchKey);
    }));

    // 按首字母排序
    filterItems.sort((a, b) {
      var aLetter = PinyinUtils.getFirstLetter(a.displayName).toUpperCase();
      var bLetter = PinyinUtils.getFirstLetter(b.displayName).toUpperCase();
      return aLetter.compareTo(bLetter);
    });

    // 分组
    groupedMembers.clear();
    for (var item in filterItems) {
      var firstLetter = PinyinUtils.getFirstLetter(item.displayName).toUpperCase();
      groupedMembers.putIfAbsent(firstLetter, () => []).add(item);
    }

    // 更新字母列表
    letters.value = groupedMembers.keys.toList()..sort();
  }

  void search(String key) {
    filterKey.value = key.trim(); // 去除首尾空格
    _group();
  }
}
