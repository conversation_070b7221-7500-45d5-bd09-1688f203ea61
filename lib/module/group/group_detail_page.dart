import 'dart:async';
import 'dart:math';

import 'package:and/app.dart';
import 'package:and/cache/cache_helper.dart';
import 'package:and/common/res/colours.dart';
import 'package:and/common/res/text_styles.dart';
import 'package:and/constant/group_info_keys.dart';
import 'package:and/constant/group_setting_keys.dart';
import 'package:and/constant/wk_channel_member_role.dart';
import 'package:and/eventbus/contact_sync_event.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/model/extension/wk_channel_ext.dart';
import 'package:and/model/extension/wx_channel_member_ext.dart';
import 'package:and/module/group/manager/group_manager_logic.dart'; // 新增导入
import 'package:and/module/group/members/group_members_page.dart';
import 'package:and/module/group/remark/change_group_remark_page.dart';
import 'package:and/router/router.dart';
import 'package:and/utils/common_helper.dart';
import 'package:and/utils/image_path.dart';
import 'package:and/widget/avatar_widget.dart'; // 确保 AvatarWidget 已导入
import 'package:and/widget/refresh/refresh_widget.dart';
import 'package:and/widget/simple_setting_item_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:get/get.dart';
import 'package:wukongimfluttersdk/type/const.dart';
import 'package:wukongimfluttersdk/wkim.dart';

import 'group_detail_logic.dart';
import 'widget/ui_channel_member_grid_item.dart';

class GroupDetailArgument {
  String groupNo;

  GroupDetailArgument({required this.groupNo});

  factory GroupDetailArgument.fromGet() {
    return (Get.arguments as GroupDetailArgument);
  }

  String getTag() {
    return groupNo;
  }

  static getTagFromGet() {
    return GroupDetailArgument.fromGet().getTag();
  }
}

class GroupDetailPage extends StatefulWidget {
  const GroupDetailPage({super.key});

  @override
  State<StatefulWidget> createState() {
    return _GroupDetailPageState();
  }

  static void open({required String groupNo}) {
    Get.toNamed(RouteGet.groupDetail,
        preventDuplicates: false,
        arguments: GroupDetailArgument(
          groupNo: groupNo,
        ));
  }
}

class _GroupDetailPageState extends State<GroupDetailPage> {
  late final GroupDetailArgument argument = GroupDetailArgument.fromGet();
  late GroupDetailLogic logic =
      Get.find<GroupDetailLogic>(tag: argument.getTag());
  late final list = logic.list;
  late final channel = logic.channel;
  late final currentMember = logic.currentMember;

  late StreamSubscription _contactStatusEventSubscription;

  @override
  void initState() {
    super.initState();

    _contactStatusEventSubscription =
        eventBus.on<ContactSyncEvent>().listen((event) {
      if (mounted) {
        logic.refreshData();
      }
    });
  }

  @override
  void dispose() {
    _contactStatusEventSubscription.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(title: Obx(() => _buildTitle())),
        body: SafeArea(child: Obx(() => _buildList())));
  }

  Widget _buildTitle() {
    var title = context.l10n.chatInfo;
    if (list.isNotEmpty) {
      title += "(${list.length})";
    }

    return Text(title);
  }

  Widget _buildList() {
    if (list.isEmpty) {
      return buildLoadStateWidget(logic.loadStatus.value, () {
        logic.refreshData();
      });
    }
    var extraCount = 1;
    if (currentMember.value?.role != WKChannelMemberRole.normal) {
      extraCount += 1;
    }
    var crossAxisCount = 5;
    var maxColumnCount = 4;
    var count = min(maxColumnCount * crossAxisCount, list.length + extraCount);
    var isShowAll = list.length > count - extraCount;

    return CustomScrollView(
      slivers: [
        SliverVisibility(
            visible: (currentMember.value?.role == WKChannelMemberRole.admin ||
                    currentMember.value?.role == WKChannelMemberRole.manager) ||
                channel.value.anonymous == 0,
            sliver: SliverPadding(
                padding: const EdgeInsets.symmetric(horizontal: 15),
                sliver: SliverAlignedGrid(
                    gridDelegate:
                        SliverSimpleGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: crossAxisCount,
                    ),
                    itemBuilder: (BuildContext context, int index) {
                      var isAddCount = index == list.length;
                      if (isAddCount) {
                        return _buildAddCount();
                      }
                      var isDeleteCount = index == list.length + 1;
                      if (isDeleteCount) {
                        return _buildDeleteMembers();
                      }

                      var item = list[index];
                      return UiChannelMemberGridItem(
                          item: item, groupNo: argument.groupNo);
                    },
                    itemCount: count))),
        SliverVisibility(
            visible: isShowAll,
            sliver: SliverToBoxAdapter(
              child: InkWell(
                onTap: () {
                  Get.to(GroupMembersPage(argument: argument));
                },
                child: SizedBox(
                    height: 40,
                    width: double.infinity,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(context.l10n.showAllMembers,
                            style: TextStyles.fontSize16Normal),
                        Padding(
                          padding: EdgeInsets.only(left: 10),
                          child: Icon(
                            Icons.arrow_forward_ios,
                            size: 16,
                            color: DColor.secondaryTextColor,
                          ),
                        )
                      ],
                    )),
              ),
            )),
        SliverToBoxAdapter(
          child: _buildGroupInfo(),
        ),
        SliverToBoxAdapter(
          child: Container(height: 6),
        ),
        SliverToBoxAdapter(
          child: _buildSearchHistory(),
        ),
        SliverToBoxAdapter(
          child: Container(height: 6),
        ),
        SliverToBoxAdapter(
          child: _buildClearHistory(),
        ),
        SliverToBoxAdapter(
          child: Container(height: 6),
        ),
        SliverToBoxAdapter(
          child: _buildExitGroup(),
        ),
      ],
    );
  }

  Widget _buildSearchHistory() {
    return Container(
        color: Colors.white,
        child: SimpleSettingItemWidget(context.l10n.searchChatMsg, onTap: () {
          logic.searchHistory(context);
        }));
  }

  Widget _buildClearHistory() {
    return Container(
        color: Colors.white,
        child: SimpleSettingItemWidget(context.l10n.clearChatMsg, onTap: () {
          logic.clearHistory(context);
        }));
  }

  Widget _buildExitGroup() {
    return GestureDetector(
      onTap: () {
        logic.exitGroup(context);
      },
      child: Container(
          color: Colors.white,
          padding: EdgeInsets.symmetric(vertical: 12),
          child: Center(
              child: Text(
            context.l10n.deleteGroup,
            style: TextStyles.fontSize16Normal.copyWith(color: Colors.red),
          ))),
    );
  }

  Widget _buildGroupInfo() {
    var title = channel.value.channelName.replaceAll("\n", " ");
    return Container(
      color: Colors.white,
      child: Column(
        children: [
          SimpleSettingItemWidget(context.l10n.groupName, value: title,
              onTap: () {
            logic.changeName(context);
          }),
          _buildGroupManager(),
          // 在 SimpleSettingItemWidget 的 onTap 回调中
          SimpleSettingItemWidget(context.l10n.groupQr,
          rightWidget: Image.asset(
          ImagePath.ic_qrcode,
          height: 30,
          color: DColor.secondaryTextColor,
          ), onTap: () {
          // 传递完整的 channel 对象，而不仅仅是 groupNo
          Get.toNamed(RouteGet.groupQr, arguments: channel.value);
          }),
          SimpleSettingItemWidget(
            context.l10n.groupAnnouncement,
            value: (channel.value.notice?.isEmpty ?? false)
                ? context.l10n.unsetting
                : "",
            bottomWidget: Visibility(
                visible: channel.value.notice?.isNotEmpty ?? false,
                child: Text(channel.value.notice ?? '',
                    style: TextStyles.fontSize15Normal
                        .copyWith(color: DColor.secondaryTextColor))),
            onTap: () {
              logic.changeNotice(context);
            },
          ),
          SimpleSettingItemWidget(context.l10n.remark,
              value: channel.value.channelRemark ?? '', onTap: () {
            Get.to(ChangeGroupRemarkPage(tag: argument.getTag()));
          }),
          Visibility(
              visible: currentMember.value?.role == WKChannelMemberRole.admin ||
                  currentMember.value?.role == WKChannelMemberRole.manager,
              child: SimpleSettingItemWidget(context.l10n.anonymous,
                  rightWidget: Switch(
                      value: channel.value.anonymous == 1,
                      onChanged: (value) async {
                        logic.changeAnonymous(value);
                      }))),
          Visibility(
              visible: currentMember.value?.role == WKChannelMemberRole.admin ||
                  currentMember.value?.role == WKChannelMemberRole.manager,
              child: SimpleSettingItemWidget(context.l10n.forbidden,
                  rightWidget: Switch(
                      value: channel.value.forbidden == 1,
                      onChanged: (value) async {
                        var result = await logic.updateGroupForbidden(
                            argument.groupNo, value ? 1 : 0);
                        if (result) {
                          channel.value.forbidden = value ? 1 : 0;
                          WKIM.shared.channelManager
                              .addOrUpdateChannel(channel.value);
                        }
                      }))),
          SimpleSettingItemWidget(context.l10n.msgRemind,
              rightWidget: Switch(
                  value: channel.value.mute == 1,
                  onChanged: (value) async {
                    var result = await logic.updateGroupSetting(
                        GroupSettingKeys.mute, value ? 1 : 0);
                    if (result) {
                      channel.value.mute = value ? 1 : 0;
                      WKIM.shared.channelManager
                          .addOrUpdateChannel(channel.value);
                    }
                  })),
          SimpleSettingItemWidget(context.l10n.msgTop,
              rightWidget: Switch(
                  value: channel.value.top == 1,
                  onChanged: (value) async {
                    var result = await logic.updateGroupSetting(
                        GroupSettingKeys.top, value ? 1 : 0);
                    if (result) {
                      channel.value.top = value ? 1 : 0;
                      WKIM.shared.channelManager
                          .addOrUpdateChannel(channel.value);
                    }
                  })),
          SimpleSettingItemWidget(context.l10n.saveToMaillist,
              rightWidget: Switch(
                  value: channel.value.save == 1,
                  onChanged: (value) async {
                    var result = await logic.updateGroupSetting(
                        GroupSettingKeys.save, value ? 1 : 0);
                    if (result) {
                      channel.value.save = value ? 1 : 0;
                      WKIM.shared.channelManager
                          .addOrUpdateChannel(channel.value);
                    }
                  })),
          Visibility(
              visible: channel.value.anonymous == 0,
              child: SimpleSettingItemWidget(context.l10n.myRemarkNameInGroup,
                  value: currentMember.value?.displayName, onTap: () {
                logic.changeNameInGroup(context);
              })),
          // SimpleSettingItemWidget(context.l10n.showRemarkName,
          //     rightWidget: Switch(
          //         value: channel.value.showNick == 1,
          //         onChanged: (value) async {
          //           var result = await logic.updateGroupSetting(
          //               GroupSettingKeys.showNick, value ? 1 : 0);
          //           if (result) {
          //             channel.value.showNick = value ? 1 : 0;
          //             WKIM.shared.channelManager
          //                 .addOrUpdateChannel(channel.value);
          //           }
          //         })),
          SimpleSettingItemWidget(context.l10n.report, onTap: () {
            Map<String, String> params = {
              "channel_id": argument.groupNo,
              "channel_type": WKChannelType.group.toString(),
              "uid": CacheHelper.uid ?? '',
              "token": CacheHelper.token ?? '',
            };
            CommonHelper.report(params);
          }),
        ],
      ),
    );
  }

  Widget _buildGroupManager() {
    return Visibility(
      visible: currentMember.value?.role == WKChannelMemberRole.admin,
      child: Obx(() {
        final managers = logic.groupManagers;
        final bool hasManagers = managers.isNotEmpty;
        return SimpleSettingItemWidget(
          context.l10n.groupAdmin,
          value: !hasManagers ? context.l10n.addGroupAdmin : '',
          rightWidget: hasManagers
              ? Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    SizedBox(
                      height: 30,
                      width: MediaQuery.of(context).size.width * 0.4,
                      child: ListView.builder(
                        scrollDirection: Axis.horizontal,
                        reverse: true,
                        itemCount: managers.length,
                        itemBuilder: (context, index) {
                          final manager = managers[index];
                          return Padding(
                            padding:
                                const EdgeInsets.symmetric(horizontal: 2.0),
                            child: AvatarWidget(
                              CommonHelper.getAvatarUrl(manager.memberUID),
                              size: 30,
                            ),
                          );
                        },
                      ),
                    ),
                    Row(
                      children: [
                        GestureDetector(
                          onTap: () async {
                            await Get.toNamed(
                              RouteGet.groupManager,
                              arguments: GroupManagerArgument(
                                groupId: argument.groupNo,
                                operation: GroupManagerOperation.add,
                              ),
                            );
                            if (mounted) {
                              setState(() {
                                logic.refreshManagerInfo();
                              });
                            }
                          },
                          child: Padding(
                            padding:
                                const EdgeInsets.symmetric(horizontal: 4.0),
                            child: Image.asset(ImagePath.ic_chat_add,
                                width: 30, height: 30),
                          ),
                        ),
                        GestureDetector(
                          onTap: () async {
                            await Get.toNamed(
                              RouteGet.groupManager,
                              arguments: GroupManagerArgument(
                                groupId: argument.groupNo,
                                operation: GroupManagerOperation.remove,
                              ),
                            );
                            if (mounted) {
                              setState(() {
                                logic.refreshManagerInfo();
                              });
                            }
                          },
                          child: Padding(
                            padding:
                                const EdgeInsets.symmetric(horizontal: 4.0),
                            child: Image.asset(ImagePath.ic_chat_delete,
                                width: 30, height: 30),
                          ),
                        ),
                      ],
                    ),
                  ],
                )
              : null,
          onTap: () async {
            await Get.toNamed(
              RouteGet.groupManager,
              arguments: GroupManagerArgument(
                groupId: argument.groupNo,
                operation: hasManagers
                    ? GroupManagerOperation.view
                    : GroupManagerOperation.add,
              ),
            );
            if (mounted) {
              setState(() {
                logic.refreshManagerInfo();
              });
            }
          },
        );
      }),
    );
  }

  Widget _buildAddCount() {
    return GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: () {
          logic.addGroupMembers();
        },
        child: Padding(
          padding: EdgeInsets.symmetric(vertical: 4),
          child: Column(
            children: [
              Image.asset(ImagePath.ic_chat_add, height: 45),
              SizedBox(height: 10),
              Text(
                "",
                style: TextStyles.fontSize13Normal,
                maxLines: 1,
              ),
            ],
          ),
        ));
  }

  Widget _buildDeleteMembers() {
    return GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: () {
          logic.deleteGroupMembers();
        },
        child: Padding(
          padding: EdgeInsets.symmetric(vertical: 4),
          child: Column(
            children: [
              Image.asset(ImagePath.ic_chat_delete, height: 45),
              SizedBox(height: 10),
              Text(
                "",
                style: TextStyles.fontSize13Normal,
                maxLines: 1,
              ),
            ],
          ),
        ));
  }
}
