import 'package:and/common/res/colours.dart';
import 'package:and/common/res/text_styles.dart';
import 'package:and/constant/group_info_keys.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/model/extension/wk_channel_ext.dart';
import 'package:and/module/group/group_detail_logic.dart';
import 'package:and/widget/input_form_widget.dart';
import 'package:and/widget/submit_button.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:wukongimfluttersdk/wkim.dart';

class ChangeGroupNoticePage extends StatefulWidget {
  final String tag;

  const ChangeGroupNoticePage({super.key, required this.tag});

  @override
  State<StatefulWidget> createState() {
    return _ChangeGroupNoticePageState();
  }
}

class _ChangeGroupNoticePageState extends State<ChangeGroupNoticePage> {
  late GroupDetailLogic logic = Get.find<GroupDetailLogic>(tag: widget.tag);
  late final channel = logic.channel;

  final _noticeTextController = TextEditingController();

  final GlobalKey<FormState> _noticeKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    _noticeTextController.text = channel.value.notice??'';
  }

  @override
  void dispose() {
    _noticeTextController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: Text(context.l10n.groupAnnouncement),
          actions: [
            Padding(
              padding: EdgeInsets.only(right: 15),
              child: _buildSave(),
            )
          ],
        ),
        body: Padding(
          padding: EdgeInsets.symmetric(horizontal: 15),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [_buildNoticeInput()],
          ),
        ));
  }

  Widget _buildSave() {
    return SubmitButton(
        enable: channel.value.notice != _noticeTextController.text &&
            _noticeTextController.text.isNotEmpty,
        padding: EdgeInsets.symmetric(horizontal: 10, vertical: 6),
        onPressed: () async {
          var result = await logic.updateGroupInfo(
              GroupInfoKeys.notice, _noticeTextController.text);

          if (result) {
            channel.value.channelName = _noticeTextController.text;
            WKIM.shared.channelManager.addOrUpdateChannel(channel.value);
            Get.back();
          }
        },
        text: context.l10n.globalSave);
  }

  Widget _buildNoticeInput() {
    return TextFormField(
      key: _noticeKey,
      controller: _noticeTextController,
      autofocus: true,
      onChanged: (value) {
        setState(() {

        });
      },
      onFieldSubmitted: (value) {},
      decoration: InputDecoration(
        border: InputBorder.none,
        hintText: context.l10n.groupAnnouncement,
        hintStyle: TextStyles.fontSize15Normal
            .copyWith(color: DColor.hintText.withOpacity(0.2)),
      ),
    );
  }
}
