import 'dart:async';

import 'package:and/app.dart';
import 'package:and/common/res/colours.dart';
import 'package:and/common/res/text_styles.dart';
import 'package:and/eventbus/contact_sync_event.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/module/group/group_detail_page.dart';
import 'package:and/module/group/widget/ui_channel_member_grid_item.dart';
import 'package:and/module/group/widget/ui_channel_member_list_item.dart';
import 'package:and/module/search/widget/search_input_widget.dart';
import 'package:and/module/user/user_info_page.dart';
import 'package:and/router/router.dart';
import 'package:and/widget/letter_index_bar.dart';
import 'package:and/widget/refresh/load_state.dart';
import 'package:and/widget/refresh/refresh_page.dart';
import 'package:and/widget/refresh/refresh_widget.dart';
import 'package:and/widget/submit_button.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:super_sliver_list/super_sliver_list.dart';
import 'package:wukongimfluttersdk/entity/channel_member.dart';

import 'delete_group_member_logic.dart';

class DeleteGroupMembersPage extends StatefulWidget {
  const DeleteGroupMembersPage({super.key});

  @override
  State<StatefulWidget> createState() {
    return _DeleteGroupMemberPageState();
  }

  static void open({required String groupNo}) {
    Get.toNamed(RouteGet.groupMembersDelete,
        preventDuplicates: false,
        arguments: GroupDetailArgument(
          groupNo: groupNo,
        ));
  }
}

class _DeleteGroupMemberPageState extends RefreshState<DeleteGroupMembersPage> {
  late final GroupDetailArgument argument = GroupDetailArgument.fromGet();
  late final logic = Get.find<DeleteGroupMembersLogic>();
  late final list = logic.list;
  late final selectedMemberIds = logic.selectedMemberIds;
  final ScrollController scrollController = ScrollController();
  final ListController listController = ListController();

  late StreamSubscription _contactStatusEventSubscription;

  @override
  void initState() {
    super.initState();

    _contactStatusEventSubscription =
        eventBus.on<ContactSyncEvent>().listen((event) {
      if (mounted) {
        logic.refreshData();
      }
    });
  }

  @override
  void dispose() {
    _contactStatusEventSubscription.cancel();
    super.dispose();

    Get.delete<DeleteGroupMembersLogic>();
  }

  @override
  Rx<FitLoadStatus> getLoadState() {
    return logic.loadStatus;
  }

  @override
  void didUpdateWidget(covariant DeleteGroupMembersPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Scaffold(
        appBar: AppBar(
          title: Text(context.l10n.deleteGroupMembers),
          actions: [
            Padding(
              padding: EdgeInsets.only(right: 15),
              child: Obx(() => _buildDelete()),
            )
          ],
        ),
        body: Column(
          children: [
            Container(
                margin: EdgeInsets.symmetric(horizontal: 15),
                child: _buildSearchInput()),
            SizedBox(height: 10),
            Obx(() => Expanded(child: _buildRefreshList()))
          ],
        ));
  }

  Widget _buildSearchInput() {
    return SearchInputWidget(
      searchPlaceHolder: context.l10n.search,
      showImage: false,
      autofocus: false,
      onKeyChanged: (key) {
        logic.search(key);
      },
      onFieldSubmitted: (key) {
        logic.search(key);
      },
    );
  }

  Widget _buildDelete() {
    var title = context.l10n.globalDelete;
    if (selectedMemberIds.isNotEmpty) {
      title += "(${selectedMemberIds.length})";
    }

    return SubmitButton(
        enable: selectedMemberIds.isNotEmpty,
        padding: EdgeInsets.symmetric(horizontal: 10, vertical: 6),
        onPressed: () {
          logic.deleteMembers();
        },
        text: title);
  }

  Widget _buildRefreshList() {
    return Stack(children: [
      buildRefreshWidget(
        refreshController: refreshController,
        loadStatus: logic.loadStatus.value,
        hasData: logic.letters.isNotEmpty,
        builder: (physics) => _buildList(physics),
        onRefresh: () {
          logic.refreshData();
        },
      ),
      Positioned(
        right: 5,
        top: 0,
        bottom: 0,
        child: Center(
          child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 8),
              margin: const EdgeInsets.symmetric(horizontal: 8),
              child: AlphabetScrollbar(
                selectedLetterSize: 30,
                factor: 0,
                selectedLetterAdditionalSpace: 30,
                onLetterChange: (value) {
                  int position = logic.letters.indexWhere((e) => e == value);
                  if (position >= 0) {
                    listController.jumpToItem(
                      index: position,
                      scrollController: scrollController,
                      alignment: 0,
                    );
                  }
                },
              )),
        ),
      ),
    ]);
  }

  Widget _buildList(ScrollPhysics? physics) {
    return CustomScrollView(
      controller: scrollController,
      physics: physics,
      slivers: [
        SliverPadding(
          padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 15),
          sliver: SuperSliverList(
            listController: listController,
            delegate: SliverChildBuilderDelegate((context, index) {
              String letter = logic.letters[index];
              List<WKChannelMember> members =
                  logic.groupedMembers[letter] ?? [];
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 20, vertical: 4),
                    child: Text(
                      letter,
                      style: TextStyles.fontSize14Bold
                          .copyWith(color: DColor.primaryColor),
                    ),
                  ),
                  ...members.map((member) {
                    return Obx(() => UiChannelMemberListItem(
                        item: member,
                        groupNo: argument.groupNo,
                        isSelected:
                            selectedMemberIds.contains(member.memberUID),
                        onChanged: (value) {
                          logic.select(member, isSelected: value);
                        }));
                  }),
                ],
              );
            }, childCount: logic.letters.length),
          ),
        )
      ],
    );
  }

  void search(String keyword) {
    logic.onSearch(keyword);
  }
}
