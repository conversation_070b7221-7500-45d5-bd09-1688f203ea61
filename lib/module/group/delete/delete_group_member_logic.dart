import 'package:and/cache/cache_helper.dart';
import 'package:and/common/utils/easy_loading_helper.dart';
import 'package:and/constant/wk_channel_member_role.dart';
import 'package:and/model/extension/wk_channel_ext.dart';
import 'package:and/model/extension/wx_channel_member_ext.dart';
import 'package:and/module/common/controller/list_controller.dart';
import 'package:and/module/group/group_detail_page.dart';
import 'package:and/utils/http_utils.dart';
import 'package:and/utils/pinyin_utils.dart';
import 'package:get/get.dart';
import 'package:wukongimfluttersdk/entity/channel.dart';
import 'package:wukongimfluttersdk/entity/channel_member.dart';
import 'package:wukongimfluttersdk/type/const.dart';
import 'package:wukongimfluttersdk/wkim.dart';

class DeleteGroupMembersLogic extends ListController<WKChannelMember> {
  final String key = "ui_group_members";

  final GroupDetailArgument argument;
  final RxMap<String, List<WKChannelMember>> groupedMembers =
      <String, List<WKChannelMember>>{}.obs;
  final RxList<String> letters = <String>[].obs;
  final filterKey = "".obs;

  final selectedMemberIds = <String>[].obs;

  DeleteGroupMembersLogic({required this.argument}) {
    ever(list, (value) {
      _group();
    });
    ever(filterKey, (value) {
      _group();
    });
  }

  @override
  void onReady() {
    super.onReady();
    _initListener();
  }

  @override
  void onClose() {
    _removeListener();
    super.onClose();
  }

  @override
  Future<List<WKChannelMember>?> loadData() async {
    var members = await WKIM.shared.channelMemberManager
            .getMembers(argument.groupNo, WKChannelType.group) ??
        [];

    var loginMemberRole = WKChannelMemberRole.normal;
    var loginMember = await WKIM.shared.channelMemberManager.getMember(
        argument.groupNo, WKChannelType.group, CacheHelper.uid ?? '');
    if (loginMember != null) {
      loginMemberRole = loginMember.role;
    }

    return members.where((e) {
      if (e.role == WKChannelMemberRole.manager &&
          loginMemberRole == WKChannelMemberRole.admin) {
        return true;
      } else {
        return e.role == WKChannelMemberRole.normal;
      }
    }).toList();
  }

  void _group() {
    var filterItems = <WKChannelMember>[];
    filterItems.addAll(list.where((e) {
      if (filterKey.isNotEmpty) {
        return e.memberName.contains(filterKey.value);
      }
      return true;
    }));
    // 按首字母排序
    filterItems.sort((a, b) {
      var aLetter = PinyinUtils.getFirstLetter(a.displayName);
      var bLetter = PinyinUtils.getFirstLetter(b.displayName);
      return aLetter.compareTo(bLetter);
    });
    // 分组
    groupedMembers.clear();
    for (var item in filterItems) {
      var firstLetter = PinyinUtils.getFirstLetter(item.displayName);
      groupedMembers.putIfAbsent(firstLetter, () => []).add(item);
    }
    // 更新字母列表
    letters.value = groupedMembers.keys.toList()..sort();
  }

  void onSearch(String key) {
    filterKey.value = key;
  }

  bool select(WKChannelMember member, {bool isSelected = false}) {
    if (isSelected) {
      selectedMemberIds.add(member.memberUID);
      return true;
    } else {
      selectedMemberIds.remove(member.memberUID);
      return false;
    }
  }

  _initListener() {
    // 监听刷新member事件
    WKIM.shared.channelMemberManager.addOnRefreshMemberListener(key,
        (member, isEnd) {
      print("刷新member事件");

      if (member.channelID == argument.groupNo) {
        refreshData();
      }
    });

    // 监听新增member事件
    WKIM.shared.channelMemberManager.addOnNewMemberListener(key, (members) {
      print("新增member事件");

      var isAdd = members.any((e) => e.channelID == argument.groupNo);
      if (isAdd) {
        refreshData();
      }
    });

    // 监听删除member事件
    WKIM.shared.channelMemberManager.addOnDeleteMemberListener(key, (members) {
      print("删除member事件");

      var isDelete = members.any((e) => e.channelID == argument.groupNo);
      if (isDelete) {
        refreshData();
      }
    });
  }

  _removeListener() {
    WKIM.shared.channelMemberManager.removeRefreshMemberListener(key);
    WKIM.shared.channelMemberManager.removeNewMemberListener(key);
    WKIM.shared.channelMemberManager.removeDeleteMemberListener(key);
  }

  void deleteMembers() {
    EasyLoadingHelper.show(onAction: () async {
      var result = await HttpUtils.deleteGroupMembers(
          argument.groupNo, selectedMemberIds);
      if (result) {
        Get.back();
      }
    });
  }

  void search(String key) {
    filterKey.value = key;
    refreshData();
  }
}
