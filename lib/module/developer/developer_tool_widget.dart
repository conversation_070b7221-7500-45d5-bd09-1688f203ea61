import 'dart:async';

import 'package:and/common/res/text_styles.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'developer_tools_page.dart';

class DeveloperToolWidget extends StatefulWidget {
  const DeveloperToolWidget({super.key});

  @override
  State createState() => _DeveloperToolWidget();
}

class _DeveloperToolWidget extends State<DeveloperToolWidget> {
  static int minTapCount = 5;
  int _tapCounter = 0;
  Timer? _tapTimer;

  @override
  void dispose() {
    _tapTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    var text = "Developer Tools";
    if (!kDebugMode) {
      text = "";
    }
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: _handleVersionTap,
      child: Container(
        padding: const EdgeInsets.all(10),
        constraints: const BoxConstraints(minWidth: 100, minHeight: 40),
        child: Text(text,
            style: TextStyles.fontSize14Normal),
      ),
    );
  }

  void _handleVersionTap() {
    _tapCounter++;

    if (_tapCounter == 1) {
      _tapTimer = Timer(const Duration(seconds: 1), () {
        _tapCounter = 0; // Reset the counter if no further taps
      });
    }

    if (_tapCounter >= minTapCount) {
      // Change this value to the number of taps you want
      _tapCounter = 0;
      _tapTimer?.cancel();
      _triggerAction();
    }
  }

  void _triggerAction() {
    Get.to(const DeveloperToolsPage());
  }
}
