import 'package:and/cache/cache_helper.dart';
import 'package:and/common/res/text_styles.dart';
import 'package:and/utils/common_helper.dart';
import 'package:and/widget/app_bar_widget.dart';
import 'package:flutter/material.dart';

class DeveloperToolsPage extends StatefulWidget {
  const DeveloperToolsPage({super.key});

  @override
  State<StatefulWidget> createState() {
    return _DeveloperToolsPageState();
  }
}

class _DeveloperToolsPageState extends State<DeveloperToolsPage> {
  var initDevTestMode = CacheHelper.devTestMode;
  var changedTestMode = CacheHelper.devTestMode;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBarWidget(context, "Developer Tools"),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(10),
        child: Column(
          children: <Widget>[
            SwitchListTile(
              contentPadding: const EdgeInsets.only(left: 15, right: 15),
              onChanged: (value) {
                setState(() {
                  changedTestMode = value;
                });
              },
              value: changedTestMode,
              title: Text(
                "Test Env",
                style: TextStyles.fontSize16Normal,
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    super.dispose();

    if (initDevTestMode != changedTestMode) {
      _switchDev();
    }
  }

  ///
  ///由于切换环境，导致请求 logout 的时候，用旧的 token 去请求，导致token失效，也无法正常退出
  ///因此要先登出，后切换环境
  Future<void> _switchDev() async {
    await CommonHelper.exitLogin();
    CacheHelper.saveDevTestMode(changedTestMode);
  }
}
