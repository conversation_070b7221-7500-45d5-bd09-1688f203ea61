import 'dart:io';

import 'package:and/cache/cache_helper.dart';
import 'package:and/common/extension/common_ext.dart';
import 'package:and/common/utils/easy_loading_helper.dart';
import 'package:and/constant/file_dir_keys.dart';
import 'package:and/constant/wk_channel_member_role.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/model/content/wk_file_content.dart';
import 'package:and/model/content/wk_sticker_content.dart';
import 'package:and/model/extension/wk_channel_ext.dart';
import 'package:and/model/extension/wk_msg_ext.dart';
import 'package:and/model/sticker_info.dart';
import 'package:and/module/contact/choose/choose_contact_page.dart';
import 'package:and/utils/audio_utils.dart';
import 'package:and/utils/dialog_utils.dart';
import 'package:and/utils/download/download_manager.dart';
import 'package:and/utils/fileUtils.dart';
import 'package:and/utils/image_utils.dart';
import 'package:and/utils/video_utils.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:mime/mime.dart';
import 'package:wukongimfluttersdk/entity/channel.dart';
import 'package:wukongimfluttersdk/entity/msg.dart';
import 'package:wukongimfluttersdk/model/wk_card_content.dart';
import 'package:wukongimfluttersdk/model/wk_image_content.dart';
import 'package:wukongimfluttersdk/model/wk_message_content.dart';
import 'package:wukongimfluttersdk/model/wk_text_content.dart';
import 'package:wukongimfluttersdk/model/wk_video_content.dart';
import 'package:wukongimfluttersdk/model/wk_voice_content.dart';
import 'package:wukongimfluttersdk/proto/proto.dart';
import 'package:wukongimfluttersdk/type/const.dart';
import 'package:wukongimfluttersdk/wkim.dart';

import '../../app.dart';

class ChatSendUtils {
  WKMsg? replyWKMsg;
  final String channelID;
  final int channelType;
  Function()? onSendMsgStart;
  Function()? onSendMsgEnd;

  ChatSendUtils(
      {required this.channelID,
      required this.channelType,
      this.replyWKMsg,
      this.onSendMsgStart,
      this.onSendMsgEnd});

  Future<String> moveToStoragePath(String path,
      {required String folderName}) async {
    var file = File(path);
    var isExist = await FileUtils.isStoragePath(path);
    if (isExist) {
      return file.path;
    }
    var storagePath = FileUtils.getStoragePath(file.name,
        channelType: channelType, channelID: channelID, folderName: folderName);
    var fullPath = FileUtils.getFullStoragePath(storagePath);
    File targetFile = File(fullPath);
    if (!targetFile.parent.existsSync()) {
      await targetFile.parent.create(recursive: true);
    }
    try {
      await file.copy(fullPath);
      return storagePath;
    } catch (e) {
      e.printError();
    }
    return path;
  }

  void sendStickerMessage(StickerInfo info) async {
    var imageContent = WKStickerContent(info.width, info.height);
    imageContent.url = info.filePath;

    sendMsg(imageContent);
  }

  void sendMediaMessage(File file) async {
    final mimeType = lookupMimeType(file.path);
    if (mimeType?.startsWith("image/") ?? false) {
      var imageInfo = await ImageUtils.getImageInfo(file);
      var imageContent = WKImageContent(imageInfo.width, imageInfo.height);
      var localPath =
          await moveToStoragePath(file.path, folderName: FileDirKeys.wkImages);
      imageContent.localPath = localPath;
      sendMsg(imageContent);
    } else if (mimeType?.startsWith("video/") ?? false) {
      var videoContent = WKVideoContent();
      var localPath =
          await moveToStoragePath(file.path, folderName: FileDirKeys.wkVideos);

      var fullPath = FileUtils.getFullStoragePath(localPath);
      videoContent.localPath = localPath;
      // 获取视频宽高
      var videoInfo = await VideoUtils.getVideoInfo(fullPath);
      videoContent.width = videoInfo.width.toInt();
      videoContent.height = videoInfo.height.toInt();
      videoContent.second = videoInfo.duration.inSeconds;
      videoContent.size = videoInfo.size;

      // 获取视频封面
      var coverLocalPath = await VideoUtils.getVideoThumbnail(fullPath);
      if (coverLocalPath != null) {
        videoContent.coverLocalPath = coverLocalPath;
      }
      sendMsg(videoContent);
    }
  }

  void sendTextMessage(String content, List<String> mentionIds) async {
    if (content.isEmpty) {
      return;
    }

    WKTextContent text = WKTextContent(content);
    if (mentionIds.isNotEmpty) {
      var mentionInfo = WKMentionInfo();
      var uidList = <String>[];
      for (var id in mentionIds) {
        if (id == "-1") {
          mentionInfo.mentionAll = true;
        } else {
          uidList.add(id);
        }
      }
      mentionInfo.uids = uidList;
      text.mentionInfo = mentionInfo;
    }

    sendMsg(text);
  }

  Future<void> sendVoiceMessage(File file) async {
    var duration = await AudioUtils.getAudioDuration(file.path);
    if ((duration?.inMilliseconds ?? 0) < 500) {
      return;
    }
    WKVoiceContent content = WKVoiceContent(duration?.inSeconds ?? 0);
    var localPath =
        await moveToStoragePath(file.path, folderName: FileDirKeys.wkVoices);
    content.localPath = localPath;
    sendMsg(content);
  }

  Future<void> sendFileMessage(File file) async {
    WKFileContent fileContent = WKFileContent();
    var fileSize = await file.length();
    var localPath = await moveToStoragePath(file.path,
        folderName: FileDirKeys.chatDownloadFile);

    fileContent.localPath = localPath;
    fileContent.name = file.name;
    fileContent.extension = file.ext;
    fileContent.size = fileSize;
    sendMsg(fileContent);
  }

  /// 发送个人名片
  Future<void> sendCardMessage(WKChannel channel) async {
    WKCardContent cardContent =
        WKCardContent(channel.channelID, channel.displayName);
    cardContent.vercode = channel.vercode;
    sendMsg(cardContent);
  }

  Future<void> sendMsg(WKMessageContent messageContent,
      {WKChannel? toChannel, Setting? msgSetting}) async {
    onSendMsgStart?.call();

    if (messageContent.contentType == WkMessageContentType.text &&
        replyWKMsg != null) {
      WKReply wkReply = WKReply();
      wkReply.payload =
          replyWKMsg?.wkMsgExtra?.messageContent ?? replyWKMsg?.messageContent;
      wkReply.fromName = await replyWKMsg?.fromName ?? "";
      wkReply.fromUID = replyWKMsg?.fromUID ?? "";
      wkReply.messageId = replyWKMsg?.messageID ?? "";
      wkReply.messageSeq = replyWKMsg?.messageSeq ?? 0;
      wkReply.rootMid = replyWKMsg?.messageContent?.reply?.rootMid ??
          replyWKMsg?.messageID ??
          "";
      messageContent.reply = wkReply;
    }

    WKChannel channel = toChannel ?? await getChatChannelInfo();

    var option = WKSendOptions();
    Setting setting = msgSetting ?? Setting();
    setting.receipt = channel.receipt; //开启回执
    option.setting = setting;
    await WKIM.shared.messageManager
        .sendWithOption(messageContent, channel, option);

    onSendMsgEnd?.call();
  }

  Future<WKChannel> getChatChannelInfo() async {
    WKChannel? channel =
        await WKIM.shared.channelManager.getChannel(channelID, channelType);
    channel ??= WKChannel(channelID, channelType);
    return channel;
  }

  Future<bool> canResendMsg(String channelID, int channelType) async {
    if (channelType == WKChannelType.personal) {
      return true;
    }
    var channel =
        await WKIM.shared.channelManager.getChannel(channelID, channelType);
    var member = await WKIM.shared.channelMemberManager
        .getMember(channelID, channelType, CacheHelper.uid ?? '');

    if (member != null) {
      if (channel != null && channel.forbidden == 1) {
        if (member.role == WKChannelMemberRole.admin) {
          return true;
        }
        if (member.role == WKChannelMemberRole.manager) {
          return member.forbiddenExpirationTime <= 0;
        }
        return false;
      }
      if (member.forbiddenExpirationTime > 0) {
        return false;
      }
    }

    return true;
  }

  void resendMessage(WKMsg msg) async {
    var canResend = msg.status != WKSendMsgResult.sendSuccess &&
        msg.status != WKSendMsgResult.sendLoading;
    if (!canResend) {
      return;
    }
    if (!await canResendMsg(msg.channelID, msg.channelType)) {
      EasyLoading.showError(globalContext?.l10n.forbiddenCanNotResend ?? '');
      return;
    }

    var messageContent = msg.messageContent;
    if (messageContent == null) {
      return;
    }

    var context = globalContext!;
    var content = context.l10n.resendMsgTip;
    if (msg.status == WKSendMsgResult.noRelation) {
      content = context.l10n.resendNoRelationGroup;
    } else if (msg.status == WKSendMsgResult.blackList) {
      content = msg.channelType == WKChannelType.group
          ? context.l10n.resendNoRelationGroup
          : context.l10n.resendNoRelationUser;
    } else if (msg.status == WKSendMsgResult.notOnWhiteList) {
      content = context.l10n.resendNoRelationUser;
    }

    var result = (await DialogUtils.showConfirmDialog(context, content,
            title: context.l10n.msgSendFail)) ??
        false;
    if (result) {
      var option = WKSendOptions();
      option.setting = msg.setting;
      await WKIM.shared.messageManager.deleteWithClientMsgNo(msg.clientMsgNO);
      await WKIM.shared.messageManager.sendWithOption(
          messageContent,
          msg.getChannelInfo() ?? WKChannel(msg.channelID, msg.channelType),
          option);
    }
  }

  Future<void> forwardMsg(WKMsg msg, WKChannel channel) async {
    var msgContent = msg.messageContent;
    if (msg.wkMsgExtra?.messageContent != null) {
      msgContent = msg.wkMsgExtra?.messageContent;
    }
    if (msgContent != null) {
      Setting setting = Setting();
      setting.receipt = channel.receipt; //开启回执
      await sendMsg(msgContent, toChannel: channel, msgSetting: setting);
    }
  }
}
