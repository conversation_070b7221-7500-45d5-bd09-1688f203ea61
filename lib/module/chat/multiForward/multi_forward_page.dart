import 'package:and/model/content/wk_multi_forward_content.dart';
import 'package:and/model/extension/wk_multi_forward_content_ext.dart';
import 'package:and/module/chat/multiForward/multi_forward_logic.dart';
import 'package:and/module/chat/widget/message/position/receiver_position_renderer.dart';
import 'package:and/module/chat/widget/multi_forward_item.dart';
import 'package:and/module/chat/widget/ui_msg_item.dart';
import 'package:and/router/router.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:wukongimfluttersdk/entity/msg.dart';
import 'package:wukongimfluttersdk/type/const.dart';

class MultiForwardArgument {
  String? clientMsgNo;
  WKMultiForwardContent? content;

  MultiForwardArgument({required this.clientMsgNo, this.content});

  factory MultiForwardArgument.fromGet() {
    return (Get.arguments as MultiForwardArgument);
  }

  String getTag() {
    return "${clientMsgNo ?? ''}_${content?.hashCode}";
  }

  static getTagFromGet() {
    return MultiForwardArgument.fromGet().getTag();
  }
}

class MultiForwardPage extends StatefulWidget {
  const MultiForwardPage({super.key});

  @override
  State<StatefulWidget> createState() {
    return _ChatPageState();
  }

  static void open({String? clientMsgNo, WKMultiForwardContent? content}) {
    Get.toNamed(RouteGet.chatMultiForward,
        preventDuplicates: false,
        arguments: MultiForwardArgument(
          clientMsgNo: clientMsgNo,
          content: content,
        ));
  }
}

class _ChatPageState extends State<MultiForwardPage> {
  late MultiForwardArgument argument = Get.arguments as MultiForwardArgument;
  late MultiForwardLogic logic =
      Get.find<MultiForwardLogic>(tag: argument.getTag());
  late final multiForwardContent = logic.data;

  @override
  void dispose() {
    super.dispose();

    Get.delete<MultiForwardLogic>(tag: argument.getTag());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: Obx(() => _buildUserInfo()),
        ),
        resizeToAvoidBottomInset: false,
        body: _buildContent());
  }

  Widget _buildUserInfo() {
    return Text(multiForwardContent.value?.getDisplayName(context) ?? "",
        maxLines: 2, overflow: TextOverflow.ellipsis);
  }

  Widget _buildContent() {
    return Obx(() => _buildChatList());
  }

  Widget _buildChatList() {
    var list = multiForwardContent.value?.msgList ?? [];
    return CustomScrollView(
      slivers: [
        SliverList.separated(
            itemBuilder: (BuildContext context, int index) {
              WKMsg item = list[index];
              item.status = -1;
              return Padding(
                padding: EdgeInsets.symmetric(horizontal: 10),
                child: MultiForwardItem(msg: item),
              );
            },
            separatorBuilder: (context, index) =>
                Divider(height: 0.5, color: Colors.grey.withOpacity(0.3)),
            itemCount: list.length),
      ],
    );
  }
}
