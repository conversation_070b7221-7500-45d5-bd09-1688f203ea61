import 'package:and/module/chat/chat_logic.dart';
import 'package:and/module/chat/chat_page.dart';
import 'package:and/module/chat/multiForward/multi_forward_logic.dart';
import 'package:get/get.dart';

import 'multi_forward_page.dart';

class MultiForwardBinding extends Bindings {
  @override
  void dependencies() {
    MultiForwardArgument argument = Get.arguments as MultiForwardArgument;
    Get.lazyPut(() => MultiForwardLogic(argument: argument), tag: argument.getTag());
  }
}
