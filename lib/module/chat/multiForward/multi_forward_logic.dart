import 'package:and/app.dart';
import 'package:and/common/utils/easy_loading_helper.dart';
import 'package:and/constant/wk_cmd_keys.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/model/content/wk_multi_forward_content.dart';
import 'package:and/module/common/controller/simple_controller.dart';
import 'package:get/get.dart';
import 'package:oktoast/oktoast.dart';
import 'package:wukongimfluttersdk/entity/msg.dart';
import 'package:wukongimfluttersdk/type/const.dart';
import 'package:wukongimfluttersdk/wkim.dart';

import 'multi_forward_page.dart';

class MultiForwardLogic extends SimpleController<WKMultiForwardContent> {
  final String key = "multi_forward";
  final MultiForwardArgument argument;

  String get clientMsgNo => argument.clientMsgNo??'';

  MultiForwardLogic({required this.argument});

  @override
  void onReady() {
    super.onReady();
    _initListener();

    EasyLoadingHelper.show(onAction: () async {
      await refreshData();
    });
  }

  @override
  void onClose() {
    _removeListener();
    super.onClose();
  }

  @override
  Future<WKMultiForwardContent?> loadData() async {
    WKMultiForwardContent? content = argument.content;
    if (content == null) {
      var msg =
          await WKIM.shared.messageManager.getWithClientMsgNo(clientMsgNo);
      if (msg != null) {
        content = msg.messageContent as WKMultiForwardContent?;
      }
    }
    var msgList = content?.msgList;
    for (var msg in msgList ?? <WKMsg>[]) {
      if (msg.messageContent == null) {
        var item =
            await WKIM.shared.messageManager.getWithMessageID(msg.messageID);
        var content = item?.messageContent;
        msg.messageContent = content;
        msg.contentType = item?.contentType ?? WkMessageContentType.unknown;
      }
    }
    return content;
  }

  _initListener() {
    WKIM.shared.cmdManager.addOnCmdListener(key, (wkcmd) async {
      if (wkcmd.cmd == WKCMDKeys.wkMessageRevoke) {
        var messageId = wkcmd.param['message_id'];
        var msg = await WKIM.shared.messageManager.getWithMessageID(messageId);
        if (msg?.clientMsgNO == clientMsgNo) {
          var context = globalContext;
          if (context != null) {
            showToast(context.l10n.msgRevoked);
            Get.back();
          }
        }
      }
    });
  }

  _removeListener() {
    WKIM.shared.cmdManager.removeCmdListener(key);
  }
}
