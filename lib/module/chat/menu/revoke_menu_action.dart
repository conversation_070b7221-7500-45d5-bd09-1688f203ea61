import 'package:and/l10n/l10n.dart';
import 'package:and/model/extension/wk_msg_ext.dart';
import 'package:and/module/chat/menu/message_menu_action.dart';
import 'package:and/module/videocall/videocall_logic.dart';
import 'package:and/utils/image_path.dart';
import 'package:and/utils/popmenu_util.dart';
import 'package:flutter/material.dart';
import 'package:wukongimfluttersdk/entity/msg.dart';

class RevokeMenuAction extends MessageMenuAction {
  @override
  MenuItem render(
      BuildContext context, WKMsg msg, Function(MessageMenuType) onTap) {
    return MenuItem(
        text: context.l10n.revoke,
        icon: ImagePath.ic_msg_withdraw,
        onTap: () {
          onTap(getType());
        });
  }

  @override
  Future<bool> canRender(WKMsg msg) async {
    return await msg.canRevoke() && !msg.isCallMsg;
  }

  @override
  MessageMenuType getType() {
    return MessageMenuType.revoke;
  }
}
