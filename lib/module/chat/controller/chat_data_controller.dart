import 'chat_operation.dart';

abstract class ChatDataController<T> {
  Future<void> insert(T message, {int? index});

  Future<void> insertAll(List<T> messages, {int? index});

  Future<void> update(T oldMessage, T newMessage);

  Future<void> updateWhere(T newMessage, bool Function(T element) test);

  Future<void> remove(T message);

  Future<void> removeWhere(bool Function(T element) test);

  Future<void> set(List<T> messages);

  Future<void> clear();

  T? firstOrNull({bool Function(T element)? where});

  T? lastOrNull({bool Function(T element)? where});

  List<T> get messages;

  Stream<ChatOperation> get operationsStream;

  void dispose();
}
