import 'dart:async';

import 'package:and/common/extension/common_ext.dart';
import 'package:get/get.dart';

import 'chat_data_controller.dart';
import 'chat_operation.dart';
import 'upload_progress_mixin.dart';

class InMemoryChatController<T>
    with UploadProgressMixin
    implements ChatDataController<T> {
  final RxList<T> _messages;
  final _operationsController = StreamController<ChatOperation>.broadcast();
  final bool Function(T a, T b)? _equalityComparer;

  InMemoryChatController({List<T>? messages, bool Function(T a, T b)? equalityComparer})
      : _messages = RxList<T>(messages ?? []),
        _equalityComparer = equalityComparer;

  bool _contains(T message) {
    if (_equalityComparer != null) {
      return _messages.any((element) => _equalityComparer(element, message));
    }
    return _messages.contains(message);
  }

  @override
  Future<void> insert(T message, {int? index}) async {
    if (_contains(message)) return;

    if (index == null) {
      _messages.add(message);
      _operationsController.add(
        ChatOperation.insert(message, _messages.length - 1),
      );
    } else {
      _messages.insert(index, message);
      _operationsController.add(ChatOperation.insert(message, index));
    }
  }

  @override
  Future<void> insertAll(List<T> messages, {int? index}) async {
    if (messages.isEmpty) return;

    // 过滤掉已存在的消息
    final newMessages =
        messages.where((msg) => !_contains(msg)).toList();
    if (newMessages.isEmpty) return;

    if (index == null) {
      _messages.addAll(newMessages);
      _operationsController.add(
        ChatOperation.insert(newMessages, _messages.length - 1),
      );
    } else {
      _messages.insertAll(index, newMessages);
      _operationsController.add(ChatOperation.insert(newMessages, index));
    }
  }

  @override
  Future<void> remove(T message) async {
    final index = _messages.indexOf(message);

    if (index > -1) {
      _messages.removeAt(index);
      _operationsController.add(ChatOperation.remove(message, index));
    }
  }

  @override
  Future<void> removeWhere(bool Function(T element) test) async {
    final message = _messages.where(test).firstOrNull;
    if (message == null) return;

    remove(message);
  }

  @override
  Future<void> update(T oldMessage, T newMessage) async {
    if (oldMessage == newMessage) return;

    final index = _messages.indexOf(oldMessage);

    if (index > -1) {
      _messages[index] = newMessage;
      _operationsController.add(ChatOperation.update(oldMessage, newMessage));
    }
  }

  @override
  Future<void> updateWhere(T newMessage, bool Function(T element) test) async {
    var oldMessage = _messages.where(test).firstOrNull;
    if (oldMessage == null) return;
    update(oldMessage, newMessage);
  }

  @override
  Future<void> set(List<T> messages) async {
    _messages.value = messages;
    _operationsController.add(ChatOperation.set());
  }

  @override
  Future<void> clear() async {
    set([]);
  }

  @override
  RxList<T> get messages => _messages;

  @override
  Stream<ChatOperation> get operationsStream => _operationsController.stream;

  @override
  void dispose() {
    disposeUploadProgress();
    _operationsController.close();
  }

  @override
  T? firstOrNull({bool Function(T element)? where}) {
    if (where == null) {
      return messages.firstOrNull;
    }
    return messages.where(where).firstOrNull;
  }

  @override
  T? lastOrNull({bool Function(T element)? where}) {
    if (where == null) {
      return messages.lastOrNull;
    }
    return messages.lastItemWhereOrNull(where);
  }

}
