
enum ChatOperationType { insert, update, remove, set }

class ChatOperation<T> {
  final ChatOperationType type;
  final T? oldMessage;
  final T? message;
  final int? index;

  ChatOperation._(this.type, {this.oldMessage, this.message, this.index});

  factory ChatOperation.insert(T message, int index) => ChatOperation._(
        ChatOperationType.insert,
        message: message,
        index: index,
      );

  factory ChatOperation.update(T oldMessage, T message) =>
      ChatOperation._(
        ChatOperationType.update,
        oldMessage: oldMessage,
        message: message,
      );

  factory ChatOperation.remove(T message, int index) => ChatOperation._(
        ChatOperationType.remove,
        message: message,
        index: index,
      );

  factory ChatOperation.set() => ChatOperation._(ChatOperationType.set);
}
