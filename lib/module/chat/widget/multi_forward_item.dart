import 'package:and/module/chat/widget/message/common/msg_from_name_widget.dart';
import 'package:and/module/chat/widget/message/common/msg_reply_widget.dart';
import 'package:and/module/chat/widget/message/type/message_content_renderer.dart';
import 'package:and/module/chat/widget/ui_msg_item.dart';
import 'package:and/module/user/user_info_page.dart';
import 'package:and/utils/common_helper.dart';
import 'package:and/widget/avatar_widget.dart';
import 'package:flutter/material.dart';
import 'package:wukongimfluttersdk/entity/channel.dart';
import 'package:wukongimfluttersdk/entity/msg.dart';

import 'message/common/msg_time_widget.dart';

class MultiForwardItem extends StatelessWidget {
  final WKMsg msg;

  const MultiForwardItem({
    super.key,
    required this.msg,
  });

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: Alignment.centerLeft,
      child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            InkWell(
                onTap: () {
                  UserInfoPage.open(channelID: msg.fromUID);
                },
                child: Column(
                  children: [
                    SizedBox(
                      height: 10,
                    ),
                    AvatarWidget(
                      CommonHelper.getAvatarUrl(msg.fromUID),
                      size: 30,
                      fontSize: 14,
                    ),
                  ],
                )),
            Flexible(
                flex: 1,
                child: Container(
                  margin: const EdgeInsets.only(
                      left: 12, right: 60, top: 8, bottom: 8),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      MsgFromNameWidget(msg: msg),
                      _buildContent(context, msg),
                      MsgTimeWidget(msg: msg, isShowFullTime: true),
                      ReplyContentWidget(
                        msg: msg,
                        callback: MessageItemCallback(
                          resendMessage: (WKMsg msg) {},
                          reEditMessage: (WKMsg msg) {},
                        ),
                      ),
                    ],
                  ),
                ))
          ]),
    );
  }

  Widget _buildContent(BuildContext context, WKMsg msg) {
    var render = MessageRendererRegistry().getRenderer(msg);
    if (render == null) {
      return Container();
    }
    Widget content = render.render(
        context,
        msg,
        MessageItemCallback(
          resendMessage: (msg) {},
          reEditMessage: (msg) {},
          isAnonymous: false,
        ));
    if (render.needContainer()) {
      return Container(
          margin: EdgeInsets.symmetric(vertical: 4),
          padding: EdgeInsets.symmetric(horizontal: 10, vertical: 10),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
          ),
          child: content);
    }
    return content;
  }
}
