import 'package:and/module/chat/widget/message/type/message_content_renderer.dart';
import 'package:and/module/chat/widget/ui_msg_item.dart';
import 'package:and/utils/common_helper.dart';
import 'package:and/widget/avatar_widget.dart';
import 'package:flutter/material.dart';
import 'package:wukongimfluttersdk/entity/msg.dart';

import 'message_position_renderer.dart';

class CenterPositionRenderer extends MessagePositionRenderer {
  @override
  Widget render(BuildContext context, WKMsg msg, MessageItemCallback callback) {
    return Center(
      child:
            MessageRendererRegistry().getRenderer(msg)?.render(context, msg, callback) ??
                Container(),
    );
  }

  @override
  bool canRender(WKMsg msg) {
    var contentType = msg.contentType;
    return contentType >= 1000 && contentType <= 2000 || msg.wkMsgExtra?.revoke == 1;
  }
}
