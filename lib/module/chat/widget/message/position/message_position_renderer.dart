import 'package:and/module/chat/widget/message/position/center_position_renderer.dart';
import 'package:and/module/chat/widget/message/type/message_content_renderer.dart';
import 'package:and/module/chat/widget/ui_msg_item.dart';
import 'package:flutter/material.dart';
import 'package:wukongimfluttersdk/entity/msg.dart';

import 'receiver_position_renderer.dart';
import 'sender_position_renderer.dart';

abstract class MessagePositionRenderer {
  Widget render(BuildContext context, WKMsg msg, MessageItemCallback callback);
  bool canRender(WKMsg msg);
}


class MessagePositionRendererRegistry {
  static final MessagePositionRendererRegistry _instance =
      MessagePositionRendererRegistry._internal();
  factory MessagePositionRendererRegistry() => _instance;
  MessagePositionRendererRegistry._internal();

  final List<MessagePositionRenderer> _renderers = [
    CenterPositionRenderer(),
    SenderPositionRenderer(),
    ReceiverPositionRenderer(),
  ];

  MessagePositionRenderer? getRenderer(WKMsg msg) {
    for (var renderer in _renderers) {
      if (renderer.canRender(msg)) {
        return renderer;
      }
    }
    return null;
  }
}