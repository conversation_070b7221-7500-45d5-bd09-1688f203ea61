import 'package:and/cache/cache_helper.dart';
import 'package:and/module/chat/widget/message/common/msg_from_name_widget.dart';
import 'package:and/module/chat/widget/message/common/msg_reactions_widget.dart';
import 'package:and/module/chat/widget/message/common/msg_reply_widget.dart';
import 'package:and/module/chat/widget/message/common/msg_time_widget.dart';
import 'package:and/module/chat/widget/message/common/voice_recognize_widget.dart';
import 'package:and/module/chat/widget/message/type/message_content_renderer.dart';
import 'package:and/module/chat/widget/ui_msg_item.dart';
import 'package:and/utils/common_helper.dart';
import 'package:and/widget/avatar_widget.dart';
import 'package:flutter/material.dart';
import 'package:wukongimfluttersdk/entity/msg.dart';
import 'package:wukongimfluttersdk/type/const.dart';

import 'message_position_renderer.dart';

class ReceiverPositionRenderer extends MessagePositionRenderer {
  @override
  Widget render(BuildContext context, WKMsg msg, MessageItemCallback callback) {
    var isPublicUser = !callback.isAnonymous ||
        (callback.isManagerMsg?.call(msg.fromUID) ?? false);

    return Align(
      alignment: Alignment.centerLeft,
      child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            InkWell(
                onTap: isPublicUser
                    ? () {
                        callback.viewUserInfo?.call(msg.fromUID);
                      }
                    : null,
                child: Column(
                  children: [
                    SizedBox(
                      height: 9,
                    ),
                    AvatarWidget(
                      CommonHelper.getAvatarUrl(msg.fromUID,
                          anonymous: !isPublicUser),
                      size: 34,
                      fontSize: 14,
                    ),
                  ],
                )),
            Flexible(
                flex: 1,
                child: Container(
                  margin: const EdgeInsets.only(
                      left: 12, right: 60, top: 8, bottom: 8),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Visibility(
                          visible: msg.channelType == WKChannelType.group,
                          child: MsgFromNameWidget(msg: msg, textSize: 12)),
                      SizedBox(height: 4),
                      Container(
                          padding: EdgeInsets.symmetric(
                              horizontal: 10, vertical: 10),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              MessageRendererRegistry()
                                      .getRenderer(msg)
                                      ?.render(context, msg, callback) ??
                                  Container(),
                              MsgReactionsWidget(
                                msg: msg,
                                isAnonymous: callback.isAnonymous,
                                onPickReactionTap: (key) {
                                  callback.pickEmoji?.call(msg, key);
                                },
                                onEmojiReactionTap: (emoji) {
                                  callback.emojiReaction?.call(msg, emoji);
                                },
                              )
                            ],
                          )),
                      Visibility(
                          visible:
                              callback.isShowMessageTime?.call(msg) ?? false,
                          child: MsgTimeWidget(msg: msg)),
                      ReplyContentWidget(
                        msg: msg,
                        callback: callback,
                      ),
                      VoiceRecognizeWidget(
                        voiceRecognize: callback.getVoiceRecognize?.call(msg),
                      ),
                    ],
                  ),
                ))
          ]),
    );
  }

  @override
  bool canRender(WKMsg msg) {
    return msg.fromUID != CacheHelper.userProfile?.uid;
  }
}
