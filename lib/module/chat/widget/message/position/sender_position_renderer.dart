import 'package:and/cache/cache_helper.dart';
import 'package:and/common/res/colours.dart';
import 'package:and/model/user_info.dart';
import 'package:and/module/chat/widget/message/common/msg_reactions_widget.dart';
import 'package:and/module/chat/widget/message/common/msg_reply_widget.dart';
import 'package:and/module/chat/widget/message/common/msg_time_widget.dart';
import 'package:and/module/chat/widget/message/common/voice_recognize_widget.dart';
import 'package:and/module/chat/widget/message/type/message_content_renderer.dart';
import 'package:and/module/chat/widget/ui_msg_item.dart';
import 'package:and/module/user/user_info_page.dart';
import 'package:and/utils/common_helper.dart';
import 'package:and/widget/avatar_widget.dart';
import 'package:flutter/material.dart';
import 'package:wukongimfluttersdk/entity/msg.dart';

import 'message_position_renderer.dart';

class SenderPositionRenderer extends MessagePositionRenderer {
  @override
  Widget render(BuildContext context, WKMsg msg, MessageItemCallback callback) {
    var isPublicUser =
        !callback.isAnonymous || (callback.isManagerMsg?.call(msg.fromUID) ?? false);

    return Align(
      alignment: Alignment.centerRight,
      child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Flexible(
                flex: 1,
                child: Container(
                    margin: const EdgeInsets.only(
                        left: 60, right: 12, top: 8, bottom: 8),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Container(
                            padding: EdgeInsets.symmetric(
                                horizontal: 10, vertical: 10),
                            decoration: BoxDecoration(
                              color: DColor.primaryColor.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.end,
                              children: [
                                MessageRendererRegistry()
                                        .getRenderer(msg)
                                        ?.render(context, msg, callback) ??
                                    Container(),
                                MsgReactionsWidget(
                                  msg: msg,
                                  isAnonymous: callback.isAnonymous,
                                  onPickReactionTap: (key) {
                                    callback.pickEmoji?.call(msg, key);
                                  },
                                  onEmojiReactionTap: (emoji) {
                                    callback.emojiReaction?.call(msg, emoji);
                                  },
                                )
                              ],
                            )),
                        Visibility(
                            visible:
                                callback.isShowMessageTime?.call(msg) ?? false,
                            child: MsgTimeWidget(msg: msg)),
                        ReplyContentWidget(
                          msg: msg,
                          callback: callback,
                        ),
                        VoiceRecognizeWidget(
                          voiceRecognize: callback.getVoiceRecognize?.call(msg),
                        ),
                      ],
                    ))),
            InkWell(
              onTap: isPublicUser
                  ? () {
                      callback.viewUserInfo?.call(CacheHelper.uid ?? '');
                    }
                  : null,
              child: Column(
                children: [
                  SizedBox(
                    height: 9,
                  ),
                  AvatarWidget(
                    CommonHelper.getAvatarUrl(CacheHelper.uid ?? '',
                        anonymous: !isPublicUser),
                    size: 34,
                    fontSize: 14,
                  ),
                ],
              ),
            ),
          ]),
    );
  }

  @override
  bool canRender(WKMsg msg) {
    return msg.fromUID == CacheHelper.userProfile?.uid;
  }
}
