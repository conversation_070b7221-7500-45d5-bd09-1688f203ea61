import 'package:and/l10n/l10n.dart';
import 'package:and/module/chat/widget/ui_msg_item.dart';
import 'package:flutter/material.dart';
import 'package:wukongimfluttersdk/entity/msg.dart';
import 'package:and/common/res/text_styles.dart';
import 'message_content_renderer.dart';

class UnknownMessageRenderer extends MessageContentRenderer {
  @override
  Widget render(BuildContext context, WKMsg msg, MessageItemCallback callback) {
    return Text(
      context.l10n.chatMessageUnknown,
      style: TextStyles.fontSize15Normal,
    );
  }

  @override
  bool canRender(WKMsg msg) {
    return true;
  }
}
