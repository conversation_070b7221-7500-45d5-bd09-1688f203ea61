import 'package:and/app.dart';
import 'package:and/cache/cache_helper.dart';
import 'package:and/common/res/colours.dart';
import 'package:and/common/res/text_styles.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/model/extension/wk_channel_ext.dart';
import 'package:and/model/extension/wk_msg_ext.dart';
import 'package:and/model/extension/wx_channel_member_ext.dart';
import 'package:and/module/chat/widget/ui_msg_item.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:wukongimfluttersdk/entity/msg.dart';
import 'package:wukongimfluttersdk/type/const.dart';
import 'package:wukongimfluttersdk/wkim.dart';

import 'message_content_renderer.dart';

class RevokedMessageRenderer extends MessageContentRenderer {
  @override
  Widget render(BuildContext context, WKMsg msg, MessageItemCallback callback) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
      decoration: BoxDecoration(
        color: Color(0x26000000),
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 2,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: RevokedMessageWidget(msg: msg, onReEditTap: () {
        callback.reEditMessage(msg);
      }),
    );
  }

  @override
  Widget reply(BuildContext context, WKMsg msg, MessageItemCallback callback) {
    var reply = msg.messageContent?.reply;
    if (reply == null) {
      return Container();
    }
    return Text(context.l10n.replyMsgIsRevoked,
        style: TextStyles.fontSize13Normal.copyWith(color: DColor.hintText));
  }

  @override
  bool canRender(WKMsg msg) {
    return msg.wkMsgExtra?.revoke == 1;
  }
}

class RevokedMessageWidget extends StatefulWidget {
  final WKMsg msg;
  final Function() onReEditTap;

  const RevokedMessageWidget({
    super.key,
    required this.msg,
    required this.onReEditTap,
  });

  @override
  State<StatefulWidget> createState() {
    return _RevokedMessageWidgetState();
  }
}

class _RevokedMessageWidgetState extends State<RevokedMessageWidget> {
  late Future<String?> _contentFuture;

  @override
  void initState() {
    super.initState();
    _contentFuture = getContent();
  }

  @override
  void didUpdateWidget(covariant RevokedMessageWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    _contentFuture = getContent();
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<String?>(
      future: _contentFuture,
      builder: (BuildContext context, AsyncSnapshot<String?> snapshot) {
        var canReEdit = widget.msg.fromUID == CacheHelper.uid &&
            ((DateTime.now().millisecondsSinceEpoch / 1000).toInt() -
                    widget.msg.timestamp <
                300) && widget.msg.contentType == WkMessageContentType.text;
        return RichText(
            textScaler: MediaQuery.of(context).textScaler,
            text: TextSpan(children: [
          TextSpan(
              text: snapshot.data ?? '',
              style: TextStyles.fontSize13Normal.copyWith(color: Colors.white)),
              if (canReEdit)
                TextSpan(
                  text: " ${context.l10n.reEdit}",
                  style: TextStyles.fontSize13Normal.copyWith(color: Colors.orange),
                  recognizer: canReEdit
                      ? (TapGestureRecognizer()
                        ..onTap = () {
                          widget.onReEditTap();
                        })
                      : null),
        ]));
      },
    );
  }

  Future<String?> getContent() async {
    return widget.msg.showRevokeMsg;
  }
}
