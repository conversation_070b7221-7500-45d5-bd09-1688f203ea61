import 'dart:io';
import 'dart:math';

import 'package:and/common/res/text_styles.dart';
import 'package:and/model/content/wk_file_content.dart';
import 'package:and/model/extension/wk_file_content_ext.dart';
import 'package:and/model/extension/wk_image_content_ext.dart';
import 'package:and/module/chat/widget/message/common/msg_file_widget.dart';
import 'package:and/module/chat/widget/message/common/msg_image_widget.dart';
import 'package:and/module/chat/widget/message/common/msg_status_widget.dart';
import 'package:and/module/chat/widget/ui_msg_item.dart';
import 'package:and/utils/common_helper.dart';
import 'package:and/utils/image_path.dart';
import 'package:and/widget/image/simple_pics_wiper.dart';
import 'package:extended_image/extended_image.dart';
import 'package:flutter/material.dart';
import 'package:wukongimfluttersdk/entity/msg.dart';
import 'package:wukongimfluttersdk/model/wk_image_content.dart';
import 'package:wukongimfluttersdk/type/const.dart';
import 'package:wukongimfluttersdk/wkim.dart';

import 'message_content_renderer.dart';

class FileMessageRenderer extends MessageContentRenderer {
  @override
  Widget render(BuildContext context, WKMsg msg, MessageItemCallback callback) {
    return FileContentWidget(
        channelID: msg.channelID,
        channelType: msg.channelType,
        messageID: msg.messageID,
        fileContent: msg.messageContent as WKFileContent,
        extraWidget: MsgStatusWidget(
            msg: msg,
            onResendMsgTap: () {
              callback.resendMessage(msg);
            }));
  }

  @override
  bool needContainer() {
    return true;
  }

  @override
  Widget reply(BuildContext context, WKMsg msg, MessageItemCallback callback) {
    var reply = msg.messageContent?.reply;
    if (reply == null) {
      return Container();
    }
    return FutureBuilder<WKMsg?>(
      future: WKIM.shared.messageManager.getWithMessageID(reply.messageId),
      builder: (BuildContext context, AsyncSnapshot<WKMsg?> snapshot) {
        var replyMsg = snapshot.data ?? msg;
        return FileContentWidget(
          channelID: replyMsg.channelID,
          channelType: replyMsg.channelType,
          messageID: reply.messageId,
          fileContent: reply.payload as WKFileContent,
          titleFontSize: 12,
          sizeFontSize: 10,
        );
      },
    );
  }

  @override
  bool canRender(WKMsg msg) {
    return msg.contentType == WkMessageContentType.file;
  }
}
