import 'package:and/module/chat/widget/message/common/msg_status_widget.dart';
import 'package:and/module/chat/widget/message/common/msg_voice_widget.dart';
import 'package:and/module/chat/widget/ui_msg_item.dart';
import 'package:flutter/material.dart';
import 'package:wukongimfluttersdk/entity/msg.dart';
import 'package:wukongimfluttersdk/model/wk_voice_content.dart';
import 'package:wukongimfluttersdk/type/const.dart';
import 'package:wukongimfluttersdk/wkim.dart';

import 'message_content_renderer.dart';

class VoiceMessageRenderer extends MessageContentRenderer {
  @override
  Widget render(BuildContext context, WKMsg msg, MessageItemCallback callback) {
    return Container(
      constraints: BoxConstraints(maxWidth: 200),
      padding: const EdgeInsets.symmetric(vertical: 6),
      child: VoiceContentWidget(
          channelID: msg.channelID,
          channelType: msg.channelType,
          messageID: msg.messageID,
          voiceContent: msg.messageContent as WKVoiceContent,
          extraWidget: MsgStatusWidget(
              msg: msg,
              onResendMsgTap: () {
                callback.resendMessage(msg);
              })),
    );
  }

  @override
  bool needContainer() {
    return true;
  }

  @override
  Widget reply(BuildContext context, WKMsg msg, MessageItemCallback callback) {
    var reply = msg.messageContent?.reply;
    if (reply == null) {
      return Container();
    }
    WKVoiceContent voiceContent = reply.payload! as WKVoiceContent;
    return InkWell(
      onTap: () async {
        WKMsg wkMsg = await WKIM.shared.messageManager
                .getWithMessageID(reply.messageId) ??
            msg;
        VoicePlayUtils voicePlayerUtils = VoicePlayUtils(
          voiceContent,
          channelID: wkMsg.channelID,
          channelType: wkMsg.channelType,
          messageID: wkMsg.messageID,
        );
        await voicePlayerUtils.playOrDownload();
      },
      child: super.reply(context, msg, callback),
    );
  }

  @override
  bool canRender(WKMsg msg) {
    return msg.contentType == WkMessageContentType.voice;
  }
}
