import 'package:and/common/res/colours.dart';
import 'package:and/common/res/text_styles.dart';
import 'package:and/constant/user_online_status.dart';
import 'package:and/constant/wk_channel_extras.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/model/extension/wk_channel_ext.dart';
import 'package:and/module/contact/widget/ui_channel_name_widget.dart';
import 'package:and/utils/image_path.dart';
import 'package:and/utils/svg_path.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_svg/svg.dart';
import 'package:wukongimfluttersdk/db/const.dart';
import 'package:wukongimfluttersdk/entity/channel.dart';
import 'package:wukongimfluttersdk/type/const.dart';

class ChatUserWidget extends StatelessWidget {
  final WKChannel channel;
  final int count;

  const ChatUserWidget({super.key, required this.channel, this.count = 0});

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Flexible(
            flex: 1,
            child: channel.channelType == WKChannelType.group
                ? _buildGroupInfo(context)
                : _buildUserInfo(context)),
        SizedBox(width: 4),
        _buildMuteInfo(context),
      ],
    );
  }

  Widget _buildMuteInfo(BuildContext context) {
    var isMute = channel.mute == 1;
    return isMute
        ? Image.asset(
            ImagePath.ic_list_mute,
            height: 15,
            color: Colors.grey,
          )
        : Container();
  }

  Widget _buildGroupInfo(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Row(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Flexible(
                  flex: 1,
                  child: UiChannelNameWidget(channel: channel, textSize: 18)),
              Visibility(
                  visible: count > 0,
                  child: Text(
                    " ($count)",
                    style: TextStyles.fontSize18Normal,
                  ))
            ]),
        if (channel.channelRemark.isNotEmpty)
          Text(
            channel.channelName,
            style: TextStyles.fontSize13Normal,
          ),
      ],
    );
  }

  Widget _buildUserInfo(BuildContext context) {
    var signature = "";
    if (channel.remoteExtraMap != null) {
      signature = channel.signature ?? '';
    }
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        UiChannelNameWidget(channel: channel, textSize: 18),
        if (signature.isNotEmpty)
          Padding(
            padding: EdgeInsets.only(top: 4),
            child: _buildUserSummary(context, signature),
          )
      ],
    );
  }

  Widget _buildUserSummary(BuildContext context, String signature) {
    return GestureDetector(
      onTap: () {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            content: Text(
              signature,
              style: TextStyles.fontSize15Normal,
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text(context.l10n.globalOk),
              ),
            ],
          ),
        );
      },
      child: Text(
        signature,
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
        style: TextStyles.fontSize13Normal.copyWith(
          color: DColor.secondaryTextColor,
        ),
      ),
    );
  }

  Widget _buildUserStatus(BuildContext context) {
    return Container(
        padding: EdgeInsets.symmetric(horizontal: 6, vertical: 2),
        decoration: BoxDecoration(
          color: channel.isOnline ? Colors.green : Colors.grey,
          borderRadius: BorderRadius.circular(4),
        ),
        child: channel.isOnline
            ? _buildOnlineStatus(context)
            : _buildOfflineStatus(context));
  }

  Widget _buildOnlineStatus(BuildContext context) {
    var client = context.l10n.phone;
    if (channel.deviceFlag == UserOnlineStatus.Web) {
      client = context.l10n.web;
    } else if (channel.deviceFlag == UserOnlineStatus.PC) {
      client = context.l10n.pc;
    }

    return Text(
      "$client${context.l10n.online}",
      style: TextStyles.fontSize13Normal.copyWith(
        color: Colors.white,
      ),
    );
  }

  Widget _buildOfflineStatus(BuildContext context) {
    if (channel.lastOffline == 0) {
      return Container();
    }
    var onlineTime = context.l10n.offline;
    DateTime lastOfflineTime =
        DateTime.fromMillisecondsSinceEpoch(channel.lastOffline * 1000);
    DateTime now = DateTime.now();
    // 计算时间差
    Duration difference = now.difference(lastOfflineTime);

    if (difference.inSeconds < 60) {
      onlineTime = context.l10n.justNow;
    } else if (difference.inSeconds < 600) {
      onlineTime = context.l10n.minAgo((difference.inSeconds / 60).toInt());
    }

    return Text(
      onlineTime,
      style: TextStyles.fontSize13Normal.copyWith(
        color: Colors.white,
      ),
    );
  }
}
