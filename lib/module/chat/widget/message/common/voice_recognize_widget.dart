import 'package:and/common/res/colours.dart';
import 'package:and/common/res/text_styles.dart';
import 'package:and/model/voice_recognize.dart';
import 'package:flutter/material.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';

class VoiceRecognizeWidget extends StatefulWidget {
  final VoiceRecognize? voiceRecognize;

  const VoiceRecognizeWidget({super.key, required this.voiceRecognize});

  @override
  State<StatefulWidget> createState() {
    return _VoiceRecognizeWidgetState();
  }
}

class _VoiceRecognizeWidgetState extends State<VoiceRecognizeWidget> {
  @override
  Widget build(BuildContext context) {
    var recognize = widget.voiceRecognize;
    if (recognize == null) {
      return Container();
    }
    return Container(
      margin: EdgeInsets.only(top: 4),
      padding: EdgeInsets.symmetric(horizontal: 4, vertical: 4),
      decoration: BoxDecoration(
        color: DColor.greyE7,
        borderRadius: BorderRadius.circular(8),
      ),
      child: (recognize.event == SteamEvent.begin)
          ? LoadingAnimationWidget.waveDots(
              color: DColor.primaryColor,
              size: 20,
            )
          : Text(
              recognize.data?.text ?? '',
              style: TextStyles.fontSize13Normal,
            ),
    );
  }
}
