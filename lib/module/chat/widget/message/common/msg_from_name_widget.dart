import 'package:and/common/res/text_styles.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/model/extension/wk_msg_ext.dart';
import 'package:flutter/material.dart';
import 'package:wukongimfluttersdk/entity/msg.dart';

class MsgFromNameWidget extends StatefulWidget {
  final WKMsg msg;
  final double? textSize;

  const MsgFromNameWidget(
      {super.key, required this.msg, this.textSize});

  @override
  State<StatefulWidget> createState() {
    return _MsgFromNameWidgetState();
  }
}

class _MsgFromNameWidgetState extends State<MsgFromNameWidget> {
  late Future<String?> _fromName;

  @override
  void initState() {
    super.initState();
    _fromName = widget.msg.fromNameDisplay;
  }

  @override
  void didUpdateWidget(covariant MsgFromNameWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    _fromName = widget.msg.fromNameDisplay;
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      child: FutureBuilder<String?>(
        future: _fromName,
        builder: (BuildContext context, AsyncSnapshot<String?> snapshot) {
          return Text(
            snapshot.data ?? '',
            style: TextStyles.fontSize15Normal.copyWith(
              fontSize: widget.textSize ?? 15,
            ),
            overflow: TextOverflow.ellipsis,
            maxLines: 1,
          );
        },
      ),
    );
  }
}
