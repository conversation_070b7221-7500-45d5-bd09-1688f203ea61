import 'dart:math';

import 'package:and/cache/cache_helper.dart';
import 'package:and/common/res/colours.dart';
import 'package:and/common/res/text_styles.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/model/extension/wk_msg_ext.dart';
import 'package:and/utils/dialog_utils.dart';
import 'package:and/utils/lottie_path.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/material.dart';
import 'dart:math';
import 'dart:typed_data';
import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';
import 'package:wukongimfluttersdk/entity/channel.dart';
import 'package:wukongimfluttersdk/entity/msg.dart';
import 'package:wukongimfluttersdk/type/const.dart';
import 'package:wukongimfluttersdk/wkim.dart';

class MsgStatusWidget extends StatelessWidget {
  final WKMsg msg;
  final Function()? onResendMsgTap;
  final Color textColor;

  // 构造函数，设置默认值
  const MsgStatusWidget(
      {super.key,
      required this.msg,
      required this.onResendMsgTap,
      this.textColor = DColor.secondaryTextColor});

  @override
  Widget build(BuildContext context) {
    return _buildStatus(context);
  }

  Widget _buildStatus(BuildContext context) {
    var isSend = msg.isDeleted == 0 && msg.fromUID == CacheHelper.uid;
    if (!isSend) return SizedBox();
    if (msg.status == -1) return SizedBox();

    String? path;
    var autoRepeat = false;
    var isNeedColored = true;

    if (msg.status == WKSendMsgResult.sendSuccess) {
      if (msg.setting.receipt == 1 && (msg.wkMsgExtra?.readedCount ?? 0) > 0) {
        path = LottiePath.ticks_double;
      } else {
        path = LottiePath.ticks_single;
      }
    } else if (msg.status == WKSendMsgResult.sendLoading) {
      path = LottiePath.msg_sending;
      autoRepeat = true;
    } else {
      path = LottiePath.error;
      isNeedColored = false;
    }

    if (isNeedColored) {
      return ColorFiltered(
        colorFilter: ColorFilter.mode(textColor, BlendMode.srcIn),
        child: Lottie.asset(path, repeat: autoRepeat, height: 20),
      );
    }

    return InkWell(
        onTap: () {
          onResendMsgTap?.call();
        },
        child: Lottie.asset(path, repeat: autoRepeat, height: 20));
  }
}
