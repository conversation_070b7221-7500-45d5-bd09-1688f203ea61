import 'dart:math';

import 'package:flutter/material.dart';

class WaveformView extends StatelessWidget {
  final List<int> waveformData;
  final double progress;
  final bool isFresh;
  final Color innerColor;
  final Color outerColor;
  final Color freshColor;
  final double width;
  final double height;

  // 构造函数，设置默认值
  const WaveformView({super.key,
    required this.waveformData,
    this.progress = 0.0,
    this.isFresh = false,
    this.innerColor = Colors.grey,
    this.outerColor = Colors.deepOrange,
    this.freshColor = Colors.blue,
    this.width = double.infinity,
    this.height = 40
  });

  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      size: Size(width, height), // Adjust height as needed
      painter: WaveformPainter(
        waveformData: waveformData,
        progress: progress,
        isFresh: isFresh,
        innerColor: innerColor,
        outerColor: outerColor,
        freshColor: freshColor,
      ),
    );
  }
}


class WaveformPainter extends CustomPainter {
  final List<int> waveformData;
  final double progress;
  final bool isFresh;
  final Color innerColor;
  final Color outerColor;
  final Color freshColor;

  WaveformPainter({
    required this.waveformData,
    required this.progress,
    required this.isFresh,
    required this.innerColor,
    required this.outerColor,
    required this.freshColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paintInner = Paint()..color = isFresh && progress == 0 ? freshColor : innerColor;
    final paintOuter = Paint()..color = outerColor;

    final totalBarsCount = (size.width / 3).toInt();
    final samplesCount = waveformData.length * 8 ~/ 5;
    final samplesPerBar = samplesCount / totalBarsCount;

    double barCounter = 0;
    int nextBarNum = 0;
    double barNum = 0;

    for (int a = 0; a < samplesCount; a++) {
      if (a != nextBarNum) continue;

      int drawBarCount = 0;
      int lastBarNum = nextBarNum;
      while (lastBarNum == nextBarNum) {
        barCounter += samplesPerBar;
        nextBarNum = barCounter.toInt();
        drawBarCount++;
      }

      int bitPointer = a * 5;
      int byteNum = bitPointer ~/ 8;
      int byteBitOffset = bitPointer - byteNum * 8;
      int currentByteCount = 8 - byteBitOffset;
      int nextByteRest = 5 - currentByteCount;

      int value = (waveformData[byteNum] >> byteBitOffset) & ((1 << min(5, currentByteCount)) - 1);
      if (nextByteRest > 0) {
        value = (value << nextByteRest) | (waveformData[byteNum + 1] & ((1 << nextByteRest) - 1));
      }

      for (int b = 0; b < drawBarCount; b++) {
        double x = barNum * 3.0;
        double barHeight = max(1.0, 14.0 * value / 31.0);
        double y = size.height;

        if (x < progress && x + 2.0 < progress) {
          canvas.drawRect(
            Rect.fromLTRB(x, y - barHeight, x + 2.0, y),
            paintOuter,
          );
        } else {
          canvas.drawRect(
            Rect.fromLTRB(x, y - barHeight, x + 2.0, y),
            paintInner,
          );
          if (x < progress) {
            canvas.drawRect(
              Rect.fromLTRB(x, y - barHeight, progress, y),
              paintOuter,
            );
          }
        }
        barNum++;
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}
