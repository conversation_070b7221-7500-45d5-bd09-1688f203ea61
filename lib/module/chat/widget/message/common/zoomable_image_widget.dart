import 'dart:async';
import 'dart:io';

import 'package:and/app.dart';
import 'package:and/common/res/colours.dart';
import 'package:and/common/res/text_styles.dart';
import 'package:and/constant/file_dir_keys.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/model/content/wk_file_content.dart';
import 'package:and/model/extension/wk_file_content_ext.dart';
import 'package:and/module/file/file_viewer_page.dart';
import 'package:and/module/video/video_page.dart';
import 'package:and/utils/download/chat_file_utils.dart';
import 'package:and/utils/download/download_manager.dart';
import 'package:and/utils/fileUtils.dart';
import 'package:and/utils/format_utils.dart';
import 'package:and/utils/image_path.dart';
import 'package:and/widget/image/simple_pics_wiper.dart';
import 'package:background_downloader/background_downloader.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mime/mime.dart';
import 'package:oktoast/oktoast.dart';
import 'package:open_file/open_file.dart';

class ZoomableImage extends StatefulWidget {
  final String imagePath;
  final double width;
  final double height;
  final Function() onTap;

  const ZoomableImage({
    super.key,
    required this.imagePath,
    required this.onTap,
    this.width = 20,
    this.height = 20,
  });

  @override
  State<ZoomableImage> createState() => _ZoomableImageState();
}

class _ZoomableImageState extends State<ZoomableImage>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;

  int _scaleCount = 0; // 新增缩放计数器
  final int _maxScaleCount = 2; // 最大缩放次数

  final double _minScale = 0.6; // 最小缩放比例
  final Duration _duration = const Duration(milliseconds: 200); // 动画时长

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: _duration,
      lowerBound: _minScale,
    )..value = 1.0; // 初始值设为正常大小

    _scaleAnimation = CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    );

    _controller.addStatusListener(_handleAnimationStatus);
  }

  void _handleAnimationStatus(AnimationStatus status) {
    var value = _scaleAnimation.value;
    if (status == AnimationStatus.completed ||
        status == AnimationStatus.dismissed) {
      _scaleCount++; // 每次动画完成增加计数器
      if (_scaleCount < _maxScaleCount) {
        if (value == 1.0) {
          _controller.reverse();
        } else {
          _controller.forward();
        }
      } else {
        widget.onTap();
        _controller.forward(); // 达到最大缩放次数后恢复原大小
        _scaleCount = 0; // 重置计数器
      }
    } else if (status == AnimationStatus.dismissed) {
      _controller.forward();
      _scaleCount = 0; // 如果动画被取消，也重置计数器
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _onTapUp(TapUpDetails _) {
    _controller.reverse();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapUp: _onTapUp,
      child: AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Container(
              width: widget.width * _scaleAnimation.value,
              height: widget.height * _scaleAnimation.value,
              margin: EdgeInsets.all(2),
              child: child);
        },
        child: Image.asset(
              widget.imagePath,
              fit: BoxFit.cover,
            ),
      ),
    );
  }
}
