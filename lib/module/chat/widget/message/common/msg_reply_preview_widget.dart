import 'package:and/common/res/colours.dart';
import 'package:and/common/res/text_styles.dart';
import 'package:and/model/extension/wk_msg_ext.dart';
import 'package:flutter/material.dart';
import 'package:wukongimfluttersdk/entity/msg.dart';

class MsgReplyPreviewWidget extends StatefulWidget {
  final WKMsg msg;
  final Function() onClose;

  const MsgReplyPreviewWidget(
      {super.key, required this.msg, required this.onClose});

  @override
  State<StatefulWidget> createState() {
    return _MsgReplyPreviewWidgetState();
  }
}

class _MsgReplyPreviewWidgetState extends State<MsgReplyPreviewWidget> {
  late Future<String?> _content;
  late Future<String?> _fromName;

  @override
  void initState() {
    super.initState();
    _content = widget.msg.previewContent;
    _fromName = widget.msg.fromNameDisplay;
  }

  @override
  void didUpdateWidget(covariant MsgReplyPreviewWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    _content = widget.msg.previewContent;
    _fromName = widget.msg.fromNameDisplay;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(top: 4),
      padding: EdgeInsets.symmetric(horizontal: 4, vertical: 2),
      decoration: BoxDecoration(
        color: DColor.greyE7,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Expanded(
              child: RichText(
            textScaler: MediaQuery.of(context).textScaler,
            maxLines: 2,
            textAlign: TextAlign.start,
            overflow: TextOverflow.ellipsis,
            text: TextSpan(style: TextStyles.fontSize15Normal, children: [
              WidgetSpan(child: _buildName()),
              WidgetSpan(
                  child: Padding(
                padding: EdgeInsets.only(left: 5),
                child: _buildContent(),
              )),
            ]),
          )),
          GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: widget.onClose,
              child: Padding(
                padding: EdgeInsets.all(4),
                child: Icon(
                  Icons.close,
                  size: 15,
                  color: Color(0xFF999999),
                ),
              ))
        ],
      ),
    );
  }

  Widget _buildName() {
    return FutureBuilder<String?>(
      future: _fromName,
      builder: (BuildContext context, AsyncSnapshot<String?> snapshot) {
        var name = snapshot.data ?? '';
        if (name.isNotEmpty) {
          name += ": ";
        }
        return Text(
          name,
          style: TextStyles.fontSize15Normal.copyWith(color: DColor.hintText),
          overflow: TextOverflow.ellipsis,
          maxLines: 1,
        );
      },
    );
  }

  Widget _buildContent() {
    return FutureBuilder<String?>(
      future: _content,
      builder: (BuildContext context, AsyncSnapshot<String?> snapshot) {
        return Text(
          snapshot.data ?? '',
          style: TextStyles.fontSize15Normal.copyWith(color: DColor.hintText),
          overflow: TextOverflow.ellipsis,
          maxLines: 1,
        );
      },
    );
  }
}
