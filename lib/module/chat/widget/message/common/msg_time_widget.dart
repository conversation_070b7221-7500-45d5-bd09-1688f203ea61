import 'dart:math';

import 'package:and/cache/cache_helper.dart';
import 'package:and/common/res/colours.dart';
import 'package:and/common/res/text_styles.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/model/extension/wk_msg_ext.dart';
import 'package:and/utils/dialog_utils.dart';
import 'package:and/utils/lottie_path.dart';
import 'package:and/utils/time_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/material.dart';
import 'dart:math';
import 'dart:typed_data';
import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';
import 'package:wukongimfluttersdk/entity/channel.dart';
import 'package:wukongimfluttersdk/entity/msg.dart';
import 'package:wukongimfluttersdk/type/const.dart';
import 'package:wukongimfluttersdk/wkim.dart';

class MsgTimeWidget extends StatelessWidget {
  final WKMsg msg;
  final Color textColor;
  final bool isShowFullTime;

  const MsgTimeWidget(
      {super.key,
      required this.msg,
      this.isShowFullTime = false,
      this.textColor = DColor.secondaryTextColor});

  @override
  Widget build(BuildContext context) {
    return _buildMessageTime(context);
  }

  Widget _buildMessageTime(BuildContext context) {
    return Text(
        isShowFullTime
            ? TimeUtils.getNewChatTime(msg.timestamp, isShowHM: true)
            : msg.formatTime,
        style: TextStyles.fontSize13Normal.copyWith(color: textColor));
  }
}
