import 'dart:io';
import 'dart:math';

import 'package:and/model/extension/wk_image_content_ext.dart';
import 'package:and/widget/image/simple_pics_wiper.dart';
import 'package:extended_image/extended_image.dart';
import 'package:flutter/material.dart';
import 'package:wukongimfluttersdk/model/wk_image_content.dart';

class ImageContentWidget extends StatelessWidget {
  final double maxHeight;
  final double maxWidth;
  final double? width;
  final double? height;
  final WKImageContent imageContent;

  const ImageContentWidget(
      {super.key,
      required this.imageContent,
      this.width,
      this.height,
      this.maxHeight = 200,
      this.maxWidth = 150});

  @override
  Widget build(BuildContext context) {
    double originalWidth = imageContent.width.toDouble();
    double originalHeight = imageContent.height.toDouble();
    if (originalHeight == 0) {
      originalHeight = maxHeight;
    }
    if (originalWidth == 0) {
      originalWidth = maxWidth;
    }
    double widthRatio = maxWidth / originalWidth;
    double heightRatio = maxHeight / originalHeight;
    double ratio = min(widthRatio, heightRatio);

    double imageWidth = originalWidth * ratio;
    double imageHeight = originalHeight * ratio;
    if (width != null) {
      imageWidth = width!;
    }
    if (height != null) {
      imageHeight = height!;
    }
    var tag = UniqueKey();
    var imageUrl = imageContent.imageUrl;
    return GestureDetector(
      onTap: () {
        previewImage(context, imageUrl, tag: tag);
      },
      child: Hero(
          tag: tag,
          child: _buildImage(
            context,
            imageUrl,
            imageWidth,
            imageHeight,
          )),
    );
  }

  Widget _buildImage(BuildContext context, String imageUrl, double imageWidth,
      double imageHeight) {
    if (imageUrl.startsWith("http")) {
      return ExtendedImage.network(
          shape: BoxShape.rectangle,
          borderRadius: BorderRadius.circular(8),
          clipBehavior: Clip.antiAlias,
          width: imageWidth,
          height: imageHeight,
          imageUrl,
          fit: BoxFit.cover);
    }

    var file = File(imageUrl);
    if (!file.existsSync()) {
      return Container(
        width: imageWidth,
        height: imageHeight,
        decoration: BoxDecoration(
          color: Colors.grey[200],
          borderRadius: BorderRadius.circular(8),
        ),
      );
    }
    return ExtendedImage.file(file,
        shape: BoxShape.rectangle,
        borderRadius: BorderRadius.circular(8),
        clipBehavior: Clip.antiAlias,
        width: imageWidth,
        height: imageHeight,
        fit: BoxFit.cover);
  }
}
