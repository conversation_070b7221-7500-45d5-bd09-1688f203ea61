import 'package:and/common/res/colours.dart';
import 'package:and/common/res/text_styles.dart';
import 'package:and/l10n/l10n.dart';
import 'package:flutter/material.dart';

class PromptNewMsgWidget extends StatelessWidget {
  const PromptNewMsgWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
            child: Container(
          margin: EdgeInsets.symmetric(horizontal: 15, vertical: 5),
          color: DColor.divider,
          height: 1,
        )),
        Text(context.l10n.newsLine, style: TextStyles.fontSize13Normal),
        Expanded(
            child: Container(
          margin: EdgeInsets.symmetric(horizontal: 15),
          color: DColor.divider,
          height: 1,
        )),
      ],
    );
  }
}
