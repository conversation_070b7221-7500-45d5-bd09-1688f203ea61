import 'package:and/common/res/text_styles.dart';
import 'package:and/model/extension/wk_msg_ext.dart';
import 'package:and/module/chat/widget/message/position/message_position_renderer.dart';
import 'package:and/module/chat/widget/message/type/message_content_renderer.dart';
import 'package:and/module/contact/widget/ui_channel_name_widget.dart';
import 'package:and/utils/common_helper.dart';
import 'package:and/widget/avatar_widget.dart';
import 'package:flutter/material.dart';
import 'package:wukongimfluttersdk/entity/channel.dart';
import 'package:wukongimfluttersdk/entity/msg.dart';

import 'msg_from_name_widget.dart';

class MsgForwardPreviewWidget extends StatefulWidget {
  final List<WKChannel> channels;
  final String content;

  const MsgForwardPreviewWidget(
      {super.key, required this.channels, required this.content});

  @override
  State<StatefulWidget> createState() {
    return _MsgForwardPreviewWidgetState();
  }
}

class _MsgForwardPreviewWidgetState extends State<MsgForwardPreviewWidget> {
  @override
  Widget build(BuildContext context) {
    return CustomScrollView(
      shrinkWrap: true,
      slivers: [
        SliverPadding(
            padding: const EdgeInsets.symmetric(vertical: 15),
            sliver: SliverList(
              delegate:
                  SliverChildBuilderDelegate((BuildContext context, int index) {
                var channel = widget.channels[index];
                return Padding(padding: EdgeInsets.symmetric(vertical: 6), child: Row(children: [
                  AvatarWidget(
                    CommonHelper.getAvatarUrl(channel.channelID,
                        channelType: channel.channelType),
                    size: 30,
                    fontSize: 14,
                  ),
                  const SizedBox(
                    width: 10,
                  ),
                  Expanded(child: UiChannelNameWidget(
                    channel: channel,
                  ))
                ]));
              }, childCount: widget.channels.length),
            )),
        SliverToBoxAdapter(
            child: Text(widget.content, style: TextStyles.fontSize15Normal)),
      ],
    );
  }
}
