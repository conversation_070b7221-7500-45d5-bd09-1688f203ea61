import 'package:and/common/res/colours.dart';
import 'package:and/common/res/text_styles.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/module/chat/widget/message/position/message_position_renderer.dart';
import 'package:and/utils/image_path.dart';
import 'package:flutter/material.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'package:wukongimfluttersdk/entity/msg.dart';

class ChatChoicePanelWidget extends StatelessWidget {
  final int choiceCount;
  final Function()? onForwardTap;
  final Function()? onDeleteTap;

  const ChatChoicePanelWidget(
      {super.key,
      required this.choiceCount,
      this.onForwardTap,
      this.onDeleteTap});

  @override
  Widget build(BuildContext context) {
    return Container(
      color: DColor.greyE7,
      child: SafeArea(
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 8),
          color: DColor.greyE7,
          child: Row(children: [
        Expanded(child: _buildForward(context)),
        Expanded(child: _buildDelete(context)),
      ]),
    )));
  }

  Widget _buildForward(BuildContext context) {
    return _buildItem(
      context,
      context.l10n.forward,
      ImagePath.ic_msg_forward,
      choiceCount > 0,
      () {
        onForwardTap?.call();
      },
    );
  }

  Widget _buildDelete(BuildContext context) {
    return _buildItem(
      context,
      context.l10n.delete,
      ImagePath.ic_msg_delete,
      choiceCount > 0,
      () {
        onDeleteTap?.call();
      },
    );
  }

  Widget _buildItem(BuildContext context, String title, String icon,
      bool isEnable, Function() onTap) {
    return InkWell(
      onTap: isEnable ? onTap : null,
      child: Column(
        children: [
          Image.asset(icon,
              height: 30, color: Colors.black.withOpacity(isEnable ? 1 : 0.3)),
          Text(title,
              style: TextStyles.fontSize15Normal.copyWith(
                  color: Colors.black.withOpacity(isEnable ? 1 : 0.3))),
        ],
      ),
    );
  }
}
