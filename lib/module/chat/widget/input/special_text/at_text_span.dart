import 'package:and/common/extension/common_ext.dart';
import 'package:and/model/extension/wx_channel_member_ext.dart';
import 'package:extended_text_field/extended_text_field.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:wukongimfluttersdk/entity/channel_member.dart';

class AtText extends SpecialText {
  AtText(TextStyle? textStyle, SpecialTextGestureTapCallback? onTap,
      {this.showAtBackground = false,
      this.start,
      this.mentionMembers = const []})
      : super(flag, ' ', textStyle, onTap: onTap);
  static const String flag = '@';
  final int? start;
  final List<WKChannelMember> mentionMembers;

  /// whether show background for @somebody
  final bool showAtBackground;

  @override
  bool isEnd(String value) {
    var isMatch = mentionMembers.any((e) => "${e.displayName} " == value);
    if (isMatch) {
      return true;
    }
    return false;
  }

  @override
  InlineSpan finishText() {
    final TextStyle? textStyle =
        this.textStyle?.copyWith(color: Colors.blue, fontSize: 16.0);

    final String memberName = getContent();
    final String atText = toString();
    final text = atText;
    final actualText = atText;
    final currentMember = mentionMembers
        .firstItemWhereOrNull((element) => element.displayName == memberName);
    if (currentMember == null) {
      return TextSpan(text: text);
    }

    return showAtBackground
        ? BackgroundTextSpan(
            background: Paint()..color = Colors.blue.withOpacity(0.15),
            text: text,
            actualText: actualText,
            start: start!,

            ///caret can move into special text
            deleteAll: true,
            style: textStyle,
            recognizer: (TapGestureRecognizer()
              ..onTap = () {
                if (onTap != null) {
                  onTap!(atText.trim());
                }
              }))
        : SpecialTextSpan(
            text: text,
            actualText: actualText,
            start: start!,
            style: textStyle,
            recognizer: (TapGestureRecognizer()
              ..onTap = () {
                if (onTap != null) {
                  onTap!(atText.trim());
                }
              }));
  }
}
