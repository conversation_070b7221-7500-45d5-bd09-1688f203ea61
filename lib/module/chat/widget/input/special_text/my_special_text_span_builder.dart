import 'package:extended_text_field/extended_text_field.dart';
import 'package:flutter/material.dart';
import 'package:wukongimfluttersdk/entity/channel_member.dart';
import 'at_text_span.dart';

class MySpecialTextSpanBuilder extends SpecialTextSpanBuilder {
  /// whether show background for @somebody
  final bool showAtBackground;
  final List<WKChannelMember> mentionMembers;

  MySpecialTextSpanBuilder(
      {this.showAtBackground = false, this.mentionMembers = const []});

  @override
  SpecialText? createSpecialText(String flag,
      {TextStyle? textStyle,
      SpecialTextGestureTapCallback? onTap,
      int? index}) {
    if (flag == '') {
      return null;
    }

    ///index is end index of start flag, so text start index should be index-(flag.length-1)
    if (isStart(flag, AtText.flag)) {
      return AtText(textStyle, onTap,
          start: index! - (AtText.flag.length - 1),
          showAtBackground: showAtBackground,
          mentionMembers: mentionMembers);
    }
    return null;
  }
}
