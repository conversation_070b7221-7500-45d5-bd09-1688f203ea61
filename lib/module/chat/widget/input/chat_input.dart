import 'dart:ui';

import 'package:and/common/res/text_styles.dart';
import 'package:and/model/extension/wx_channel_member_ext.dart';
import 'package:and/model/sticker_info.dart';
import 'package:and/module/chat/widget/input/panel/audio/voice_panel_widget.dart';
import 'package:and/module/chat/widget/input/panel/emoji/emoji_panel_widget.dart';
import 'package:and/module/chat/widget/input/panel/panel_registry.dart';
import 'package:and/module/chat/widget/input/panel/tool_panel_widget.dart';
import 'package:and/module/chat/widget/input/special_text/my_special_text_span_builder.dart';
import 'package:chat_bottom_container/panel_container.dart';
import 'package:chat_bottom_container/typedef.dart';
import 'package:extended_text_field/extended_text_field.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:multi_trigger_autocomplete/multi_trigger_autocomplete.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:wukongimfluttersdk/entity/channel_member.dart';

import 'options/mention_autocomplete_options.dart';

class ChatInput extends StatefulWidget {
  static const Color _sentinelColor = Colors.transparent;

  final TextEditingController? textEditingController;
  final double? left;
  final double? right;
  final double? top;
  final double? bottom;
  final double? sigmaX;
  final double? sigmaY;
  final EdgeInsetsGeometry? padding;
  final Widget? emojiIcon;
  final Widget? attachmentIcon;
  final Widget? sendIcon;
  final Widget? voiceIcon;
  final double? gap;
  final InputBorder? inputBorder;
  final bool? filled;
  final Widget? topWidget;
  final Color? backgroundColor;
  final Color? emojiIconColor;
  final Color? attachmentIconColor;
  final Color? sendIconColor;
  final Color? voiceIconColor;
  final Color? hintColor;
  final Color? textColor;
  final Color? inputFillColor;
  final bool enableMentionAll;
  final List<WKChannelMember>? users;
  final Widget? bottomWidget;
  final bool isAnonymous;
  final Function(String text, List<String> mentionIds)? onMessageSendCallback;
  final Function(ChatToolType toolType)? onMessageToolCallback;
  final Function(StickerInfo)? onStickerEmojiSendCallback;
  final Function(String path)? onVoiceRecordCallback;
  final Function(String text)? onSaveDraft;

  const ChatInput(
      {super.key,
      this.textEditingController,
      this.left = 0,
      this.right = 0,
      this.top,
      this.bottom = 0,
      this.sigmaX = 20,
      this.sigmaY = 20,
      this.padding = const EdgeInsets.all(8.0),
      this.attachmentIcon = const Icon(Icons.add),
      this.sendIcon = const Icon(Icons.send_rounded),
      this.voiceIcon = const Icon(Icons.mic),
      this.emojiIcon = const Icon(Icons.emoji_emotions_outlined),
      this.gap = 8,
      this.inputBorder = const OutlineInputBorder(
        borderSide: BorderSide.none,
        borderRadius: BorderRadius.all(Radius.circular(4)),
      ),
      this.filled = true,
      this.topWidget,
      this.backgroundColor = _sentinelColor,
      this.attachmentIconColor,
      this.sendIconColor,
      this.voiceIconColor,
      this.emojiIconColor,
      this.hintColor,
      this.textColor,
      this.inputFillColor,
      this.enableMentionAll = false,
      this.users,
      this.bottomWidget,
      this.onMessageSendCallback,
      this.onMessageToolCallback,
      this.onStickerEmojiSendCallback,
      this.onVoiceRecordCallback,
      this.onSaveDraft,
      this.isAnonymous = false});

  @override
  State<ChatInput> createState() => ChatInputState();
}

class ChatInputState extends State<ChatInput> {
  final double defaultKeyboardHeight = 300;
  final _key = GlobalKey();

  final GlobalKey<ExtendedTextFieldState> _textFieldKey =
      GlobalKey<ExtendedTextFieldState>();

  final mentionMembers = <WKChannelMember>[];

  late final TextEditingController _textController;

  final controller = ChatBottomPanelContainerController<PanelType>();
  final FocusNode inputFocusNode = FocusNode();
  PanelType currentPanelType = PanelType.none;
  bool readOnly = false;

  final PanelRegistry _panelRegistry = PanelRegistry();

  void _initDefaultPanels() {
    _panelRegistry.register(
      PanelConfig(
          id: PanelType.emoji,
          name: 'emoji',
          icon: Icons.emoji_emotions_outlined,
          readOnly: true,
          builder: (context, keyboardHeight) => EmojiPanelWidget(
                keyboardHeight: keyboardHeight,
                textController: _textController,
                onEmojiAdded: () {
                  setState(() {});
                },
                onStickerEmojiTap: widget.onStickerEmojiSendCallback,
              )),
    );

    _panelRegistry.register(
      PanelConfig(
        id: PanelType.tool,
        name: 'tool',
        icon: Icons.mic,
        readOnly: true,
        builder: (context, keyboardHeight) => ToolPanelWidget(
          tools: ChatToolType.values,
          keyboardHeight: keyboardHeight,
          onTap: (type) {
            // updatePanelType(PanelType.none);
            widget.onMessageToolCallback?.call(type);
          },
        ),
      ),
    );

    _panelRegistry.register(
      PanelConfig(
        id: PanelType.voice,
        name: 'voice',
        icon: Icons.mic,
        readOnly: true,
        builder: (context, keyboardHeight) => VoicePanelWidget(
          keyboardHeight: keyboardHeight,
          onStop: (value) async {
            updatePanelType(PanelType.none);
            widget.onVoiceRecordCallback?.call(value);
          },
          onCancel: () {
            // 取消的时候不隐藏
            // updatePanelType(PanelType.none);
          },
        ),
      ),
    );
  }

  void updateDraft(String draft) {
    _textController.text = draft.trim();
    setState(() {});
  }

  void hiddenKeyboard() {
    // 更新面板类型
    updatePanelType(PanelType.none);
    // 确保输入框失去焦点
    inputFocusNode.unfocus();
    FocusScope.of(context).unfocus();
    controller.updatePanelType(ChatBottomPanelType.none);
    SystemChannels.textInput.invokeMethod('TextInput.hide');
  }

  @override
  void initState() {
    super.initState();
    _textController = widget.textEditingController ?? TextEditingController();
    _initDefaultPanels();
  }

  @override
  void didUpdateWidget(covariant ChatInput oldWidget) {
    super.didUpdateWidget(oldWidget);
  }

  @override
  void dispose() {
    // Only try to dispose text controller if it's not provided, let
    // user handle disposing it how they want.
    widget.onSaveDraft?.call((_textController.text ?? '').trim());
    if (widget.textEditingController == null) {
      _textController.dispose();
    }
    inputFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ClipRect(
      child: BackdropFilter(
        filter: ImageFilter.blur(
          // TODO: remove backdrop filter if both are 0
          sigmaX: widget.sigmaX ?? 0,
          sigmaY: widget.sigmaY ?? 0,
        ),
        child: Container(
          key: _key,
          color: widget.backgroundColor == ChatInput._sentinelColor
              // ignore: deprecated_member_use
              ? Theme.of(context)
                  .colorScheme
                  .surfaceContainerLow
                  .withOpacity(0.8)
              : widget.backgroundColor,
          child: Column(
            children: [
              if (widget.topWidget != null) widget.topWidget!,
              Padding(
                padding: EdgeInsets.symmetric(vertical: 8, horizontal: 8),
                child: _buildInputContainer(),
              ),
              _buildPanelContainer()
            ],
          ),
        ),
      ),
    );
  }

  void _handleSubmitted(String text) {
    if (text.isNotEmpty) {
      //遍历文本，按顺序记录@somebody的 id 数组
      var spans = MySpecialTextSpanBuilder(
                  showAtBackground: true, mentionMembers: mentionMembers)
              .build(text)
              .children ??
          [];

      var memberIds = <String>[];
      for (var span in spans) {
        if (span is BackgroundTextSpan) {
          var text = span.text ?? '';
          if (text.startsWith("@")) {
            var displayName = text.trim().substring(1);
            var member = mentionMembers.firstWhereOrNull(
                (element) => element.displayName == displayName);
            if (member != null) {
              memberIds.add(member.memberUID);
            }
          }
        }
      }

      widget.onMessageSendCallback?.call(text, memberIds);
      mentionMembers.clear();
      _textController.clear();
    }
  }

  Widget _buildInputContainer() {
    final theme = Theme.of(context);
    return Row(
      children: [
        widget.attachmentIcon != null
            ? IconButton(
                padding: EdgeInsets.symmetric(vertical: 8, horizontal: 4),
                icon: currentPanelType == PanelType.tool
                    ? Icon(Icons.cancel_outlined)
                    : widget.attachmentIcon!,
                color: widget.attachmentIconColor ??
                    // ignore: deprecated_member_use
                    theme.colorScheme.onSurface.withOpacity(0.5),
                iconSize: 30,
                onPressed: () async {
                  updatePanelType(
                    PanelType.tool == currentPanelType
                        ? PanelType.keyboard
                        : PanelType.tool,
                  );
                },
              )
            : const SizedBox.shrink(),
        Expanded(
            child: Listener(
          onPointerUp: (event) {
            // Currently it may be emojiPanel.
            if (readOnly) {
              updatePanelType(PanelType.keyboard);
            }
          },
          child: _buildTextField(),
        )),
        widget.emojiIcon != null
            ? IconButton(
                padding: EdgeInsets.symmetric(vertical: 8, horizontal: 4),
                icon: widget.emojiIcon!,
                color: widget.emojiIconColor ??
                    // ignore: deprecated_member_use
                    theme.colorScheme.onSurface.withOpacity(0.5),
                iconSize: 30,
                onPressed: () {
                  updatePanelType(
                    PanelType.emoji == currentPanelType
                        ? PanelType.keyboard
                        : PanelType.emoji,
                  );
                },
              )
            : const SizedBox.shrink(),
        Visibility(
            visible: _textController.text.isEmpty,
            child: widget.voiceIcon != null
                ? IconButton(
                    padding: EdgeInsets.symmetric(vertical: 8, horizontal: 4),
                    icon: widget.voiceIcon!,
                    color: widget.voiceIconColor ??
                        // ignore: deprecated_member_use
                        theme.colorScheme.onSurface.withOpacity(0.5),
                    iconSize: 30,
                    onPressed: () async {
                      var isVoicePanel = currentPanelType == PanelType.voice;
                      if (isVoicePanel) {
                        updatePanelType(PanelType.keyboard);
                      } else {
                        var status = await Permission.microphone.request();
                        var isGranted = status.isGranted;
                        if (isGranted) {
                          updatePanelType(PanelType.voice);
                        } else {
                          showRecordPermissionDialog(context);
                        }
                      }
                    },
                  )
                : const SizedBox.shrink()),
        Visibility(
            visible: _textController.text.isNotEmpty,
            child: widget.sendIcon != null
                ? IconButton(
                    padding: EdgeInsets.symmetric(vertical: 8, horizontal: 4),
                    icon: widget.sendIcon!,
                    color: widget.sendIconColor ??
                        // ignore: deprecated_member_use
                        theme.colorScheme.onSurface.withOpacity(0.5),
                    iconSize: 30,
                    onPressed: () => _handleSubmitted(_textController.text),
                  )
                : const SizedBox.shrink()),
      ],
    );
  }

  Widget _buildTextField() {
    final theme = Theme.of(context);
    return MultiTriggerAutocomplete(
      optionsAlignment: OptionsAlignment.topStart,
      textEditingController: _textController,
      focusNode: inputFocusNode,
      autocompleteTriggers: [
        // Add the triggers you want to use for autocomplete
        AutocompleteTrigger(
          trigger: '@',
          optionsViewBuilder: (context, autocompleteQuery, controller) {
            return MentionAutocompleteOptions(
              query: autocompleteQuery.query,
              users: widget.isAnonymous? []: widget.users ?? [],
              enableMentionAll: widget.enableMentionAll,
              onMentionUserTap: (user) {
                final autocomplete = MultiTriggerAutocomplete.of(context);
                var hasMention = mentionMembers
                    .any((element) => element.memberUID == user.memberUID);
                if (!hasMention) {
                  mentionMembers.add(user);
                }
                autocomplete.acceptAutocompleteOption(user.displayName);
              },
            );
          },
        ),
      ],
      // Add the text field widget you want to use for autocomplete
      fieldViewBuilder: (context, controller, focusNode) {
        return Column(
          children: [
            ExtendedTextField(
              key: _textFieldKey,
              focusNode: focusNode,
              controller: controller,
              minLines: 1,
              maxLines: 5,
              readOnly: readOnly,
              specialTextSpanBuilder: MySpecialTextSpanBuilder(
                  showAtBackground: true, mentionMembers: mentionMembers),
              decoration: InputDecoration(
                hintText: '',
                hintStyle: TextStyles.fontSize15Normal.copyWith(
                  color: widget.hintColor ??
                      // ignore: deprecated_member_use
                      theme.colorScheme.onSurface.withOpacity(0.5),
                ),
                contentPadding:
                    EdgeInsets.symmetric(vertical: 4, horizontal: 4),
                border: widget.inputBorder,
                filled: widget.filled,
                fillColor: widget.inputFillColor ??
                    theme.colorScheme.surfaceContainerHigh
                        // ignore: deprecated_member_use
                        .withOpacity(0.8),
                hoverColor: Colors.transparent,
              ),
              style: TextStyles.fontSize16Normal.copyWith(
                color: widget.textColor ?? theme.colorScheme.onSurface,
              ),
              textInputAction: TextInputAction.newline,
              onChanged: (value) {
                var spans = MySpecialTextSpanBuilder(
                            showAtBackground: true,
                            mentionMembers: mentionMembers)
                        .build(value)
                        .children ??
                    [];

                //删除不存在的 mention
                var memberNames = <String>[];
                for (var span in spans) {
                  if (span is BackgroundTextSpan) {
                    var text = span.text ?? '';
                    if (text.startsWith("@")) {
                      var displayName = text.trim().substring(1);
                      memberNames.add(displayName);
                    }
                  }
                }
                if (memberNames.isEmpty) {
                  mentionMembers.clear();
                } else {
                  mentionMembers.removeWhere(
                      (element) => !memberNames.contains(element.displayName));
                }
                setState(() {});
              },
            ),
            widget.bottomWidget ?? const SizedBox.shrink(),
          ],
        );
      },
    );
  }

  Widget _buildPanelContainer() {
    return ChatBottomPanelContainer<PanelType>(
      controller: controller,
      inputFocusNode: inputFocusNode,
      otherPanelWidget: (panelId) {
        Widget widget = SafeArea(child: SizedBox.shrink());
        if (panelId != null) {
          final panel = _panelRegistry.getPanel(panelId);
          var keyboardHeight = controller.keyboardHeight;
          // 如果键盘高度为0(可能还未初始化)，则使用默认高度
          if (keyboardHeight == 0) {
            keyboardHeight = defaultKeyboardHeight;
          }
          widget = panel?.builder(context, keyboardHeight) ??
              SafeArea(child: SizedBox.shrink());
        }

        return widget;
      },
      onPanelTypeChange: (panelType, data) {
        // 记录当前的面板类型
        switch (panelType) {
          case ChatBottomPanelType.none:
            currentPanelType = PanelType.none;
            break;
          case ChatBottomPanelType.keyboard:
            currentPanelType = PanelType.keyboard;
            break;
          case ChatBottomPanelType.other:
            if (data == null) return;
            currentPanelType = data;
            break;
        }
        setState(() {});
      },
      panelBgColor: Color(0xFFF5F5F5),
    );
  }

  void updatePanelType(PanelType type) async {
    final isSwitchToKeyboard = PanelType.keyboard == type;
    bool isUpdated = false;
    switch (type) {
      case PanelType.keyboard:
        updateInputView(isReadOnly: false);
        break;
      default:
        isUpdated = updateInputView(isReadOnly: true);
        break;
    }

    final panel = _panelRegistry.getPanel(type);
    updatePanelTypeFunc() {
      controller.updatePanelType(
        isSwitchToKeyboard
            ? ChatBottomPanelType.keyboard
            : ChatBottomPanelType.other,
        data: type,
        forceHandleFocus: panel?.forceHandleFocus ?? ChatBottomHandleFocus.none,
      );
    }

    if (isUpdated) {
      // Waiting for the input view to update.
      WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
        updatePanelTypeFunc();
      });
    } else {
      updatePanelTypeFunc();
    }
  }

  bool updateInputView({
    required bool isReadOnly,
  }) {
    if (readOnly != isReadOnly) {
      readOnly = isReadOnly;
      // You can just refresh the input view.
      setState(() {});
      return true;
    }
    return false;
  }
}
