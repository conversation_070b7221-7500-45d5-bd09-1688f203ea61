import 'dart:async';
import 'dart:io';
import 'dart:math';
import 'dart:math' as math;

import 'package:and/common/res/text_styles.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/module/chat/widget/input/panel/audio/platform/audio_recorder_platform.dart';
import 'package:and/utils/audio_utils.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:record/record.dart';

import 'line_wave_voice_widget.dart';

enum DragPosition { none, top, center, bottom }


/// **弹出对话框：引导去设置页**
void showRecordPermissionDialog(BuildContext context) {
  showDialog(
    context: context,
    builder: (context) => AlertDialog(
      title: Text(context.l10n.needVoicePermission),
      content: Text(context.l10n.openVoicePermission),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: Text(context.l10n.globalCancel),
        ),
        TextButton(
          onPressed: () {
            openAppSettings(); // 跳转到系统设置页
            Navigator.pop(context);
          },
          child: Text(context.l10n.globalSetting),
        ),
      ],
    ),
  );
}

class VoicePanelWidget extends StatefulWidget {
  final Function(String path) onStop;
  final Function() onCancel;
  final double? keyboardHeight;

  const VoicePanelWidget({
    super.key,
    required this.onStop,
    required this.onCancel,
    this.keyboardHeight,
  });

  @override
  State<VoicePanelWidget> createState() => _VoicePanelWidgetState();
}

class _VoicePanelWidgetState extends State<VoicePanelWidget>
    with AudioRecorderMixin {
  Timer? _debounceTimer;
  int _recordDuration = 0;
  Timer? _timer;
  late final AudioRecorder _audioRecorder;
  StreamSubscription<RecordState>? _recordSub;
  RecordState _recordState = RecordState.stop;
  StreamSubscription<Amplitude>? _amplitudeSub;

  DragPosition _dragPosition = DragPosition.none;
  bool hasPermission = false;

  final _voiceViewKey = GlobalKey<LineWaveVoiceViewState>();

  @override
  void initState() {
    _audioRecorder = AudioRecorder();

    _recordSub = _audioRecorder.onStateChanged().listen((recordState) {
      _updateRecordState(recordState);
    });

    _amplitudeSub = _audioRecorder
        .onAmplitudeChanged(const Duration(milliseconds: 100))
        .listen((amp) {
      var current = amp.current;
      if (current == double.negativeInfinity) {
        current = 0;
      }
      _voiceViewKey.currentState?.maxAmplitude = calculateMaxAmplitude(current);
    });

    super.initState();
  }

  double calculateMaxAmplitude(double x) {
    final exponent = x / 20;
    final maxAmplitude = pow(10, exponent);
    return maxAmplitude.toDouble();
  }

  @override
  void dispose() {
    _timer?.cancel();
    _debounceTimer?.cancel();
    _recordSub?.cancel();
    _amplitudeSub?.cancel();
    _audioRecorder.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      height: widget.keyboardHeight ?? 300,
      child: SafeArea(
          child: Padding(
        padding: EdgeInsets.symmetric(vertical: 20),
        child: _buildRecordView(),
      )),
    );
  }

  Widget _buildRecordView() {
    return LayoutBuilder(builder: (context, constraints) {
      double maxHeight = constraints.maxHeight > 0
          ? constraints.maxHeight
          : MediaQuery.of(context).size.height;
      return GestureDetector(
          // onLongPress: _start,
          // // 长按开始录音
          // onLongPressUp: _stop,
          onVerticalDragStart: (details) async {
            _start();
            setState(() {
              _dragPosition = DragPosition.center;
            });
          },
          onVerticalDragUpdate: (details) {
            setState(() {
              var percent = details.localPosition.dy / maxHeight;
              if (percent < 0.3) {
                _dragPosition = DragPosition.top;
              } else if (percent > 0.7) {
                _dragPosition = DragPosition.bottom;
              } else {
                _dragPosition = DragPosition.center;
              }
            });
          },
          onVerticalDragEnd: (details) {
            _debounceTimer?.cancel();
            _debounceTimer = Timer(const Duration(milliseconds: 300), () {
              _stop(isCancel: _dragPosition == DragPosition.top);
              setState(() {
                _dragPosition = DragPosition.none;
              });
            });
          },
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Expanded(flex: 1, child: LineWaveVoiceView(key: _voiceViewKey)),
              Expanded(flex: 4, child: _buildRecordControl()),
              Expanded(flex: 1, child: _buildRecordText()),
            ],
          ));
    });
  }

  Future<void> _start() async {
    var isRecording = await _audioRecorder.isRecording();
    if (isRecording) {
      print("当前正在录音中");
      return;
    }
    if (!hasPermission) {
      var status = await _audioRecorder.hasPermission();
      hasPermission = hasPermission || status;
    }
    if (!hasPermission) {
      showRecordPermissionDialog(context);
      return;
    }
    try {
      const encoder = AudioEncoder.wav;
      // if (!await _isEncoderSupported(encoder)) {
      //   return;
      // }

      // final devs = await _audioRecorder.listInputDevices();
      // debugPrint(devs.toString());

      const config = RecordConfig(encoder: encoder, numChannels: 1);

      // Record to file
      await recordFile(_audioRecorder, config);

      // Record to stream
      // await recordStream(_audioRecorder, config);

      _recordDuration = 0;

      _startTimer();
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
    }
  }

  Future<void> _stop({bool isCancel = false}) async {
    _voiceViewKey.currentState?.stopRecord();
    if (isCancel || !hasPermission) {
      await _audioRecorder.cancel();
      _onCancelRecord(null);
    } else {
      var isRecording = await _audioRecorder.isRecording();
      if (!isRecording) {
        _onCancelRecord(null);
        return;
      }
      final path = await _audioRecorder.stop();

      if (path != null) {
        var duration = await AudioUtils.getAudioDuration(path);
        if ((duration?.inMilliseconds ?? 0) < 500) {
          _onCancelRecord(path);
          return;
        }
        _onStopRecord(path);
      } else {
        _onCancelRecord(path);
      }
    }
  }

  Future<void> _onStopRecord(String path) async {
    widget.onStop(path);
  }

  Future<void> _onCancelRecord(String? path) async {
    if (path != null) {
      var file = File(path);
      file.delete();
    }
    widget.onCancel();
  }

  Future<void> _pause() => _audioRecorder.pause();

  Future<void> _resume() => _audioRecorder.resume();

  void _updateRecordState(RecordState recordState) {
    setState(() => _recordState = recordState);

    SchedulerBinding.instance.addPostFrameCallback((_) {
      if (recordState == RecordState.record) {
        _voiceViewKey.currentState?.startRecord();
      } else if (recordState == RecordState.stop) {
        _voiceViewKey.currentState?.stopRecord();
      }
    });

    switch (recordState) {
      case RecordState.pause:
        _timer?.cancel();
        break;
      case RecordState.record:
        _startTimer();
        break;
      case RecordState.stop:
        _timer?.cancel();
        _recordDuration = 0;
        break;
    }
  }

  Future<bool> _isEncoderSupported(AudioEncoder encoder) async {
    final isSupported = await _audioRecorder.isEncoderSupported(
      encoder,
    );

    if (!isSupported) {
      debugPrint('${encoder.name} is not supported on this platform.');
      debugPrint('Supported encoders are:');

      for (final e in AudioEncoder.values) {
        if (await _audioRecorder.isEncoderSupported(e)) {
          debugPrint('- ${e.name}');
        }
      }
    }

    return isSupported;
  }

  Widget _buildRecordControl() {
    final theme = Theme.of(context);
    Icon icon = Icon(Icons.mic, color: theme.primaryColor, size: 80);
    Color color = theme.primaryColor.withValues(alpha: 0.1);

    return Center(
      child: ClipOval(
        child: Material(
            color: color,
            child: SizedBox(width: 100, height: 100, child: icon)),
      ),
    );
  }

  Widget _buildRecordText() {
    var text = context.l10n.pressTalk;
    var isAlertRelease = false;

    if (_recordState != RecordState.stop) {
      if (_dragPosition == DragPosition.top) {
        text = context.l10n.releaseToCancel;
        isAlertRelease = true;
      } else {
        text = context.l10n.holdToRecord;
      }
    }

    if (isAlertRelease) {
      return Center(
          child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
        decoration: BoxDecoration(
          color: Colors.red,
          borderRadius: BorderRadius.circular(4),
        ),
        child: Text(text,
            textAlign: TextAlign.center,
            style: TextStyles.fontSize15Normal.copyWith(color: Colors.white)),
      ));
    }
    return Text(text);
  }

  void _startTimer() {
    _timer?.cancel();

    _timer = Timer.periodic(const Duration(seconds: 1), (Timer t) {
      setState(() => _recordDuration++);
    });
  }
}
