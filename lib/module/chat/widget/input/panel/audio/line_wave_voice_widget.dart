import 'dart:async';
import 'dart:math';
import 'dart:ui';

import 'package:and/l10n/l10n.dart';
import 'package:flutter/material.dart';

class LineWaveVoiceView extends StatefulWidget {
  final Color lineColor;
  final double lineWidth;
  final double textSize;
  final Color textColor;

  const LineWaveVoiceView({
    super.key,
    this.lineColor = const Color(0xffff9c00),
    this.lineWidth = 3,
    this.textSize = 14,
    this.textColor = const Color(0xff666666),
  });

  @override
  State<LineWaveVoiceView> createState() => LineWaveVoiceViewState();
}

class LineWaveVoiceViewState extends State<LineWaveVoiceView> {
  static const _defaultWaveHeights = [2, 3, 4, 3, 2, 2, 2, 2, 2, 2];
  final List<int> _waveHeights = [..._defaultWaveHeights];

  final int minWaveH = 2; //最小的矩形线高，是线宽的2倍，线宽从lineWidth获得
  final int maxWaveH = 7; //最高波峰，是线宽的4倍

  double maxAmplitude = 0;
  Timer? _timer;
  Timer? _countdownTimer;
  int _remainingSeconds = 60;
  String _text = '';

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _updateText();
    });
  }

  @override
  void dispose() {
    _timer?.cancel();
    _countdownTimer?.cancel();
    super.dispose();
  }

  void startRecord() {
    _timer?.cancel();
    _countdownTimer?.cancel();
    _remainingSeconds = 60;
    _updateText();

    _timer = Timer.periodic(const Duration(milliseconds: 100), (timer) {
      _refreshWave();
      setState(() {});
    });

    _countdownTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      _remainingSeconds--;
      if (_remainingSeconds <= 0) {
        stopRecord();
      } else {
        _updateText();
      }
    });
  }

  void stopRecord() {
    _timer?.cancel();
    _countdownTimer?.cancel();
    _waveHeights
      ..clear()
      ..addAll(_defaultWaveHeights);
    maxAmplitude = 0;
    _remainingSeconds = 60;
    _updateText();
  }

  void _updateText() {
    final minutes = _remainingSeconds ~/ 60;
    final seconds = _remainingSeconds % 60;
    var time = "${minutes.toString()}:${seconds.toString().padLeft(2, '0')}";
    setState(() => _text = context.l10n.timeRemaining(time));
  }

  void _refreshWave() {
    // 获取音频振幅
    final maxAmp = maxAmplitude;
    final waveH = minWaveH + (maxAmp * (maxWaveH - 2)).round();
    _waveHeights.insert(0, waveH);
    if (_waveHeights.length > 10) _waveHeights.removeLast();
  }

  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      size: const Size(double.infinity, 50),
      painter: _VoiceWavePainter(
        lineColor: widget.lineColor,
        lineWidth: widget.lineWidth,
        text: _text,
        textSize: widget.textSize,
        textColor: widget.textColor,
        waveHeights: _waveHeights,
      ),
    );
  }
}

class _VoiceWavePainter extends CustomPainter {
  final Color lineColor;
  final double lineWidth;
  final String text;
  final double textSize;
  final Color textColor;
  final List<int> waveHeights;

  _VoiceWavePainter({
    required this.lineColor,
    required this.lineWidth,
    required this.text,
    required this.textSize,
    required this.textColor,
    required this.waveHeights,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    _drawText(canvas, center);
    _drawWaves(canvas, center, size);
  }

  void _drawText(Canvas canvas, Offset center) {
    final textPainter = TextPainter(
      text: TextSpan(
        text: text,
        style: TextStyle(
          color: textColor,
          fontSize: textSize,
        ),
      ),
      textDirection: TextDirection.ltr,
    )..layout();

    final textOffset = Offset(
      center.dx - textPainter.width / 2,
      center.dy - textPainter.height / 2,
    );
    textPainter.paint(canvas, textOffset);
  }

  void _drawWaves(Canvas canvas, Offset center, Size size) {
    final paint = Paint()
      ..color = lineColor
      ..style = PaintingStyle.fill
      ..strokeWidth = lineWidth;

    final textWidth = _measureTextWidth(text, textSize);

    for (var i = 0; i < waveHeights.length; i++) {
      final waveH = waveHeights[i] * lineWidth / 2;
      final left = lineWidth * 2 * i;

      // 右侧波形
      final rightRect = Rect.fromLTRB(
        center.dx + textWidth / 2 + left + lineWidth,
        center.dy - waveH,
        center.dx + textWidth / 2 + left + lineWidth * 2,
        center.dy + waveH,
      );
      canvas.drawRRect(
        RRect.fromRectAndRadius(rightRect, const Radius.circular(6)),
        paint,
      );

      // 左侧波形
      final leftRect = Rect.fromLTRB(
        center.dx - textWidth / 2 - left - lineWidth * 2,
        center.dy - waveH,
        center.dx - textWidth / 2 - left - lineWidth,
        center.dy + waveH,
      );
      canvas.drawRRect(
        RRect.fromRectAndRadius(leftRect, const Radius.circular(6)),
        paint,
      );
    }
  }

  double _measureTextWidth(String text, double fontSize) {
    final textPainter = TextPainter(
      text: TextSpan(
        text: text,
        style: TextStyle(fontSize: fontSize),
      ),
      textDirection: TextDirection.ltr,
    )..layout();
    return textPainter.width;
  }

  @override
  bool shouldRepaint(covariant _VoiceWavePainter oldDelegate) {
    return oldDelegate.text != text || oldDelegate.waveHeights != waveHeights;
  }
}
