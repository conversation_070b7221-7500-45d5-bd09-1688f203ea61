import 'package:and/l10n/l10n.dart';
import 'package:and/utils/image_path.dart';
import 'package:flutter/material.dart';

extension ChatToolTypeExtension on ChatToolType {
  String name(BuildContext context) {
    switch (this) {
      case ChatToolType.gallery:
        return context.l10n.image;
      case ChatToolType.takePhoto:
        return context.l10n.imageTakePhoto;
      case ChatToolType.card:
        return context.l10n.card;
      case ChatToolType.file:
        return context.l10n.file;
    }
  }

  String get icon {
    switch (this) {
      case ChatToolType.gallery:
        return ImagePath.ic_image;
      case ChatToolType.takePhoto:
        return ImagePath.ic_take_photo;
      case ChatToolType.card:
        return ImagePath.ic_card;
      case ChatToolType.file:
        return ImagePath.ic_file;
    }
  }
}

enum ChatToolType {
  gallery,
  takePhoto,
  file,
  card,
}

class ToolPanelWidget extends StatefulWidget {
  final List<ChatToolType> tools;
  final Function(ChatToolType) onTap;
  final double? keyboardHeight;

  const ToolPanelWidget({
    super.key,
    required this.tools,
    required this.onTap,
    this.keyboardHeight,
  });

  @override
  State<ToolPanelWidget> createState() => _ToolPanelWidgetState();
}

class _ToolPanelWidgetState extends State<ToolPanelWidget> {
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      height: widget.keyboardHeight ?? 300,
      child: SafeArea(child: _buildToolsView(context)),
    );
  }

  Widget _buildToolsView(BuildContext context) {
    return GridView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 4,
        mainAxisSpacing: 12,
        crossAxisSpacing: 16,
        childAspectRatio: 0.95, // 略微调整宽高比
      ),
      itemCount: widget.tools.length,
      itemBuilder: (context, index) {
        final tool = widget.tools[index];
        return GestureDetector(
          onTap: () {
            widget.onTap(tool);
          },
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Center(
                  child: Image.asset(
                    tool.icon,
                    color: Colors.black54,
                    width: 28,
                    height: 28,
                  ),
                ),
              ),
              const SizedBox(height: 8),
              Text(
                tool.name(context),
                style: const TextStyle(
                  fontSize: 12,
                  color: Colors.black54,
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
