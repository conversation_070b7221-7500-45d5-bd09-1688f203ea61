import 'package:chat_bottom_container/typedef.dart';
import 'package:flutter/material.dart';

enum PanelType {
  none,
  keyboard,
  emoji,
  voice,
  tool,
}

class PanelConfig {
  final PanelType id;
  final String name;
  final IconData icon;
  final bool readOnly;
  final ChatBottomHandleFocus forceHandleFocus;
  final Widget Function(BuildContext context, double? keyboardHeight) builder;
  final bool Function()? shouldShow;

  const PanelConfig({
    required this.id,
    required this.name,
    required this.icon,
    required this.builder,
    this.readOnly = false,
    this.forceHandleFocus = ChatBottomHandleFocus.none,
    this.shouldShow,
  });
}

class PanelRegistry {
  static final PanelRegistry _instance = PanelRegistry._internal();

  factory PanelRegistry() => _instance;

  PanelRegistry._internal();

  final Map<PanelType, PanelConfig> _panels = {
  };

  void register(PanelConfig config) {
    _panels[config.id] = config;
  }

  void unregister(PanelType id) {
    _panels.remove(id);
  }

  PanelConfig? getPanel(PanelType id) {
    return _panels[id];
  }

  List<PanelConfig> getPanels() {
    return _panels.values.where((panel) {
      return panel.shouldShow?.call() ?? true;
    }).toList();
  }

  void clear() {
    _panels.clear();
  }
}
