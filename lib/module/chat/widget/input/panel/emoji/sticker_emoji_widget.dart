import 'dart:convert';
import 'dart:io';

import 'package:and/cache/cache.dart';
import 'package:and/common/res/colours.dart';
import 'package:and/common/utils/easy_loading_helper.dart';
import 'package:and/http/my_http.dart';
import 'package:and/io/user.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/model/sticker_info.dart';
import 'package:and/utils/common_helper.dart';
import 'package:and/utils/http_utils.dart';
import 'package:and/utils/image_utils.dart';
import 'package:extended_image/extended_image.dart';
import 'package:flutter/material.dart';
import 'package:oktoast/oktoast.dart';

class StickerEmojiWidget extends StatefulWidget {
  final Function(StickerInfo)? onStickerEmojiTap;

  const StickerEmojiWidget({super.key, this.onStickerEmojiTap});

  @override
  State createState() => _StickerEmojiWidgetState();
}

class _StickerEmojiWidgetState extends State<StickerEmojiWidget>
    with SingleTickerProviderStateMixin {
  final int ADD_STICKER_ID = -1;
  var isDeleteMode = false;
  static List<StickerInfo> _stickers = [];
  bool _isLoading = true;
  StickerInfo? _selectedSticker;
  bool _showDeleteConfirm = false;

  // 缓存键
  static const String _STICKER_CACHE_KEY = "user_stickers_cache";

  @override
  void initState() {
    super.initState();
    _loadCachedStickers();
    _loadStickers();
  }

  // 加载缓存的贴纸数据
  Future<void> _loadCachedStickers() async {
    final cachedData = Cache.instance.getString(_STICKER_CACHE_KEY);
    if (cachedData.isNotEmpty) {
      try {
        final List<dynamic> decoded = json.decode(cachedData);
        final List<StickerInfo> stickers =
            decoded.map((item) => StickerInfo.fromJson(item)).toList();
        setState(() {
          _stickers = stickers;
          _isLoading = false;
        });
      } catch (e) {
        // 解析缓存数据出错，忽略
      }
    }
  }

  // 保存贴纸数据到缓存
  Future<void> _saveStickerCache(List<StickerInfo> stickers) async {
    final jsonData = json.encode(stickers.map((e) => e.toJson()).toList());
    await Cache.instance.putString(_STICKER_CACHE_KEY, jsonData);
  }

  Future<void> _loadStickers() async {
    try {
      final stickers = await UserApi(MyHttp.dio).stickers();
      setState(() {
        _stickers = stickers;
        _isLoading = false;
      });
      // 更新缓存
      _saveStickerCache(stickers);
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _uploadSticker() async {
    List<File> files = await ImageUtils.pickImages(context, compress: true);

    // 如果没有选择图片，直接返回，不显示提示
    if (files.isEmpty) {
      return;
    }

    var result = await HttpUtils.uploadSticker(files);
    if (result) {
      await _loadStickers();
    }
  }

  Future<void> _deleteSticker(StickerInfo sticker) async {
    EasyLoadingHelper.show(onAction: () async {
      try {
        await UserApi(MyHttp.dio).deleteSticker(sticker.id.toString());
        showToast(context.l10n.globalDeleteSuccess);
        _stickers.remove(sticker);
        _saveStickerCache(_stickers);
        setState(() {
          _selectedSticker = null;
          _showDeleteConfirm = false;
        });
        // _loadStickers();
      } catch (e) {
        showToast(context.l10n.deleteFailed);
      }
    });
  }

  void _onStickerTap(StickerInfo sticker) {
    widget.onStickerEmojiTap?.call(sticker);
  }

  void _showStickerPopup(StickerInfo sticker) {
    setState(() {
      _selectedSticker = sticker;
      _showDeleteConfirm = false;
    });
  }

  void _onDeleteButtonTap() {
    setState(() {
      _showDeleteConfirm = true;
    });
  }

  void _onConfirmDeleteTap() {
    if (_selectedSticker != null) {
      _deleteSticker(_selectedSticker!);
    }
  }

  void _dismissPopup() {
    setState(() {
      _selectedSticker = null;
      _showDeleteConfirm = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        _buildStickerPanel(),
        if (_selectedSticker != null) _buildStickerPopup(),
      ],
    );
  }

  Widget _buildStickerPopup() {
    return GestureDetector(
      onTap: _dismissPopup,
      child: Container(
        color: Colors.black54,
        width: double.infinity,
        height: double.infinity,
        child: Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 150,
                height: 150,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.2),
                      blurRadius: 10,
                      spreadRadius: 2,
                    ),
                  ],
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: ExtendedImage.network(
                    CommonHelper.getFileUrl(_selectedSticker!.filePath),
                    fit: BoxFit.cover,
                  ),
                ),
              ),
              const SizedBox(height: 16),
              GestureDetector(
                onTap: _showDeleteConfirm
                    ? _onConfirmDeleteTap
                    : _onDeleteButtonTap,
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                  decoration: BoxDecoration(
                    color: _showDeleteConfirm ? Colors.red : Colors.white,
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.2),
                        blurRadius: 5,
                        spreadRadius: 1,
                      ),
                    ],
                  ),
                  child: Text(
                    _showDeleteConfirm
                        ? context.l10n.globalConfirmDelete
                        : context.l10n.globalDelete,
                    style: TextStyle(
                      color: _showDeleteConfirm ? Colors.white : Colors.black,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStickerPanel() {
    var stickers = [];
    stickers.add(StickerInfo(uid: "", filePath: "", id: ADD_STICKER_ID));

    if (_stickers.isNotEmpty) {
      stickers.addAll(_stickers);
    }

    return _isLoading
        ? const Center(child: CircularProgressIndicator())
        : GridView.builder(
            padding: EdgeInsets.all(12),
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 5,
              crossAxisSpacing: 8,
              mainAxisSpacing: 8,
            ),
            itemCount: stickers.length,
            itemBuilder: (context, index) {
              final sticker = stickers[index];
              if (sticker.id == ADD_STICKER_ID) {
                return GestureDetector(
                  onTap: () => _uploadSticker(),
                  child: Container(
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: Colors.black,
                        width: 1,
                        style: BorderStyle.solid,
                      ),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      Icons.add,
                      size: 28,
                      color: Colors.black87,
                    ),
                  ),
                );
              }
              return GestureDetector(
                onTap: () => _onStickerTap(sticker),
                onLongPress: () => _showStickerPopup(sticker),
                child: Container(
                  decoration: BoxDecoration(
                    border: Border.all(color: DColor.greyE7),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Stack(
                    children: [
                      Positioned.fill(
                          child: ExtendedImage.network(
                              shape: BoxShape.rectangle,
                              borderRadius: BorderRadius.circular(8),
                              clipBehavior: Clip.antiAlias,
                              CommonHelper.getFileUrl(sticker.filePath),
                              fit: BoxFit.cover)),
                    ],
                  ),
                ),
              );
            },
          );
  }
}
