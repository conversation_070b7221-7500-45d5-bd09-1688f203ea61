import 'package:and/common/res/colours.dart';
import 'package:and/common/res/text_styles.dart';
import 'package:and/model/sticker_info.dart';
import 'package:and/utils/image_path.dart';
import 'package:emoji_picker_flutter/emoji_picker_flutter.dart';
import 'package:flutter/material.dart';

import 'sticker_emoji_widget.dart';

sealed class EmojiType {
  final Widget icon;

  const EmojiType(this.icon);
}

class TextEmoji extends EmojiType {
  TextEmoji()
      : super(
            Icon(Icons.emoji_emotions_outlined, size: 28, color: Colors.black));
}

class StickerEmoji extends EmojiType {
  StickerEmoji()
      : super(Image.asset(ImagePath.ic_sticker, height: 28, color: Colors.red));
}

class EmojiPanelWidget extends StatefulWidget {
  final double? keyboardHeight;
  final TextEditingController textController;
  final Function()? onEmojiAdded;
  final Function(StickerInfo)? onStickerEmojiTap;

  const EmojiPanelWidget(
      {super.key,
      this.keyboardHeight,
      required this.textController,
      this.onEmojiAdded,
      this.onStickerEmojiTap});

  @override
  State<EmojiPanelWidget> createState() => _EmojiPanelWidgetState();
}

class _EmojiPanelWidgetState extends State<EmojiPanelWidget>
    with SingleTickerProviderStateMixin {
  var emojiTypes = [TextEmoji(), StickerEmoji()];

  late final TabController _tabController = TabController(
    vsync: this,
    length: emojiTypes.length,
  );
  final PageController _pageController = PageController();

  @override
  void initState() {
    super.initState();
    _tabController.addListener(() {
      setState(() {
        _pageController.jumpToPage(_tabController.index);
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      height: widget.keyboardHeight ?? 300,
      child: SafeArea(
          child: Column(
        children: [Expanded(child: _buildPageView()), _buildEmojiTabPanel()],
      )),
    );
  }

  Widget _buildEmojiTabPanel() {
    return TabBar(
      isScrollable: true,
      tabAlignment: TabAlignment.start,
      indicatorColor: DColor.primaryColor,
      indicatorSize: TabBarIndicatorSize.label,
      labelColor: DColor.primaryColor,
      labelStyle: TextStyles.fontSize13Normal,
      unselectedLabelColor: DColor.primaryTextColor,
      unselectedLabelStyle: TextStyles.fontSize13Normal,
      controller: _tabController,
      tabs: emojiTypes.map((status) {
        return _buildTabItem(status);
      }).toList(),
      onTap: (index) {
        _pageController.animateToPage(index,
            duration: kTabScrollDuration, curve: Curves.ease);
      },
    );
  }

  Widget _buildTabItem(EmojiType type) {
    return Tab(
      child: type.icon,
    );
  }

  Widget _buildPageView() {
    return PageView.builder(
      controller: _pageController,
      itemCount: emojiTypes.length,
      physics: const NeverScrollableScrollPhysics(),
      onPageChanged: (index) {
        _tabController.animateTo(index);
      },
      itemBuilder: (context, index) {
        var emojiType = emojiTypes[index];
        if (emojiType is TextEmoji) {
          return _buildEmojiPickerPanel();
        } else {
          return StickerEmojiWidget(
              onStickerEmojiTap: widget.onStickerEmojiTap);
        }
      },
    );
  }

  Widget _buildEmojiPickerPanel() {
    return EmojiPicker(
        config: Config(
            bottomActionBarConfig: BottomActionBarConfig(
                enabled: false, showBackspaceButton: false)),
        onEmojiSelected: (category, emoji) {
          final text = widget.textController.text;
          final selection = widget.textController.selection;
          if (selection.start < 0 || selection.end < 0) {
            setState(() {
              widget.textController.text = text + emoji.emoji;
              widget.textController.selection = TextSelection.collapsed(
                offset: text.length + emoji.emoji.length,
              );
            });
            widget.onEmojiAdded?.call();
            return;
          }
          final newText = text.replaceRange(
            selection.start,
            selection.end,
            emoji.emoji,
          );
          final newSelection = TextSelection.collapsed(
            offset: selection.start + emoji.emoji.length,
          );
          widget.textController.value = TextEditingValue(
            text: newText,
            selection: newSelection,
          );

          widget.onEmojiAdded?.call();
        });
  }
}
