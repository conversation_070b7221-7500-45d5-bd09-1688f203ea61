import 'package:and/utils/image_path.dart';
import 'package:flutter/material.dart';
import 'package:wukongimfluttersdk/entity/msg.dart';

class ReactionEmoji {
  final String emoji;
  final String icon;

  ReactionEmoji({
    required this.emoji,
    required this.icon,
  });
}

List<ReactionEmoji> emojis = [
  ReactionEmoji(emoji: 'like', icon: ImagePath.ic_reaction_like),
  ReactionEmoji(emoji: 'bad', icon: ImagePath.ic_reaction_bad),
  ReactionEmoji(emoji: 'love', icon: ImagePath.ic_reaction_love),
  ReactionEmoji(emoji: 'fire', icon: ImagePath.ic_reaction_fire),
  ReactionEmoji(emoji: 'celebrate', icon: ImagePath.ic_reaction_celebrate),
  ReactionEmoji(emoji: 'happy', icon: ImagePath.ic_reaction_happy),
  ReactionEmoji(emoji: 'haha', icon: ImagePath.ic_reaction_haha),
  ReactionEmoji(emoji: 'terrified', icon: ImagePath.ic_reaction_terrified),
  ReactionEmoji(emoji: 'depressed', icon: ImagePath.ic_reaction_depressed),
  ReactionEmoji(emoji: 'shit', icon: ImagePath.ic_reaction_shit),
  ReactionEmoji(emoji: 'vomit', icon: ImagePath.ic_reaction_vomit),
];

class ReactionActionWidget extends StatefulWidget {
  final WKMsg msg;
  final Function(String) onEmojiTap;

  const ReactionActionWidget(
      {super.key, required this.msg, required this.onEmojiTap});

  @override
  State<StatefulWidget> createState() {
    return _ReactionActionWidgetState();
  }
}

class _ReactionActionWidgetState extends State<ReactionActionWidget> {

  @override
  Widget build(BuildContext context) {
    return Container(
        height: 50,
        padding: const EdgeInsets.symmetric(horizontal: 6),
        margin: const EdgeInsets.only(bottom: 10),
        constraints: const BoxConstraints(maxWidth: 300),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(30),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 10,
              spreadRadius: 2,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                for (var emoji in emojis)
                  InkWell(
                    onTap: () {
                      try {
                        Navigator.pop(context);
                        widget.onEmojiTap(emoji.emoji); // 添加异常捕获
                      } catch (e) {
                        print("Error tapping emoji: $e"); // 打印错误日志
                      }
                    },
                    child: Container(
                      margin: const EdgeInsets.symmetric(horizontal: 10),
                      child: Image.asset(
                        emoji.icon,
                        height: 30,
                      ),
                    ),
                  ),
              ],
            )));
  }
}
