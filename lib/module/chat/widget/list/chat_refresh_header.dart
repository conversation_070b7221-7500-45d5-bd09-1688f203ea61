import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

Header builderChatHeader() {
  return BuilderHeader(
      triggerOffset: 40,
      clamping: false,
      position: IndicatorPosition.above,
      infiniteOffset: null,
      processedDuration: Duration.zero,
      builder: (context, state) {
        return Stack(
          children: [
            SizedBox(
              height: state.offset,
              width: double.infinity,
            ),
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: Container(
                alignment: Alignment.center,
                width: double.infinity,
                height: 40,
                child: SpinKitCircle(
                  size: 24,
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
            )
          ],
        );
      });
}
