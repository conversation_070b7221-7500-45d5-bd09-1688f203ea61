import 'package:and/bloc/navigation/navi_count_bloc.dart';
import 'package:and/module/main/main_tab_page.dart';
import 'package:and/utils/im/im_utils.dart';
import 'package:and/utils/image_utils.dart';
import 'package:get/get.dart';
import 'package:wukongimfluttersdk/wkim.dart';

class MainTabLogic extends GetxController {
  final String key = "main";

  MainTabLogic();

  @override
  void onReady() {
    super.onReady();
    IMUtils.addListener();
  }

  @override
  void onClose() {
    IMUtils.removeListener();
    super.onClose();
  }
}
