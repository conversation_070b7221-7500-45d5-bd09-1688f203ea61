import 'dart:io';

import 'package:and/common/extension/common_ext.dart';
import 'package:and/common/res/colours.dart';
import 'package:and/common/res/text_styles.dart';
import 'package:and/common/utils/easy_loading_helper.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/module/chat/chat_send_utils.dart';
import 'package:and/module/search/globalSearch/global_search_logic.dart';
import 'package:and/module/search/globalSearch/global_search_page.dart';
import 'package:and/utils/download/download_manager.dart';
import 'package:and/utils/fileUtils.dart';
import 'package:and/utils/image_path.dart';
import 'package:and/utils/image_utils.dart';
import 'package:background_downloader/background_downloader.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mime/mime.dart';
import 'package:oktoast/oktoast.dart';
import 'package:wukongimfluttersdk/entity/channel.dart';
import 'package:path/path.dart' as p;
import '../../../app.dart';

enum FileMenu { share, download }

class FileMenuWidget extends StatelessWidget {
  final Uri uri;

  const FileMenuWidget({super.key, required this.uri});

  @override
  Widget build(BuildContext context) {
    return PopupMenuButton<FileMenu>(
      onSelected: handleClick,
      icon: const Icon(Icons.more_horiz),
      itemBuilder: (context) => [
        PopupMenuItem<FileMenu>(
            value: FileMenu.share,
            child: Row(
              children: [
                Image.asset(ImagePath.ic_msg_share,
                    height: 25,
                    width: 25,
                    color: DColor.primaryTextColor,
                    fit: BoxFit.fitHeight),
                // const Icon(Icons.refresh),
                const SizedBox(
                  width: 15,
                ),
                Text(context.l10n.shareToConversation,
                    style: TextStyles.fontSize16Normal)
              ],
            )),
        PopupMenuItem<FileMenu>(
            value: FileMenu.download,
            child: Row(
              children: [
                Image.asset(ImagePath.ic_msg_download,
                    height: 25,
                    width: 25,
                    color: DColor.primaryTextColor,
                    fit: BoxFit.fitHeight),
                // const Icon(Icons.refresh),
                const SizedBox(
                  width: 15,
                ),
                Text(context.l10n.saveFile, style: TextStyles.fontSize16Normal)
              ],
            )),
      ],
    );
  }

  void handleClick(FileMenu item) async {
    var actionUri = uri;
    if (uri.isHttpUri) {
      await EasyLoadingHelper.show(onAction: () async {
        var file = await downloadFile(uri);
        if (file != null) {
          actionUri = Uri.file(file.path);
        }
      });
    }

    switch (item) {
      case FileMenu.share:
        shareFile(actionUri);
        break;
      case FileMenu.download:
        saveFile(actionUri);
        break;
    }
  }

  void shareFile(Uri uri) async {
    var context = globalContext;
    if (context == null) return;
    await GlobalSearchPage.open(
        isMultiSelect: true,
        searchType: GlobalSearchType.recentConversation,
        title: context.l10n.chooseContact,
        onSelectedChannel: (channels) async {
          for (var channel in channels) {
            shareToConversation(channel, uri);
          }

          showToast(context.l10n.shareSuccess);

          Get.back();
        });
  }

  void shareToConversation(WKChannel channel, Uri uri) async {
    ChatSendUtils sendUtils = ChatSendUtils(
        channelID: channel.channelID, channelType: channel.channelType);
    if (uri.isFileUri) {
      File file = File.fromUri(uri);
      final mimeType = lookupMimeType(file.path);
      if (mimeType != null &&
          (mimeType.startsWith("image") || mimeType.startsWith("video"))) {
        sendUtils.sendMediaMessage(file);
      } else {
        sendUtils.sendFileMessage(file);
      }
    }
  }

  void saveFile(Uri uri) async {
    if (uri.isFileUri) {
      File file = File.fromUri(uri);
      if (FileUtils.isMediaFile(file)) {
        ImageUtils.saveToAlbum(file);
      } else {
        var saveDir = await FileUtils.getSaveDirectory();
        var path = p.join(saveDir.path, file.name);
        var saveFile = File(path);
        if (!saveFile.parent.existsSync()) {
          await saveFile.parent.create(recursive: true);
        }
        var result = await file.copy(path);
        if (result.existsSync()) {
          showToast(globalContext!.l10n.fileSaved(path));
        }
      }
    }
  }

  bool isMediaUri(Uri uri) {
    if (uri.isFileUri) {
      File file = File.fromUri(uri);
      return FileUtils.isMediaFile(file);
    }
    return false;
  }

  Future<File?> downloadFile(Uri uri) async {
    DownloadManager downloadManager = DownloadManager();

    var downloadPath = p.join(
        FileUtils.getChatDownloadDirectory().path, uri.pathSegments.last);
    File file = File(downloadPath);
    if (file.existsSync()) {
      return file;
    }
    await downloadManager.download(uri.toString(), localPath: downloadPath);

    if (file.existsSync()) {
      return file;
    }
    return null;
  }
}
