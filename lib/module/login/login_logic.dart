import 'package:and/l10n/language_utils.dart';
import 'package:country_codes/country_codes.dart';
import 'package:get/get.dart';

enum LoginType { phone, email }

class LoginLogic extends GetxController {
  final areaCode = "0086".obs;
  final loginType = LoginType.phone.obs;

  String get areaCodeDisplay => "+${areaCode.value.substring(2)}";

  @override
  void onReady() async{
    super.onReady();
    await CountryCodes.init();
    final CountryDetails details = CountryCodes.detailsForLocale(LanguageUtils.getDeviceLocale());
    var dialCode = details.dialCode;
    if (dialCode != null) {
      areaCode.value = dialCode.replaceAll("+", "00");
    }
  }

  void setAreaCode(String? code) {
    if (code == null) return;
    areaCode.value = code;
  }
}
