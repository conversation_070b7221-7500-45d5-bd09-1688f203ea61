import 'package:and/common/extension/common_ext.dart';
import 'package:and/common/res/colours.dart';
import 'package:and/common/res/text_styles.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/module/forget/pwd_forget_page.dart';
import 'package:and/router/router.dart';
import 'package:and/utils/http_utils.dart';
import 'package:and/utils/image_path.dart';
import 'package:and/widget/input_form_widget.dart';
import 'package:and/widget/submit_button.dart';
import 'package:flutter/material.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:get/get.dart';

import 'login_logic.dart';

class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  State<StatefulWidget> createState() {
    return _LoginPageState();
  }
}

class _LoginPageState extends State<LoginPage>
    with SingleTickerProviderStateMixin {
  final logic = Get.findOrPut<LoginLogic>(() => LoginLogic());
  late var loginType = logic.loginType;

  final _emailTextController = TextEditingController();
  final _phoneTextController = TextEditingController();
  final _pwdTextController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  final GlobalKey<FormState> _emailKey = GlobalKey<FormState>();
  final GlobalKey<FormState> _phoneKey = GlobalKey<FormState>();
  final GlobalKey<FormState> _pwdKey = GlobalKey<FormState>();
  late TabController _tabController;

  @override
  void initState() {
    super.initState();

    _tabController = TabController(length: 2, vsync: this);
    _tabController.addListener(() {
      if (_tabController.index == 0) {
        logic.loginType.value = LoginType.phone;
      } else {
        logic.loginType.value = LoginType.email;
      }
    });

    _tabController.index = logic.loginType.value == LoginType.phone ? 0 : 1;
  }

  @override
  void dispose() {
    // Clean up the controller when the widget is disposed.
    _phoneTextController.dispose();
    _emailTextController.dispose();
    _pwdTextController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Container(
        color: Colors.white,
        padding: const EdgeInsets.all(24),
        child: Obx(() => _buildContent()),
      ),
    );
  }

  Widget _buildContent() {
    return SafeArea(
        child: SizedBox.expand(
      child: SingleChildScrollView(
        child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Center(
                  child: Image.asset(ImagePath.ic_logo,
                      width: 120, height: 120, fit: BoxFit.contain),
                ),
                const SizedBox(height: 32),
                Text(
                  context.l10n.loginTitle,
                  style: const TextStyle(
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  context.l10n.loginSubTitle,
                  style: const TextStyle(
                    fontSize: 16,
                    color: Colors.black54,
                  ),
                ),
                const SizedBox(height: 24),
                _buildLoginSwitch(),
                const SizedBox(height: 24),
                AnimatedCrossFade(
                  duration: const Duration(milliseconds: 300),
                  crossFadeState: loginType.value == LoginType.phone
                      ? CrossFadeState.showFirst
                      : CrossFadeState.showSecond,
                  firstChild: _buildPhone(),
                  secondChild: _buildEmail(),
                ),
                const SizedBox(height: 16),
                _buildPassword(),
                const SizedBox(height: 12),
                Align(
                  alignment: Alignment.centerRight,
                  child: _buildMore(),
                ),
                const SizedBox(height: 24),
                _buildLoginButton(),
                const SizedBox(height: 24),
                _buildDivider(),
                const SizedBox(height: 24),
                _buildRegister(),
              ],
            )),
      ),
    ));
  }

  Widget _buildLoginSwitch() {
    return Container(
      height: 40,
      padding: EdgeInsets.symmetric(horizontal: 0, vertical: 0), // 适当缩小 padding
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(12),
      ),
      child: TabBar(
        controller: _tabController,
        tabs: [
          Tab(text: context.l10n.loginByPhone),
          Tab(text: context.l10n.loginByEmail),
        ],
        indicator: BoxDecoration(
          color: DColor.primaryColor,
          borderRadius: BorderRadius.circular(12),
        ),
        indicatorSize: TabBarIndicatorSize.tab,
        indicatorPadding: EdgeInsets.symmetric(horizontal: 0, vertical: 0),
        // 缩小 indicator
        labelPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 4),
        // 缩小 labelPadding
        labelColor: Colors.white,
        unselectedLabelColor: Colors.black54,
      ),
    );
  }

  Widget _buildDivider() {
    return Row(
      children: [
        const Expanded(child: Divider()),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Text(
            context.l10n.loginOr,
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 14,
            ),
          ),
        ),
        const Expanded(child: Divider()),
      ],
    );
  }

  Widget _buildRegister() {
    return OutlinedButton(
      onPressed: () {
        Get.toNamed(RouteGet.signup);
      },
      style: OutlinedButton.styleFrom(
        side: const BorderSide(color: DColor.primaryColor),
        padding: const EdgeInsets.symmetric(vertical: 16),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(24),
        ),
      ),
      child: Text(
        context.l10n.register_new_account,
        style: const TextStyle(
          color: DColor.primaryColor,
          fontSize: 16,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildMore() {
    return Row(
      children: [
        // InkWell(
        //   onTap: () {
        //     Get.toNamed(RouteGet.signup);
        //   },
        //   child: Text(context.l10n.signupByEmail,
        //       style: TextStyles.fontSize15Normal
        //           .copyWith(color: DColor.primaryColor)),
        // ),
        const Spacer(),

        InkWell(
          onTap: () {
            Get.toNamed(RouteGet.forgotPwd, arguments: PwdForgetArgument());
          },
          child: Text(context.l10n.loginForgot,
              style: TextStyles.fontSize15Normal
                  .copyWith(color: DColor.primaryColor)),
        ),
      ],
    );
  }

  Widget _buildLoginButton() {
    return SubmitButton(
        enable: true,
        text: context.l10n.loginLoginButton,
        textColor: Colors.white,
        backgroundColor: DColor.primaryColor,
        textSize: 20,
        onPressed: () async {
          var allValidList = [
            if (logic.loginType.value == LoginType.email)
              _emailKey
            else
              _phoneKey,
            _pwdKey,
          ];

          var isValid = allValidList
              .every((element) => element.currentState?.validate() ?? false);
          if (isValid) {
            await HttpUtils.login(
                logic.loginType.value,
                (logic.loginType.value == LoginType.email)
                    ? _emailTextController.text
                    : _phoneTextController.text,
                _pwdTextController.text,
                areaCode: logic.areaCode.value);
          }
        });
  }

  Widget _buildEmail() {
    return InputTextForm(
        formKey: _emailKey,
        validator: FormBuilderValidators.email(
            errorText: context.l10n.signupEmailErrorFormat),
        hint: context.l10n.signupEmailPlaceholder,
        value: "",
        keyboardType: TextInputType.emailAddress,
        controller: _emailTextController,
        onChanged: (value) {
          setState(() {});
        });
  }

  Widget _buildPhone() {
    return InputTextForm(
        formKey: _phoneKey,
        validator: FormBuilderValidators.phoneNumber(
            errorText: context.l10n.signupPhoneErrorFormat),
        hint: context.l10n.signupPhonePlaceholder,
        value: "",
        keyboardType: TextInputType.phone,
        controller: _phoneTextController,
        prefixIcon: InkWell(
          onTap: () async {
            var code = await Get.toNamed(RouteGet.chooseCountry);
            logic.setAreaCode(code);
          },
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 8),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(logic.areaCodeDisplay),
                Icon(Icons.arrow_drop_down)
              ],
            ),
          ),
        ),
        onChanged: (value) {
          setState(() {});
        });
  }

  Widget _buildPassword() {
    return InputTextForm(
        formKey: _pwdKey,
        validator: FormBuilderValidators.required(
            errorText: context.l10n.signupPasswordErrorRequired),
        hint: context.l10n.signupPasswordPlaceholder,
        value: "",
        obscureText: true,
        keyboardType: TextInputType.text,
        controller: _pwdTextController,
        onChanged: (value) {
          setState(() {});
        });
  }
}
