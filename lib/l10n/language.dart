enum Language {
  fsLan(title: "跟随系统语言", countryCode: "fs-Lan"),
  enUS(title: "English", countryCode: "en-US"),
  zhCN(title: "简体中文", countryCode: "zh-CN"),
  zhTW(title: "繁體中文", countryCode: "zh-TW"),
  jaJP(title: "日本語", countryCode: "ja-JP");

  final String title;
  final String countryCode;

  const Language({required this.title, required this.countryCode});

  static Language getLanguage(String countryCode) {
    return Language.values.firstWhere(
        (element) => element.countryCode == countryCode,
        orElse: () => Language.fsLan);
  }
}
