import 'package:json_annotation/json_annotation.dart';

part 'invite_code.g.dart';

@JsonSerializable()
class InviteInfo {
  @Json<PERSON>ey(name: 'invite_code', defaultValue: '')
  final String inviteCode;
  @Json<PERSON>ey(name: 'invite_url', defaultValue: '')
  final String inviteUrl;
  @<PERSON>son<PERSON><PERSON>(name: 'modifiable_time', defaultValue: 0)
  final int modifiableTime;


  const InviteInfo({
    required this.inviteCode,
    required this.inviteUrl,
    required this.modifiableTime,
  });

  factory InviteInfo.fromJson(Map<String, dynamic> json) =>
      _$InviteInfoFromJson(json);

  Map<String, dynamic> toJson() => _$InviteInfoToJson(this);
}
