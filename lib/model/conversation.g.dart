// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'conversation.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Conversation _$ConversationFromJson(Map<String, dynamic> json) => Conversation(
      channelId: json['channel_id'] as String? ?? '',
      channelType: (json['channel_type'] as num?)?.toInt() ?? 0,
      unread: (json['unread'] as num?)?.toInt() ?? 0,
      stick: (json['stick'] as num?)?.toInt() ?? 0,
      mute: (json['mute'] as num?)?.toInt() ?? 0,
      timestamp: (json['timestamp'] as num?)?.toInt() ?? 0,
      lastMsgSeq: (json['last_msg_seq'] as num?)?.toInt() ?? 0,
      lastClientMsgNo: json['last_client_msg_no'] as String? ?? '',
      offsetMsgSeq: (json['offset_msg_seq'] as num?)?.toInt() ?? 0,
      version: (json['version'] as num?)?.toInt() ?? 0,
      recents: (json['recents'] as List<dynamic>?)
              ?.map((e) => Message.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      users: (json['users'] as List<dynamic>?)
              ?.map((e) => UserInfo.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      groups: (json['groups'] as List<dynamic>?)
              ?.map((e) => Group.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      extra: json['extra'] == null
          ? null
          : ConversationExtra.fromJson(json['extra'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$ConversationToJson(Conversation instance) =>
    <String, dynamic>{
      'channel_id': instance.channelId,
      'channel_type': instance.channelType,
      'unread': instance.unread,
      'stick': instance.stick,
      'mute': instance.mute,
      'timestamp': instance.timestamp,
      'last_msg_seq': instance.lastMsgSeq,
      'last_client_msg_no': instance.lastClientMsgNo,
      'offset_msg_seq': instance.offsetMsgSeq,
      'version': instance.version,
      'recents': instance.recents,
      'users': instance.users,
      'groups': instance.groups,
      'extra': instance.extra,
    };
