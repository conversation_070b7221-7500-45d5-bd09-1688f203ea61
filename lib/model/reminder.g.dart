// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'reminder.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Reminder _$ReminderFromJson(Map<String, dynamic> json) => Reminder(
      id: (json['id'] as num?)?.toInt() ?? 0,
      channelId: json['channel_id'] as String? ?? '',
      channelType: (json['channel_type'] as num?)?.toInt() ?? 0,
      publisher: json['publisher'] as String? ?? '',
      messageSeq: (json['message_seq'] as num?)?.toInt() ?? 0,
      messageId: json['message_id'] as String? ?? '',
      reminderType: (json['reminder_type'] as num?)?.toInt() ?? 0,
      uid: json['uid'] as String? ?? '',
      text: json['text'] as String? ?? '',
      data: json['data'],
      isLocate: (json['is_locate'] as num?)?.toInt() ?? 0,
      version: (json['version'] as num?)?.toInt() ?? 0,
      done: (json['done'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$ReminderToJson(Reminder instance) => <String, dynamic>{
      'id': instance.id,
      'channel_id': instance.channelId,
      'channel_type': instance.channelType,
      'publisher': instance.publisher,
      'message_seq': instance.messageSeq,
      'message_id': instance.messageId,
      'reminder_type': instance.reminderType,
      'uid': instance.uid,
      'text': instance.text,
      'data': instance.data,
      'is_locate': instance.isLocate,
      'version': instance.version,
      'done': instance.done,
    };
