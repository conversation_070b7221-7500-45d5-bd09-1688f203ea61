import 'package:json_annotation/json_annotation.dart';

part 'reminder.g.dart';

@JsonSerializable()
class Reminder {
  @Json<PERSON>ey(defaultValue: 0)
  final int id;
  @Json<PERSON>ey(name: 'channel_id', defaultValue: '')
  final String channelId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'channel_type', defaultValue: 0)
  final int channelType;
  @<PERSON>son<PERSON>ey(defaultValue: '')
  final String publisher;
  @JsonKey(name: 'message_seq', defaultValue: 0)
  final int messageSeq;
  @<PERSON>son<PERSON>ey(name: 'message_id', defaultValue: '')
  final String messageId;
  @<PERSON>son<PERSON><PERSON>(name: 'reminder_type', defaultValue: 0)
  final int reminderType;
  @<PERSON>son<PERSON><PERSON>(defaultValue: '')
  final String uid;
  @Json<PERSON>ey(defaultValue: '')
  final String text;
  final dynamic data;
  @<PERSON>son<PERSON><PERSON>(name: 'is_locate', defaultValue: 0)
  final int isLocate;
  @<PERSON>son<PERSON>ey(defaultValue: 0)
  final int version;
  @<PERSON><PERSON><PERSON><PERSON>(defaultValue: 0)
  final int done;

  const Reminder({
    required this.id,
    required this.channelId,
    required this.channelType,
    required this.publisher,
    required this.messageSeq,
    required this.messageId,
    required this.reminderType,
    required this.uid,
    required this.text,
    this.data,
    required this.isLocate,
    required this.version,
    required this.done,
  });

  factory Reminder.fromJson(Map<String, dynamic> json) =>
      _$ReminderFromJson(json);

  Map<String, dynamic> toJson() => _$ReminderToJson(this);
}
