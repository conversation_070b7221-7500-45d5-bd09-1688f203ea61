import 'package:json_annotation/json_annotation.dart';

part 'pinned_status_message.g.dart';

@JsonSerializable()
class PinnedStatusMessage {
  @J<PERSON><PERSON>ey(name: 'message_id', defaultValue: '')
  final String messageId;
  @<PERSON>son<PERSON><PERSON>(name: 'message_seq', defaultValue: 0)
  final int messageSeq;
  @Json<PERSON>ey(name: 'channel_id', defaultValue: '')
  final String channelId;
  @<PERSON>sonKey(name: 'channel_type', defaultValue: 0)
  final int channelType;
  @<PERSON>son<PERSON>ey(name: 'is_deleted', defaultValue: 0)
  final int isDeleted;
  @<PERSON>son<PERSON><PERSON>(name: 'version', defaultValue: 0)
  final int version;
  @Json<PERSON>ey(name: 'created_by', defaultValue: '')
  final String createdBy;
  @Json<PERSON>ey(name: 'updated_at', defaultValue: '')
  final String updatedAt;
  @JsonKey(name: 'created_at', defaultValue: '')
  final String createdAt;

  const PinnedStatusMessage({
    required this.messageId,
    required this.messageSeq,
    required this.channelId,
    required this.channelType,
    required this.isDeleted,
    required this.version,
    required this.createdBy,
    required this.updatedAt,
    required this.createdAt,
  });

  factory PinnedStatusMessage.fromJson(Map<String, dynamic> json) =>
      _$PinnedStatusMessageFromJson(json);

  Map<String, dynamic> toJson() => _$PinnedStatusMessageToJson(this);

}