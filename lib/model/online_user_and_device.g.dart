// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'online_user_and_device.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

OnlineUserAndDevice _$OnlineUserAndDeviceFromJson(Map<String, dynamic> json) =>
    OnlineUserAndDevice(
      pc: json['pc'] == null
          ? null
          : PcOnline.fromJson(json['pc'] as Map<String, dynamic>),
      friends: (json['friends'] as List<dynamic>?)
              ?.map((e) => FriendOnline.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
    );

Map<String, dynamic> _$OnlineUserAndDeviceToJson(
        OnlineUserAndDevice instance) =>
    <String, dynamic>{
      'pc': instance.pc,
      'friends': instance.friends,
    };

PcOnline _$PcOnlineFromJson(Map<String, dynamic> json) => PcOnline(
      online: (json['online'] as num?)?.toInt() ?? 0,
      deviceFlag: (json['device_flag'] as num?)?.toInt() ?? 0,
      muteOfApp: (json['mute_of_app'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$PcOnlineToJson(PcOnline instance) => <String, dynamic>{
      'online': instance.online,
      'device_flag': instance.deviceFlag,
      'mute_of_app': instance.muteOfApp,
    };

FriendOnline _$FriendOnlineFromJson(Map<String, dynamic> json) => FriendOnline(
      uid: json['uid'] as String? ?? '',
      deviceFlag: (json['device_flag'] as num?)?.toInt() ?? 0,
      lastOffline: (json['last_offline'] as num?)?.toInt() ?? 0,
      online: (json['online'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$FriendOnlineToJson(FriendOnline instance) =>
    <String, dynamic>{
      'uid': instance.uid,
      'device_flag': instance.deviceFlag,
      'last_offline': instance.lastOffline,
      'online': instance.online,
    };
