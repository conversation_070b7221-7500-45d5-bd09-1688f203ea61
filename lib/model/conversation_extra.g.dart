// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'conversation_extra.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ConversationExtra _$ConversationExtraFromJson(Map<String, dynamic> json) =>
    ConversationExtra(
      channelId: json['channel_id'] as String? ?? '',
      channelType: (json['channel_type'] as num?)?.toInt() ?? 0,
      browseTo: (json['browse_to'] as num?)?.toInt() ?? 0,
      keepMessageSeq: (json['keep_message_seq'] as num?)?.toInt() ?? 0,
      keepOffsetY: (json['draft_updated_at'] as num?)?.toInt() ?? 0,
      draftUpdatedAt: (json['keep_offset_y'] as num?)?.toInt() ?? 0,
      draft: json['draft'] as String? ?? '',
      version: (json['version'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$ConversationExtraToJson(ConversationExtra instance) =>
    <String, dynamic>{
      'channel_id': instance.channelId,
      'channel_type': instance.channelType,
      'browse_to': instance.browseTo,
      'keep_message_seq': instance.keepMessageSeq,
      'keep_offset_y': instance.draftUpdatedAt,
      'draft_updated_at': instance.keepOffsetY,
      'draft': instance.draft,
      'version': instance.version,
    };
