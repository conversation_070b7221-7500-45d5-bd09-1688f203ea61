import 'package:json_annotation/json_annotation.dart';

part 'conversation_extra.g.dart';

@JsonSerializable()
class ConversationExtra {
  @Json<PERSON>ey(name: 'channel_id', defaultValue: '')
  final String channelId;
  @Json<PERSON>ey(name: 'channel_type', defaultValue: 0)
  final int channelType;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'browse_to', defaultValue: 0)
  final int browseTo;
  @<PERSON>sonKey(name: 'keep_message_seq', defaultValue: 0)
  final int keepMessageSeq;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'keep_offset_y', defaultValue: 0)
  final int draftUpdatedAt;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'draft_updated_at', defaultValue: 0)
  final int keepOffsetY;
  @Json<PERSON>ey(defaultValue: '')
  final String draft;
  @JsonKey(defaultValue: 0)
  final int version;

  const ConversationExtra({
    required this.channelId,
    required this.channelType,
    required this.browseTo,
    required this.keepMessageSeq,
    required this.keepOffsetY,
    required this.draftUpdatedAt,
    required this.draft,
    required this.version,
  });

  factory ConversationExtra.fromJson(Map<String, dynamic> json) =>
      _$ConversationExtraFromJson(json);

  Map<String, dynamic> toJson() => _$ConversationExtraToJson(this);
}
