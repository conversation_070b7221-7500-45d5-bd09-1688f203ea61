import 'package:and/model/message.dart';
import 'package:json_annotation/json_annotation.dart';

import 'conversation_extra.dart';
import 'group.dart';
import 'user_info.dart';

part 'conversation.g.dart';

@JsonSerializable()
class Conversation {
  @Json<PERSON><PERSON>(name: 'channel_id', defaultValue: '')
  final String channelId;
  @<PERSON>son<PERSON>ey(name: 'channel_type', defaultValue: 0)
  final int channelType;
  @<PERSON>sonKey(defaultValue: 0)
  final int unread;
  @<PERSON>son<PERSON>ey(defaultValue: 0)
  final int stick;
  @<PERSON><PERSON><PERSON><PERSON>(defaultValue: 0)
  final int mute;
  @Json<PERSON>ey(defaultValue: 0)
  final int timestamp;
  @JsonKey(name: 'last_msg_seq', defaultValue: 0)
  final int lastMsgSeq;
  @JsonKey(name: 'last_client_msg_no', defaultValue: '')
  final String lastClientMsgNo;
  @<PERSON>sonKey(name: 'offset_msg_seq', defaultValue: 0)
  final int offsetMsgSeq;
  @<PERSON><PERSON><PERSON><PERSON>(defaultValue: 0)
  final int version;
  @Json<PERSON>ey(defaultValue: [])
  final List<Message> recents;
  @<PERSON>son<PERSON><PERSON>(defaultValue: [])
  final List<UserInfo> users;
  @JsonKey(defaultValue: [])
  final List<Group> groups;
  final ConversationExtra? extra;

  const Conversation({
    required this.channelId,
    required this.channelType,
    required this.unread,
    required this.stick,
    required this.mute,
    required this.timestamp,
    required this.lastMsgSeq,
    required this.lastClientMsgNo,
    required this.offsetMsgSeq,
    required this.version,
    required this.recents,
    required this.users,
    required this.groups,
    this.extra
  });

  factory Conversation.fromJson(Map<String, dynamic> json) =>
      _$ConversationFromJson(json);

  Map<String, dynamic> toJson() => _$ConversationToJson(this);
}
