import 'package:json_annotation/json_annotation.dart';

part 'scan_result.g.dart';

@JsonSerializable()
class ScanResult {
  @JsonKey(defaultValue: '')
  final String forward;
  @JsonKey(defaultValue: '')
  final String type;
  final Map? data;

  const ScanResult({
    required this.forward,
    required this.type,
    this.data,
  });

  factory ScanResult.fromJson(Map<String, dynamic> json) =>
      _$ScanResultFromJson(json);

  Map<String, dynamic> toJson() => _$ScanResultToJson(this);
}
