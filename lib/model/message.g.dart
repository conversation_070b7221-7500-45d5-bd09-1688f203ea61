// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'message.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Message _$MessageFromJson(Map<String, dynamic> json) => Message(
      messageId: (json['message_id'] as num?)?.toInt() ?? 0,
      messageSeq: (json['message_seq'] as num?)?.toInt() ?? 0,
      channelId: json['channel_id'] as String? ?? '',
      channelType: (json['channel_type'] as num?)?.toInt() ?? 0,
      clientMsgNo: json['client_msg_no'] as String? ?? '',
      fromUid: json['from_uid'] as String? ?? '',
      timestamp: (json['timestamp'] as num?)?.toInt() ?? 0,
      voiceStatus: (json['voice_status'] as num?)?.toInt() ?? 0,
      isDeleted: (json['is_deleted'] as num?)?.toInt() ?? 0,
      voiceRecognizeResult: json['voice_recognize_result'] as String?,
      revoke: (json['revoke'] as num?)?.toInt() ?? 0,
      revoker: json['revoker'] as String? ?? '',
      extraVersion: (json['extra_version'] as num?)?.toInt() ?? 0,
      unreadCount: (json['unread_count'] as num?)?.toInt() ?? 0,
      readedCount: (json['readed_count'] as num?)?.toInt() ?? 0,
      readed: (json['readed'] as num?)?.toInt() ?? 0,
      receipt: (json['receipt'] as num?)?.toInt() ?? 0,
      setting: (json['setting'] as num?)?.toInt() ?? 0,
      payload: json['payload'],
      signalPayload: json['signal_payload'] as String? ?? '',
      messageExtra: json['message_extra'] == null
          ? null
          : MessageExtra.fromJson(
              json['message_extra'] as Map<String, dynamic>),
      reactions: (json['reactions'] as List<dynamic>?)
              ?.map((e) => SyncReaction.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
    );

Map<String, dynamic> _$MessageToJson(Message instance) => <String, dynamic>{
      'message_id': instance.messageId,
      'message_seq': instance.messageSeq,
      'channel_id': instance.channelId,
      'channel_type': instance.channelType,
      'client_msg_no': instance.clientMsgNo,
      'from_uid': instance.fromUid,
      'timestamp': instance.timestamp,
      'voice_status': instance.voiceStatus,
      'is_deleted': instance.isDeleted,
      'voice_recognize_result': instance.voiceRecognizeResult,
      'revoke': instance.revoke,
      'revoker': instance.revoker,
      'extra_version': instance.extraVersion,
      'unread_count': instance.unreadCount,
      'readed_count': instance.readedCount,
      'readed': instance.readed,
      'receipt': instance.receipt,
      'setting': instance.setting,
      'payload': instance.payload,
      'signal_payload': instance.signalPayload,
      'message_extra': instance.messageExtra,
      'reactions': instance.reactions,
    };
