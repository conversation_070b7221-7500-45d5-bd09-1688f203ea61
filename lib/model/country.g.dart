// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'country.dart';

// **************************************************************************
// CopyWithGenerator
// **************************************************************************

abstract class _$CountryCWProxy {
  Country code(String code);

  Country icon(String icon);

  Country name(String name);

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `Country(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// Country(...).copyWith(id: 12, name: "My name")
  /// ````
  Country call({
    String code,
    String icon,
    String name,
  });
}

/// Proxy class for `copyWith` functionality. This is a callable class and can be used as follows: `instanceOfCountry.copyWith(...)`. Additionally contains functions for specific fields e.g. `instanceOfCountry.copyWith.fieldName(...)`
class _$CountryCWProxyImpl implements _$CountryCWProxy {
  const _$CountryCWProxyImpl(this._value);

  final Country _value;

  @override
  Country code(String code) => this(code: code);

  @override
  Country icon(String icon) => this(icon: icon);

  @override
  Country name(String name) => this(name: name);

  @override

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `Country(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// Country(...).copyWith(id: 12, name: "My name")
  /// ````
  Country call({
    Object? code = const $CopyWithPlaceholder(),
    Object? icon = const $CopyWithPlaceholder(),
    Object? name = const $CopyWithPlaceholder(),
  }) {
    return Country(
      code: code == const $CopyWithPlaceholder()
          ? _value.code
          // ignore: cast_nullable_to_non_nullable
          : code as String,
      icon: icon == const $CopyWithPlaceholder()
          ? _value.icon
          // ignore: cast_nullable_to_non_nullable
          : icon as String,
      name: name == const $CopyWithPlaceholder()
          ? _value.name
          // ignore: cast_nullable_to_non_nullable
          : name as String,
    );
  }
}

extension $CountryCopyWith on Country {
  /// Returns a callable class that can be used as follows: `instanceOfCountry.copyWith(...)` or like so:`instanceOfCountry.copyWith.fieldName(...)`.
  // ignore: library_private_types_in_public_api
  _$CountryCWProxy get copyWith => _$CountryCWProxyImpl(this);
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Country _$CountryFromJson(Map<String, dynamic> json) => Country(
      code: json['code'] as String? ?? '',
      icon: json['icon'] as String? ?? '',
      name: json['name'] as String? ?? '',
    );

Map<String, dynamic> _$CountryToJson(Country instance) => <String, dynamic>{
      'code': instance.code,
      'icon': instance.icon,
      'name': instance.name,
    };
