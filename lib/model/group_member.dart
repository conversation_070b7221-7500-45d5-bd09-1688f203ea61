import 'package:json_annotation/json_annotation.dart';

part 'group_member.g.dart';

@JsonSerializable()
class GroupMember {
  @J<PERSON><PERSON><PERSON>(defaultValue: '')
  final String uid;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'group_no', defaultValue: '')
  final String groupNo;
  @<PERSON><PERSON><PERSON><PERSON>(defaultValue: '')
  final String name;
  @<PERSON>son<PERSON><PERSON>(defaultValue: '')
  final String remark;
  @<PERSON>son<PERSON>ey(defaultValue: 0)
  final int role;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'anonymous_avatar', defaultValue: 0)
  final int anonymousAvatar;
  @<PERSON><PERSON><PERSON>ey(defaultValue: 0)
  final int version;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'is_deleted', defaultValue: 0)
  final int isDeleted;
  @<PERSON>son<PERSON><PERSON>(defaultValue: 0)
  final int status;
  @Json<PERSON>ey(defaultValue: '')
  final String vercode;
  @<PERSON>son<PERSON><PERSON>(name: 'invite_uid', defaultValue: '')
  final String inviteUid;
  @<PERSON><PERSON><PERSON><PERSON>(defaultValue: 0)
  final int robot;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'forbidden_expir_time', defaultValue: 0)
  final int forbiddenExpirTime;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'updated_at', defaultValue: '')
  final String updatedAt;
  @<PERSON>son<PERSON><PERSON>(name: 'created_at', defaultValue: '')
  final String createdAt;

  const GroupMember({
    required this.uid,
    required this.groupNo,
    required this.name,
    required this.remark,
    required this.role,
    required this.version,
    required this.isDeleted,
    required this.status,
    required this.vercode,
    required this.inviteUid,
    required this.robot,
    required this.anonymousAvatar,
    required this.forbiddenExpirTime,
    required this.updatedAt,
    required this.createdAt,
  });

  factory GroupMember.fromJson(Map<String, dynamic> json) =>
      _$GroupMemberFromJson(json);

  Map<String, dynamic> toJson() => _$GroupMemberToJson(this);
}
