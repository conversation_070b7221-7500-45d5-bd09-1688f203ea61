import 'package:json_annotation/json_annotation.dart';

part 'device.g.dart';

@JsonSerializable()
class Device {
  @JsonKey(name: 'device_id', defaultValue: '')
  final String deviceId;
  @Json<PERSON>ey(name: 'device_name', defaultValue: '')
  final String deviceName;
  @JsonKey(name: 'device_model', defaultValue: '')
  final String deviceModel;

  const Device({
    required this.deviceId,
    required this.deviceName,
    required this.deviceModel,
  });

  factory Device.fromJson(Map<String, dynamic> json) =>
      _$DeviceFromJson(json);

  Map<String, dynamic> toJson() => _$DeviceToJson(this);
}
