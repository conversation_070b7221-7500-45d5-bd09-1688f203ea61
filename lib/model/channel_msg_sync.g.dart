// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'channel_msg_sync.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ChannelMsgSync _$ChannelMsgSyncFromJson(Map<String, dynamic> json) =>
    ChannelMsgSync(
      startMessageSeq: (json['start_message_seq'] as num?)?.toInt() ?? 0,
      endMessageSeq: (json['end_message_seq'] as num?)?.toInt() ?? 0,
      pullMode: (json['pull_mode'] as num?)?.toInt() ?? 0,
      more: (json['more'] as num?)?.toInt() ?? 0,
      messages: (json['messages'] as List<dynamic>?)
              ?.map((e) => Message.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
    );

Map<String, dynamic> _$ChannelMsgSyncToJson(ChannelMsgSync instance) =>
    <String, dynamic>{
      'start_message_seq': instance.startMessageSeq,
      'end_message_seq': instance.endMessageSeq,
      'pull_mode': instance.pullMode,
      'more': instance.more,
      'messages': instance.messages,
    };
