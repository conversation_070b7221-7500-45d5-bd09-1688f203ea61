import 'package:json_annotation/json_annotation.dart';

part 'online_user_and_device.g.dart';

@JsonSerializable()
class OnlineUserAndDevice {
  final PcOnline? pc;
  @JsonKey(defaultValue: [])
  final List<FriendOnline> friends;

  const OnlineUserAndDevice({
    this.pc,
    required this.friends,
  });

  factory OnlineUserAndDevice.fromJson(Map<String, dynamic> json) =>
      _$OnlineUserAndDeviceFromJson(json);

  Map<String, dynamic> toJson() => _$OnlineUserAndDeviceToJson(this);
}

@JsonSerializable()
class PcOnline {
  @JsonKey(defaultValue: 0)
  final int online;
  @JsonKey(name: 'device_flag', defaultValue: 0)
  final int deviceFlag;
  @JsonKey(name: 'mute_of_app', defaultValue: 0)
  final int muteOfApp;

  const PcOnline({
    required this.online,
    required this.deviceFlag,
    required this.muteOfApp,
  });

  factory PcOnline.fromJson(Map<String, dynamic> json) =>
      _$PcOnlineFromJson(json);

  Map<String, dynamic> toJson() => _$PcOnlineToJson(this);
}

@JsonSerializable()
class FriendOnline {
  @JsonKey(defaultValue: '')
  final String uid;
  @JsonKey(name: 'device_flag', defaultValue: 0)
  final int deviceFlag;
  @JsonKey(name: 'last_offline', defaultValue: 0)
  final int lastOffline;
  @JsonKey(defaultValue: 0)
  final int online;

  const FriendOnline({
    required this.uid,
    required this.deviceFlag,
    required this.lastOffline,
    required this.online,
  });

  factory FriendOnline.fromJson(Map<String, dynamic> json) =>
      _$FriendOnlineFromJson(json);

  Map<String, dynamic> toJson() => _$FriendOnlineToJson(this);
}
