import 'package:json_annotation/json_annotation.dart';

part 'sync_reaction.g.dart';

@JsonSerializable()
class SyncReaction {
  @Json<PERSON>ey(name: 'message_id', defaultValue: '')
  final String messageId;
  @Json<PERSON>ey(defaultValue: '')
  final String uid;
  @<PERSON><PERSON><PERSON><PERSON>(defaultValue: '')
  final String name;
  @Json<PERSON><PERSON>(name: 'channel_id', defaultValue: '')
  final String channelId;
  @<PERSON>sonKey(name: 'channel_type', defaultValue: 0)
  final int channelType;
  @<PERSON>son<PERSON><PERSON>(defaultValue: 0)
  final int seq;
  @Json<PERSON>ey(defaultValue: '')
  final String emoji;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'is_deleted', defaultValue: 0)
  final int isDeleted;
  @<PERSON>son<PERSON><PERSON>(name: 'created_at', defaultValue: '')
  final String createdAt;

  const SyncReaction({
    required this.messageId,
    required this.uid,
    required this.name,
    required this.channelId,
    required this.channelType,
    required this.seq,
    required this.emoji,
    required this.isDeleted,
    required this.createdAt,
  });

  factory SyncReaction.fromJson(Map<String, dynamic> json) =>
      _$SyncReactionFromJson(json);

  Map<String, dynamic> toJson() => _$SyncReactionToJson(this);
}
