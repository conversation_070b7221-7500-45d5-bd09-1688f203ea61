// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'conversation_sync.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ConversationSync _$ConversationSyncFromJson(Map<String, dynamic> json) =>
    ConversationSync(
      uid: json['uid'] as String? ?? '',
      conversations: (json['conversations'] as List<dynamic>?)
              ?.map((e) => Conversation.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      users: (json['users'] as List<dynamic>?)
              ?.map((e) => UserInfo.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      groups: (json['groups'] as List<dynamic>?)
              ?.map((e) => Group.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
    );

Map<String, dynamic> _$ConversationSyncToJson(ConversationSync instance) =>
    <String, dynamic>{
      'uid': instance.uid,
      'conversations': instance.conversations,
      'users': instance.users,
      'groups': instance.groups,
    };
