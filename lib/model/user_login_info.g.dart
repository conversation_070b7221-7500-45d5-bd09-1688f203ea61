// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_login_info.dart';

// **************************************************************************
// CopyWithGenerator
// **************************************************************************

abstract class _$UserLoginInfoCWProxy {
  UserLoginInfo uid(String? uid);

  UserLoginInfo appId(String? appId);

  UserLoginInfo name(String? name);

  UserLoginInfo username(String? username);

  UserLoginInfo sex(int? sex);

  UserLoginInfo email(String? email);

  UserLoginInfo category(String? category);

  UserLoginInfo shortNo(String? shortNo);

  UserLoginInfo zone(String? zone);

  UserLoginInfo phone(String? phone);

  UserLoginInfo token(String? token);

  UserLoginInfo chatPwd(String? chatPwd);

  UserLoginInfo lockScreenPwd(String? lockScreenPwd);

  UserLoginInfo lockAfterMinute(int? lockAfterMinute);

  UserLoginInfo setting(Setting? setting);

  UserLoginInfo rsaPublicKey(String? rsaPublicKey);

  UserLoginInfo shortStatus(int? shortStatus);

  UserLoginInfo msgExpireSecond(int? msgExpireSecond);

  UserLoginInfo inviteCode(String? inviteCode);

  UserLoginInfo signature(String? signature);

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `UserLoginInfo(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// UserLoginInfo(...).copyWith(id: 12, name: "My name")
  /// ````
  UserLoginInfo call({
    String? uid,
    String? appId,
    String? name,
    String? username,
    int? sex,
    String? email,
    String? category,
    String? shortNo,
    String? zone,
    String? phone,
    String? token,
    String? chatPwd,
    String? lockScreenPwd,
    int? lockAfterMinute,
    Setting? setting,
    String? rsaPublicKey,
    int? shortStatus,
    int? msgExpireSecond,
    String? inviteCode,
    String? signature,
  });
}

/// Proxy class for `copyWith` functionality. This is a callable class and can be used as follows: `instanceOfUserLoginInfo.copyWith(...)`. Additionally contains functions for specific fields e.g. `instanceOfUserLoginInfo.copyWith.fieldName(...)`
class _$UserLoginInfoCWProxyImpl implements _$UserLoginInfoCWProxy {
  const _$UserLoginInfoCWProxyImpl(this._value);

  final UserLoginInfo _value;

  @override
  UserLoginInfo uid(String? uid) => this(uid: uid);

  @override
  UserLoginInfo appId(String? appId) => this(appId: appId);

  @override
  UserLoginInfo name(String? name) => this(name: name);

  @override
  UserLoginInfo username(String? username) => this(username: username);

  @override
  UserLoginInfo sex(int? sex) => this(sex: sex);

  @override
  UserLoginInfo email(String? email) => this(email: email);

  @override
  UserLoginInfo category(String? category) => this(category: category);

  @override
  UserLoginInfo shortNo(String? shortNo) => this(shortNo: shortNo);

  @override
  UserLoginInfo zone(String? zone) => this(zone: zone);

  @override
  UserLoginInfo phone(String? phone) => this(phone: phone);

  @override
  UserLoginInfo token(String? token) => this(token: token);

  @override
  UserLoginInfo chatPwd(String? chatPwd) => this(chatPwd: chatPwd);

  @override
  UserLoginInfo lockScreenPwd(String? lockScreenPwd) =>
      this(lockScreenPwd: lockScreenPwd);

  @override
  UserLoginInfo lockAfterMinute(int? lockAfterMinute) =>
      this(lockAfterMinute: lockAfterMinute);

  @override
  UserLoginInfo setting(Setting? setting) => this(setting: setting);

  @override
  UserLoginInfo rsaPublicKey(String? rsaPublicKey) =>
      this(rsaPublicKey: rsaPublicKey);

  @override
  UserLoginInfo shortStatus(int? shortStatus) => this(shortStatus: shortStatus);

  @override
  UserLoginInfo msgExpireSecond(int? msgExpireSecond) =>
      this(msgExpireSecond: msgExpireSecond);

  @override
  UserLoginInfo inviteCode(String? inviteCode) => this(inviteCode: inviteCode);

  @override
  UserLoginInfo signature(String? signature) => this(signature: signature);

  @override

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `UserLoginInfo(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// UserLoginInfo(...).copyWith(id: 12, name: "My name")
  /// ````
  UserLoginInfo call({
    Object? uid = const $CopyWithPlaceholder(),
    Object? appId = const $CopyWithPlaceholder(),
    Object? name = const $CopyWithPlaceholder(),
    Object? username = const $CopyWithPlaceholder(),
    Object? sex = const $CopyWithPlaceholder(),
    Object? email = const $CopyWithPlaceholder(),
    Object? category = const $CopyWithPlaceholder(),
    Object? shortNo = const $CopyWithPlaceholder(),
    Object? zone = const $CopyWithPlaceholder(),
    Object? phone = const $CopyWithPlaceholder(),
    Object? token = const $CopyWithPlaceholder(),
    Object? chatPwd = const $CopyWithPlaceholder(),
    Object? lockScreenPwd = const $CopyWithPlaceholder(),
    Object? lockAfterMinute = const $CopyWithPlaceholder(),
    Object? setting = const $CopyWithPlaceholder(),
    Object? rsaPublicKey = const $CopyWithPlaceholder(),
    Object? shortStatus = const $CopyWithPlaceholder(),
    Object? msgExpireSecond = const $CopyWithPlaceholder(),
    Object? inviteCode = const $CopyWithPlaceholder(),
    Object? signature = const $CopyWithPlaceholder(),
  }) {
    return UserLoginInfo(
      uid: uid == const $CopyWithPlaceholder()
          ? _value.uid
          // ignore: cast_nullable_to_non_nullable
          : uid as String?,
      appId: appId == const $CopyWithPlaceholder()
          ? _value.appId
          // ignore: cast_nullable_to_non_nullable
          : appId as String?,
      name: name == const $CopyWithPlaceholder()
          ? _value.name
          // ignore: cast_nullable_to_non_nullable
          : name as String?,
      username: username == const $CopyWithPlaceholder()
          ? _value.username
          // ignore: cast_nullable_to_non_nullable
          : username as String?,
      sex: sex == const $CopyWithPlaceholder()
          ? _value.sex
          // ignore: cast_nullable_to_non_nullable
          : sex as int?,
      email: email == const $CopyWithPlaceholder()
          ? _value.email
          // ignore: cast_nullable_to_non_nullable
          : email as String?,
      category: category == const $CopyWithPlaceholder()
          ? _value.category
          // ignore: cast_nullable_to_non_nullable
          : category as String?,
      shortNo: shortNo == const $CopyWithPlaceholder()
          ? _value.shortNo
          // ignore: cast_nullable_to_non_nullable
          : shortNo as String?,
      zone: zone == const $CopyWithPlaceholder()
          ? _value.zone
          // ignore: cast_nullable_to_non_nullable
          : zone as String?,
      phone: phone == const $CopyWithPlaceholder()
          ? _value.phone
          // ignore: cast_nullable_to_non_nullable
          : phone as String?,
      token: token == const $CopyWithPlaceholder()
          ? _value.token
          // ignore: cast_nullable_to_non_nullable
          : token as String?,
      chatPwd: chatPwd == const $CopyWithPlaceholder()
          ? _value.chatPwd
          // ignore: cast_nullable_to_non_nullable
          : chatPwd as String?,
      lockScreenPwd: lockScreenPwd == const $CopyWithPlaceholder()
          ? _value.lockScreenPwd
          // ignore: cast_nullable_to_non_nullable
          : lockScreenPwd as String?,
      lockAfterMinute: lockAfterMinute == const $CopyWithPlaceholder()
          ? _value.lockAfterMinute
          // ignore: cast_nullable_to_non_nullable
          : lockAfterMinute as int?,
      setting: setting == const $CopyWithPlaceholder()
          ? _value.setting
          // ignore: cast_nullable_to_non_nullable
          : setting as Setting?,
      rsaPublicKey: rsaPublicKey == const $CopyWithPlaceholder()
          ? _value.rsaPublicKey
          // ignore: cast_nullable_to_non_nullable
          : rsaPublicKey as String?,
      shortStatus: shortStatus == const $CopyWithPlaceholder()
          ? _value.shortStatus
          // ignore: cast_nullable_to_non_nullable
          : shortStatus as int?,
      msgExpireSecond: msgExpireSecond == const $CopyWithPlaceholder()
          ? _value.msgExpireSecond
          // ignore: cast_nullable_to_non_nullable
          : msgExpireSecond as int?,
      inviteCode: inviteCode == const $CopyWithPlaceholder()
          ? _value.inviteCode
          // ignore: cast_nullable_to_non_nullable
          : inviteCode as String?,
      signature: signature == const $CopyWithPlaceholder()
          ? _value.signature
          // ignore: cast_nullable_to_non_nullable
          : signature as String?,
    );
  }
}

extension $UserLoginInfoCopyWith on UserLoginInfo {
  /// Returns a callable class that can be used as follows: `instanceOfUserLoginInfo.copyWith(...)` or like so:`instanceOfUserLoginInfo.copyWith.fieldName(...)`.
  // ignore: library_private_types_in_public_api
  _$UserLoginInfoCWProxy get copyWith => _$UserLoginInfoCWProxyImpl(this);
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UserLoginInfo _$UserLoginInfoFromJson(Map<String, dynamic> json) =>
    UserLoginInfo(
      uid: json['uid'] as String?,
      appId: json['app_id'] as String?,
      name: json['name'] as String?,
      username: json['username'] as String?,
      sex: (json['sex'] as num?)?.toInt(),
      email: json['email'] as String?,
      category: json['category'] as String?,
      shortNo: json['short_no'] as String?,
      zone: json['zone'] as String?,
      phone: json['phone'] as String?,
      token: json['token'] as String?,
      chatPwd: json['chat_pwd'] as String?,
      lockScreenPwd: json['lock_screen_pwd'] as String?,
      lockAfterMinute: (json['lock_after_minute'] as num?)?.toInt(),
      setting: json['setting'] == null
          ? null
          : Setting.fromJson(json['setting'] as Map<String, dynamic>),
      rsaPublicKey: json['rsa_public_key'] as String?,
      shortStatus: (json['short_status'] as num?)?.toInt(),
      msgExpireSecond: (json['msg_expire_second'] as num?)?.toInt(),
      inviteCode: json['invite_code'] as String?,
      signature: json['signature'] as String? ?? '',
    );

Map<String, dynamic> _$UserLoginInfoToJson(UserLoginInfo instance) =>
    <String, dynamic>{
      'uid': instance.uid,
      'app_id': instance.appId,
      'name': instance.name,
      'username': instance.username,
      'sex': instance.sex,
      'email': instance.email,
      'category': instance.category,
      'short_no': instance.shortNo,
      'zone': instance.zone,
      'phone': instance.phone,
      'token': instance.token,
      'chat_pwd': instance.chatPwd,
      'lock_screen_pwd': instance.lockScreenPwd,
      'lock_after_minute': instance.lockAfterMinute,
      'setting': instance.setting,
      'rsa_public_key': instance.rsaPublicKey,
      'short_status': instance.shortStatus,
      'msg_expire_second': instance.msgExpireSecond,
      'invite_code': instance.inviteCode,
      'signature': instance.signature,
    };

Setting _$SettingFromJson(Map<String, dynamic> json) => Setting(
      searchByPhone: (json['search_by_phone'] as num?)?.toInt() ?? 1,
      searchByShort: (json['search_by_short'] as num?)?.toInt() ?? 1,
      newMsgNotice: (json['new_msg_notice'] as num?)?.toInt() ?? 1,
      msgShowDetail: (json['msg_show_detail'] as num?)?.toInt() ?? 1,
      voiceOn: (json['voice_on'] as num?)?.toInt(),
      shockOn: (json['shock_on'] as num?)?.toInt() ?? 1,
      offlineProtection: (json['offline_protection'] as num?)?.toInt() ?? 1,
      deviceLock: (json['device_lock'] as num?)?.toInt(),
      muteOfApp: (json['mute_of_app'] as num?)?.toInt(),
    );

Map<String, dynamic> _$SettingToJson(Setting instance) => <String, dynamic>{
      'search_by_phone': instance.searchByPhone,
      'search_by_short': instance.searchByShort,
      'new_msg_notice': instance.newMsgNotice,
      'msg_show_detail': instance.msgShowDetail,
      'voice_on': instance.voiceOn,
      'shock_on': instance.shockOn,
      'offline_protection': instance.offlineProtection,
      'device_lock': instance.deviceLock,
      'mute_of_app': instance.muteOfApp,
    };
