import 'package:wukongimfluttersdk/manager/message_manager.dart';
import 'package:wukongimfluttersdk/model/wk_message_content.dart';
import 'package:wukongimfluttersdk/type/const.dart';

class WKUnknownContent extends WKMessageContent {
  WKUnknownContent() {
    contentType = WkMessageContentType.unknown;
  }

  @override
  String displayText() {
    return WKMessageManager.shared.messageTextProvider
            ?.displayText(contentType) ??
        '[未知消息]';
  }
}
