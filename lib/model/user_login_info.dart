import 'package:copy_with_extension/copy_with_extension.dart';
import 'package:json_annotation/json_annotation.dart';

part 'user_login_info.g.dart';

@JsonSerializable()
@CopyWith()
class UserLoginInfo {
  final String? uid;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'app_id')
  final String? appId;
  final String? name;
  final String? username;
  final int? sex;
  final String? email;
  final String? category;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'short_no')
  final String? shortNo;
  final String? zone;
  final String? phone;
  final String? token;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'chat_pwd')
  final String? chatPwd;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'lock_screen_pwd')
  final String? lockScreenPwd;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'lock_after_minute')
  final int? lockAfterMinute;
  final Setting? setting;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'rsa_public_key')
  final String? rsaPublicKey;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'short_status')
  final int? shortStatus;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'msg_expire_second')
  final int? msgExpireSecond;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'invite_code')
  final String? inviteCode;
  @<PERSON><PERSON><PERSON><PERSON>(defaultValue: '')
  final String? signature;

  const UserLoginInfo({
    this.uid,
    this.appId,
    this.name,
    this.username,
    this.sex,
    this.email,
    this.category,
    this.shortNo,
    this.zone,
    this.phone,
    this.token,
    this.chatPwd,
    this.lockScreenPwd,
    this.lockAfterMinute,
    this.setting,
    this.rsaPublicKey,
    this.shortStatus,
    this.msgExpireSecond,
    this.inviteCode,
    this.signature,
  });

  factory UserLoginInfo.fromJson(Map<String, dynamic> json) =>
      _$UserLoginInfoFromJson(json);

  Map<String, dynamic> toJson() => _$UserLoginInfoToJson(this);
}

@JsonSerializable()
class Setting {
  @JsonKey(name: 'search_by_phone', defaultValue: 1)
  final int? searchByPhone;
  @JsonKey(name: 'search_by_short', defaultValue: 1)
  final int? searchByShort;
  @JsonKey(name: 'new_msg_notice', defaultValue: 1)
  final int? newMsgNotice;
  @JsonKey(name: 'msg_show_detail', defaultValue: 1)
  final int? msgShowDetail;
  @JsonKey(name: 'voice_on')
  final int? voiceOn;
  @JsonKey(name: 'shock_on', defaultValue: 1)
  final int? shockOn;
  @JsonKey(name: 'offline_protection', defaultValue: 1)
  final int? offlineProtection;
  @JsonKey(name: 'device_lock')
  final int? deviceLock;
  @JsonKey(name: 'mute_of_app')
  final int? muteOfApp;

  const Setting({
    this.searchByPhone,
    this.searchByShort,
    this.newMsgNotice,
    this.msgShowDetail,
    this.voiceOn,
    this.shockOn,
    this.offlineProtection,
    this.deviceLock,
    this.muteOfApp,
  });

  factory Setting.fromJson(Map<String, dynamic> json) =>
      _$SettingFromJson(json);

  Map<String, dynamic> toJson() => _$SettingToJson(this);
}
