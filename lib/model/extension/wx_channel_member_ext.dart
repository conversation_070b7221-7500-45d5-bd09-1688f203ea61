
import 'package:and/constant/wk_channel_member_extras.dart';
import 'package:wukongimfluttersdk/db/const.dart';
import 'package:wukongimfluttersdk/entity/channel_member.dart';

extension WKChannelMemberExtension on WKChannelMember {

  String get displayName {
    if (memberRemark.isNotEmpty) {
      return memberRemark.replaceAll("\n", "");
    }
    return memberName.replaceAll("\n", "");
  }

  String? get vercode {
    if (extraMap == null) {
      return null;
    }
    return WKDBConst.readString(extraMap, WKChannelMemberExtras.WKCode);
  }

  String? get anonymousAvatar {
    if (extraMap == null) {
      return null;
    }
    return WKDBConst.readString(extraMap, WKChannelMemberExtras.WKAnonymousAvatar);
  }
}
