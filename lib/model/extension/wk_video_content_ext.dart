import 'dart:io';

import 'package:and/model/extension/wk_media_content_ext.dart';
import 'package:and/utils/common_helper.dart';
import 'package:and/utils/fileUtils.dart';
import 'package:wukongimfluttersdk/model/wk_video_content.dart';

extension WKVideoContentExtension on WKVideoContent {
  String get coverUrl {
    return CommonHelper.getFileUrl(cover);
  }

  String get videoUrl {
    return CommonHelper.getFileUrl(url);
  }

  Uri get coverUri {
    File localCoverFile = File.fromUri(localCoverUri);
    if (localCoverFile.existsSync()) {
      return localCoverUri;
    }

    return Uri.parse(coverUrl);
  }

  Uri get videoUri {
    File localFile = File.fromUri(localUri);
    if (localFile.existsSync()) {
      return localUri;
    }

    return Uri.parse(videoUrl);
  }

  Uri get localCoverUri {
    if (coverLocalPath.isEmpty) {
      return Uri();
    }
    return Uri.file(FileUtils.getFullStoragePath(coverLocalPath));
  }
}
