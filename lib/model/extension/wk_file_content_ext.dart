import 'package:and/model/content/wk_file_content.dart';
import 'package:and/utils/common_helper.dart';

extension WKFileContentExtension on WKFileContent {
  String get fileUrl {
    return CommonHelper.getFileUrl(url);
  }

  String get fileName {
    if (name.isNotEmpty) {
      return name;
    }
    if (localPath.isNotEmpty) {
      return localPath.split('/').last;
    }
    return url.split('/').last;
  }
}
