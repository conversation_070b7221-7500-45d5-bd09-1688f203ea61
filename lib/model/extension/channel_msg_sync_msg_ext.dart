import 'package:and/model/channel_msg_sync.dart';
import 'package:and/model/extension/message_ext.dart';
import 'package:wukongimfluttersdk/entity/msg.dart';

extension ChannelMsgSyncExtension on ChannelMsgSync {
  WKSyncChannelMsg toWKSyncChannelMsg() {
    WKSyncChannelMsg msg = WKSyncChannelMsg();
    msg.startMessageSeq = startMessageSeq;
    msg.endMessageSeq = endMessageSeq;
    msg.more = more;

    msg.messages = messages.map((e) => e.toWKSyncMsg()).toList();

    return msg;
  }
}
