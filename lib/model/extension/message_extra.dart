import 'package:and/model/message_extra.dart';
import 'package:wukongimfluttersdk/entity/msg.dart';

extension MessageExtraExtension on MessageExtra {
  WKSyncExtraMsg toWKSyncExtraMsg() {
    var extra = WKSyncExtraMsg();
    extra.messageID = messageId;
    extra.messageIdStr = messageIdStr;
    extra.revoke = revoke;
    extra.revoker = revoker;
    extra.extraVersion = extraVersion;
    extra.voiceStatus = voiceStatus;
    extra.isMutualDeleted = isMutualDeleted;
    extra.extraVersion = extraVersion;
    // extra.unreadCount = unreadCount;
    extra.readedCount = readedCount;
    extra.readed = readed;
    extra.contentEdit = contentEdit;
    extra.editedAt = editedAt;

    return extra;
  }

  WKMsgExtra toWKMsgExtra(String channelId, int channelType) {
    WKMsgExtra extra = WKMsgExtra();
    extra.channelID = channelId;
    extra.channelType = channelType;
    extra.messageID = messageIdStr;
    extra.revoke = revoke;
    extra.revoker = revoker;
    extra.readed = readed;
    extra.readedCount = readedCount;
    extra.isMutualDeleted = isMutualDeleted;
    extra.extraVersion = extraVersion;
    extra.editedAt = editedAt;
    if (contentEdit != null) {
      extra.contentEdit = contentEdit;
    }
    extra.isPinned = isPinned;
    return extra;
  }
}
