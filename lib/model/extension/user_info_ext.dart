
import 'package:and/constant/wk_channel_extras.dart';
import 'package:and/model/user_info.dart';
import 'package:wukongimfluttersdk/entity/channel.dart';
import 'package:wukongimfluttersdk/type/const.dart';

extension UserInfoExtension on UserInfo {
  WKChannel toWKChannel() {
    var channel = WKChannel(uid, WKChannelType.personal);
    channel.channelRemark = remark;
    channel.channelName = name;
    channel.mute = mute;
    channel.top = top;
    channel.version = version;
    channel.status = status;
    channel.isDeleted = isDeleted;
    channel.updatedAt = updatedAt;
    channel.createdAt = createdAt;
    channel.receipt = receipt;
    channel.robot = robot;
    channel.category = category;
    channel.follow = follow;
    channel.online = online;
    channel.lastOffline = lastOffline;
    channel.deviceFlag = deviceFlag;
    channel.localExtra = {
      WKChannelExtras.beDeleted: beDeleted,
      WKChannelExtras.beBlacklist: beBlacklist,
    };
    // 添加 remoteExtraMap
    channel.remoteExtraMap = {
      WKChannelExtras.revokeRemind: revokeRemind,
      WKChannelExtras.screenshot: screenshot,
      WKChannelExtras.sourceDesc: sourceDesc,
      WKChannelExtras.chatPwdOn: chatPwdOn,
      WKChannelExtras.vercode: vercode,
      WKChannelExtras.signature: signature,
    };

    return channel;
  }
}
