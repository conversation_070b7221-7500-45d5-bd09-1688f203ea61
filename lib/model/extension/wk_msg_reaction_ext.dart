import 'package:wukongimfluttersdk/entity/msg.dart';

extension WKMsgReactionExtension on WKMsgReaction {
  WKSyncMsgReaction toWKSyncMsgReaction() {
    WKSyncMsgReaction msg = WKSyncMsgReaction();
    msg.messageID = messageID;
    msg.uid = uid;
    msg.name = name;
    msg.channelID = channelID;
    msg.channelType = channelType;
    msg.seq = seq;
    msg.emoji = emoji;
    msg.isDeleted = isDeleted;
    msg.createdAt = createdAt;
    return msg;
  }
}
