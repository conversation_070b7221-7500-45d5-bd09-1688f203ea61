import 'package:and/constant/wk_channel_extras.dart';
import 'package:and/model/channel_info.dart';
import 'package:wukongimfluttersdk/entity/channel.dart';
import 'package:wukongimfluttersdk/type/const.dart';

extension ChannelInfoExtension on ChannelInfo {
  WKChannel toWKChannel() {
    var wkChannel = WKChannel(channel?.channelId ?? '',
        channel?.channelType ?? WKChannelType.personal);
    wkChannel.channelRemark = remark;
    wkChannel.channelName = name;
    wkChannel.mute = mute;
    wkChannel.top = stick;
    wkChannel.status = status;
    wkChannel.receipt = receipt;
    wkChannel.robot = robot;
    wkChannel.category = category;
    wkChannel.follow = follow;
    wkChannel.anonymous = anonymous;
    wkChannel.online = online;
    wkChannel.lastOffline = lastOffline;
    wkChannel.save = save;
    wkChannel.showNick = showNick;
    wkChannel.forbidden = forbidden;
    wkChannel.invite = invite;
    wkChannel.deviceFlag = deviceFlag;
    wkChannel.localExtra = {};
    if (parentChannel != null) {
      wkChannel.parentChannelID = parentChannel?.channelId ?? '';
      wkChannel.parentChannelType =
          parentChannel?.channelType ?? WKChannelType.personal;
    }
    // 添加 remoteExtraMap
    var remoteExtra = {};
    remoteExtra.addAll(extra);
    remoteExtra.putIfAbsent(WKChannelExtras.signature, () => signature);
    wkChannel.remoteExtraMap = remoteExtra;
    wkChannel.localExtra = {
      WKChannelExtras.beDeleted: beDeleted,
      WKChannelExtras.beBlacklist: beBlacklist,
      WKChannelExtras.notice: notice
    };

    return wkChannel;
  }
}
