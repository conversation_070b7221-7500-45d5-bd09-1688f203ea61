import 'dart:io';

import 'package:and/model/app_version.dart';

import 'package:path/path.dart' as p;
import 'package:path_provider/path_provider.dart';

extension AppVersionExtension on AppVersion {
  String get fileName {
    return "gleezy_$appVersion.apk";
  }

  Future<File> get apkFile async {
    Directory dir = await getApplicationSupportDirectory();
    String savePath = p.join(dir.path, "ota_update", fileName);
    return File(savePath);
  }

  Future deleteApkFile() async {
    File file = await apkFile;
    file.delete();
  }
}
