import 'package:and/model/conversation_extra.dart';
import 'package:wukongimfluttersdk/entity/conversation.dart';

extension ConversationExtraSyncExtension on ConversationExtra {
  WKConversationMsgExtra toWKConversationMsgExtra() {
    WKConversationMsgExtra msg = WKConversationMsgExtra();
    msg.channelID = channelId;
    msg.channelType = channelType;
    msg.draft = draft;
    msg.keepOffsetY = keepOffsetY;
    msg.keepMessageSeq = keepMessageSeq;
    msg.version = version;
    msg.browseTo = browseTo;
    msg.draftUpdatedAt = draftUpdatedAt;
    return msg;
  }
}
