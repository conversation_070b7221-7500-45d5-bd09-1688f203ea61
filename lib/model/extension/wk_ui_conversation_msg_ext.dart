import 'package:and/app.dart';
import 'package:and/constant/wk_system_account.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/model/extension/wk_channel_ext.dart';
import 'package:and/utils/common_helper.dart';
import 'package:wukongimfluttersdk/db/const.dart';
import 'package:wukongimfluttersdk/entity/channel.dart';
import 'package:wukongimfluttersdk/entity/conversation.dart';
import 'package:wukongimfluttersdk/entity/reminder.dart';
import 'package:wukongimfluttersdk/type/const.dart';

extension WKUIConversationMsgListExtension on List<WKUIConversationMsg> {
  Future<void> sortMsg() async {
    // 获取每个消息对应的 WKChannel 实例，并存储到 Map 中
    Map<WKUIConversationMsg, WKChannel?> channels = {
      for (var msg in this) msg: await msg.getWkChannel(),
    };
    sort((a, b) {
      var channelA = channels[a]; // 获取 a 的 WKChannel
      var channelB = channels[b]; // 获取 b 的 WKChannel

      // 获取 top 字段进行排序，若没有 top 字段，默认为 0
      var topA = channelA?.top ?? 0;
      var topB = channelB?.top ?? 0;

      // 如果 top 字段不同，则优先排序 top 字段
      if (topA != topB) {
        return topB.compareTo(topA); // top 值较大的排在前面
      } else {
        // 如果 top 字段相同，则根据最后消息时间戳排序
        var isSame = a.lastMsgTimestamp == b.lastMsgTimestamp;
        if (isSame) {
          return b.channelID.compareTo(a.channelID);
        }
        return b.lastMsgTimestamp.compareTo(a.lastMsgTimestamp);
      }
    });
  }
}
