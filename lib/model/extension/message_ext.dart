import 'package:and/model/extension/message_extra.dart';
import 'package:and/model/extension/payload_ext.dart';
import 'package:and/model/extension/sync_reaction_ext.dart';
import 'package:and/model/message.dart';
import 'package:wukongimfluttersdk/entity/msg.dart';

extension MessageExtension on Message {
  WKSyncMsg toWKSyncMsg() {
    WKSyncMsg syncMsg = WKSyncMsg();
    syncMsg.channelID = channelId;
    syncMsg.messageID = messageId.toString();
    syncMsg.channelType = channelType;
    syncMsg.clientMsgNO = clientMsgNo;
    syncMsg.messageSeq = messageSeq;
    syncMsg.fromUID = fromUid;
    syncMsg.isDeleted = isDeleted;
    syncMsg.timestamp = timestamp;
    syncMsg.setting = setting;
    syncMsg.receipt = receipt;
    syncMsg.revoke = revoke;
    syncMsg.revoker = revoker;
    syncMsg.extraVersion = extraVersion;
    syncMsg.unreadCount = unreadCount;
    syncMsg.readedCount = readedCount;
    syncMsg.readed = readed;
    syncMsg.voiceStatus = voiceStatus;
    syncMsg.reactions = reactions.map((e) => e.toWKSyncMsgReaction()).toList();

    try {
      if (payload is Map<String, dynamic>) {
        (payload as Map<String, dynamic>).fixPayload();
      }
      syncMsg.payload = payload;
      // msg.payload = jsonDecode(utf8.decode(base64Decode(payload)));
    } catch (e) {
      print('异常了');
    }
    // 解析扩展
    syncMsg.messageExtra = messageExtra?.toWKSyncExtraMsg();
    return syncMsg;
  }

  void fixPayload(Map<String, dynamic> payload) {
    // 解析 JSON 字符串为 Map
    if (payload['timeTrad'] is String) {
      payload['timeTrad'] = int.tryParse(payload['timeTrad']) ?? 0;
    }
  }
}
