import 'package:and/constant/group_status.dart';
import 'package:and/constant/wk_channel_extras.dart';
import 'package:and/model/channel_info.dart';
import 'package:and/model/reminder.dart';
import 'package:and/utils/common_helper.dart';
import 'package:wukongimfluttersdk/entity/channel.dart';
import 'package:wukongimfluttersdk/entity/reminder.dart';
import 'package:wukongimfluttersdk/type/const.dart';

extension ReminderExtension on Reminder {
  WKReminder toWKReminder() {
    var reminder = WKReminder();
    reminder.reminderID = id;
    reminder.channelID = channelId;
    reminder.channelType = channelType;
    reminder.messageSeq = messageSeq;
    reminder.type = reminderType;
    reminder.isLocate = isLocate;
    reminder.text = text;
    reminder.version = version;
    reminder.messageID = messageId;
    reminder.uid = uid;
    reminder.done = done;
    if (data != null) {
      reminder.data = data;
    }
    reminder.publisher = publisher;
    return reminder;
  }
}
