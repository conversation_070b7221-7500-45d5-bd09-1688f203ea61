import 'package:and/model/conversation_sync.dart';
import 'package:and/model/extension/message_ext.dart';
import 'package:wukongimfluttersdk/entity/conversation.dart';
import 'package:wukongimfluttersdk/entity/msg.dart';

extension ConversationSyncExtension on ConversationSync {
  WKSyncConversation toWKSyncConversation() {
    WKSyncConversation conversation = WKSyncConversation();
    conversation.conversations = [];

    try {
      for (var item in conversations) {
        WKSyncConvMsg convMsg = WKSyncConvMsg();
        convMsg.channelID = item.channelId;
        convMsg.channelType = item.channelType;
        convMsg.unread = item.unread;
        convMsg.timestamp = item.timestamp;
        convMsg.lastMsgSeq = item.offsetMsgSeq;
        convMsg.lastClientMsgNO = item.lastClientMsgNo;
        convMsg.version = item.version;
        List<WKSyncMsg> msgList = [];

        for (int j = 0; j < item.recents.length; j++) {
          var msgJson = item.recents[j];
          msgList.add(msgJson.toWKSyncMsg());
        }

        convMsg.recents = msgList;
        conversation.conversations?.add(convMsg);
      }
    } catch (e) {
      print('同步最近会话错误');
    }
    return conversation;
  }
}
