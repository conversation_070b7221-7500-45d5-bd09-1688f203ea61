import 'package:and/app.dart';
import 'package:and/constant/group_status.dart';
import 'package:and/constant/wk_channel_extras.dart';
import 'package:and/constant/wk_system_account.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/utils/common_helper.dart';
import 'package:date_format/date_format.dart';
import 'package:wukongimfluttersdk/db/const.dart';
import 'package:wukongimfluttersdk/entity/channel.dart';
import 'package:wukongimfluttersdk/type/const.dart';
import 'package:wukongimfluttersdk/wkim.dart';

extension WKChannelExtension on WKChannel {
  bool get isGroupEnable =>
      channelType == WKChannelType.group && status != GroupStatus.disband;

  bool get isInBlack {
    return status == 2;
  }

  bool get isOnline {
    return online == 1;
  }

  String get avatarUrl {
    if (avatar.isNotEmpty) {
      return avatar;
    }
    return CommonHelper.getAvatarUrl(channelID, channelType: channelType);
  }

  String get displayName {
    // switch (channelID) {
    //   case WKSystemAccount.systemTeam:
    //     return globalContext?.l10n.systemNotifyUser ?? "系统通知";
    //   case WKSystemAccount.systemFileHelper:
    //     return globalContext?.l10n.fileHelperUser ?? "文件传输助手";
    // }
    if (channelRemark.isNotEmpty) {
      return channelRemark.replaceAll("\n", " ");
    }
    return channelName.replaceAll("\n", " ");
  }

  Future<String> get displayNameAsync async {
    var name = displayName;
    if (name.isEmpty) {
      var channel =
          await WKIM.shared.channelManager.getChannel(channelID, channelType);
      if (channel != null) {
        name = channel.displayName;
      } else {
        WKIM.shared.channelManager.fetchChannelInfo(channelID, channelType);
      }
    }
    return name;
  }

  String? get vercode {
    if (remoteExtraMap == null) {
      return null;
    }
    return WKDBConst.readString(remoteExtraMap, WKChannelExtras.vercode);
  }

  String? get sourceDesc {
    if (remoteExtraMap == null) {
      return null;
    }
    return WKDBConst.readString(remoteExtraMap, WKChannelExtras.sourceDesc);
  }

  int? get revokeRemind {
    if (remoteExtraMap == null) {
      return null;
    }
    return WKDBConst.readInt(remoteExtraMap, WKChannelExtras.revokeRemind);
  }

  String? get signature {
    if (remoteExtraMap == null) {
      return null;
    }
    return WKDBConst.readString(remoteExtraMap, WKChannelExtras.signature).replaceAll("\n", " ");
  }


  String? get notice {
    if (localExtra == null) {
      return null;
    }
    return WKDBConst.readString(localExtra, WKChannelExtras.notice);
  }

  int get chatPwdOn {
    if (remoteExtraMap == null) {
      return 0;
    }
    return WKDBConst.readInt(remoteExtraMap, WKChannelExtras.chatPwdOn);
  }

  bool get isSystemChannel {
    return category == WKSystemAccount.accountCategorySystem;
  }

  String get categoryName {
    var context = globalContext;
    if (context == null) return "";
    if (channelType == WKChannelType.community) {
      return context.l10n.categoryCommunity;
    }
    if (robot == 1) {
      return context.l10n.categoryBot;
    }
    var name = "";
    if (category == WKSystemAccount.accountCategorySystem) {
      name = context.l10n.categoryOfficial;
    } else if (category == WKSystemAccount.accountCategoryVisitor) {
      name = context.l10n.categoryVisitor;
    } else if (category == WKSystemAccount.accountCategoryCustomerService) {
      name = context.l10n.categoryCustomerService;
    } else if (category == WKSystemAccount.channelCategoryOrganization) {
      name = context.l10n.categoryAllStaff;
    } else if (category == WKSystemAccount.channelCategoryDepartment) {
      name = context.l10n.categoryDepartment;
    }
    return name;
  }
}
