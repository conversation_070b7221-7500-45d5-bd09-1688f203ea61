import 'package:and/app.dart';
import 'package:and/constant/group_status.dart';
import 'package:and/constant/wk_channel_extras.dart';
import 'package:and/constant/wk_system_account.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/model/content/wk_multi_forward_content.dart';
import 'package:and/model/extension/wk_channel_ext.dart';
import 'package:and/utils/common_helper.dart';
import 'package:date_format/date_format.dart';
import 'package:flutter/cupertino.dart';
import 'package:wukongimfluttersdk/db/const.dart';
import 'package:wukongimfluttersdk/entity/channel.dart';
import 'package:wukongimfluttersdk/type/const.dart';

extension WkMultiForwardContentExtension on WKMultiForwardContent {
  String getDisplayName(BuildContext context) {
    var title = context.l10n.groupChat;
    if (channelType == WKChannelType.personal) {
      if (userList.isNotEmpty) {
        title = userList[0].displayName;
      } else {
        title = context.l10n.groupChat;
      }
    }

    return context.l10n.chatTitleRecords(title);
  }
}
