import 'package:and/constant/wk_channel_member_extras.dart';
import 'package:and/model/group_member.dart';
import 'package:wukongimfluttersdk/entity/channel_member.dart';
import 'package:wukongimfluttersdk/type/const.dart';

extension GroupMemberExtension on GroupMember {
  WKChannelMember toWKChannelMember() {
    WKChannelMember member = WKChannelMember();
    member.memberUID = uid;
    member.memberRemark = remark;
    member.memberName = name;
    member.channelID = groupNo;
    member.channelType = WKChannelType.group;
    member.isDeleted = isDeleted;
    member.version = version;
    member.role = role;
    member.status = status;
    member.memberInviteUID = inviteUid;
    member.robot = robot;
    member.forbiddenExpirationTime = forbiddenExpirTime;
    if (member.robot == 1 && name.isNotEmpty) {
      member.memberName = name;
    }
    member.updatedAt = updatedAt;
    member.createdAt = createdAt;
    Map<String, dynamic> hashMap = {};
    hashMap[WKChannelMemberExtras.WKCode] = vercode;
    hashMap[WKChannelMemberExtras.WKAnonymousAvatar] = anonymousAvatar;
    member.extraMap = hashMap;
    return member;
  }
}
