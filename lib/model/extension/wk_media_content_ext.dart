import 'dart:io';

import 'package:and/utils/common_helper.dart';
import 'package:and/utils/fileUtils.dart';
import 'package:wukongimfluttersdk/model/wk_media_message_content.dart';
import 'package:wukongimfluttersdk/model/wk_video_content.dart';

extension WKMediaMessageContentExtension on WKMediaMessageContent {
  Uri get localUri {
    if (localPath.isEmpty) {
      return Uri();
    }
    return Uri.file(FileUtils.getFullStoragePath(localPath));
  }
}
