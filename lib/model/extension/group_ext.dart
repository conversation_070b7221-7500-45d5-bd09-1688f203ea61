import 'package:and/constant/wk_channel_extras.dart';
import 'package:and/model/group.dart';
import 'package:and/utils/common_helper.dart';
import 'package:wukongimfluttersdk/entity/channel.dart';
import 'package:wukongimfluttersdk/type/const.dart';

extension GroupExtension on Group {
  WKChannel toWKChannel() {
    var channel = WKChannel(groupNo, WKChannelType.group);
    channel.channelName = name ?? '';
    channel.channelRemark = remark;
    channel.channelName = name;
    channel.mute = mute;
    channel.top = top;
    channel.save = save;
    channel.version = version;
    channel.status = status;
    channel.updatedAt = updatedAt;
    channel.createdAt = createdAt;
    channel.receipt = receipt;
    channel.localExtra = {
      WKChannelExtras.notice: notice,
    };
    // 添加 remoteExtraMap
    channel.remoteExtraMap = {
      WKChannelExtras.revokeRemind: revokeRemind,
      WKChannelExtras.screenshot: screenshot,
      WKChannelExtras.chatPwdOn: chatPwdOn,
      WKChannelExtras.groupType: groupType,
      WKChannelExtras.forbiddenAddFriend: forbiddenAddFriend,
      WKChannelExtras.joinGroupRemind: joinGroupRemind,
      WKChannelExtras.allowViewHistoryMsg: allowViewHistoryMsg,
    };

    return channel;
  }
}
