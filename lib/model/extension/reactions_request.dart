import 'package:json_annotation/json_annotation.dart';

part 'reactions_request.g.dart';

@JsonSerializable()
class ReactionsRequest {
  @Json<PERSON>ey(name: 'message_id', defaultValue: '')
  final String messageId;
  @Json<PERSON>ey(name: 'channel_id', defaultValue: '')
  final String channelId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'channel_type', defaultValue: 0)
  final int channelType;
  @JsonKey(defaultValue: '')
  final String emoji;

  const ReactionsRequest({
    required this.messageId,
    required this.channelId,
    required this.channelType,
    required this.emoji,
  });

  factory ReactionsRequest.fromJson(Map<String, dynamic> json) =>
      _$ReactionsRequestFromJson(json);

  Map<String, dynamic> toJson() => _$ReactionsRequestToJson(this);
}
