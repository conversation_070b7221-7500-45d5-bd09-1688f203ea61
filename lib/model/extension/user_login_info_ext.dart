
import 'package:and/model/user_info.dart';
import 'package:and/utils/common_helper.dart';
import 'package:flutter_multi_formatter/flutter_multi_formatter.dart';
import 'package:wukongimfluttersdk/entity/channel.dart';
import 'package:wukongimfluttersdk/type/const.dart';

import '../../constant/wk_channel_extras.dart';
import '../user_login_info.dart';

extension UserLoginInfoExtension on UserLoginInfo {
  String get formatPhone {
    if (phone == null || phone!.isEmpty) {
      return "";
    }
    var formatZone = "";
    if (zone != null && zone!.isNotEmpty){
      formatZone = "+${zone!.substring(2)} ";
    }
    return formatZone + phone!;
  }
}
