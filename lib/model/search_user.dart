import 'package:json_annotation/json_annotation.dart';

part 'search_user.g.dart';

@JsonSerializable()
class SearchUser {
  final Data? data;
  @JsonKey(defaultValue: 0)
  final int exist;

  const SearchUser({
    this.data,
    required this.exist,
  });

  factory SearchUser.fromJson(Map<String, dynamic> json) =>
      _$SearchUserFromJson(json);

  Map<String, dynamic> toJson() => _$SearchUserToJson(this);
}

@JsonSerializable()
class Data {
  @JsonKey(defaultValue: '')
  final String uid;
  @JsonKey(defaultValue: '')
  final String name;
  @JsonKey(defaultValue: '')
  final String vercode;

  const Data({
    required this.uid,
    required this.name,
    required this.vercode,
  });

  factory Data.fromJson(Map<String, dynamic> json) => _$DataFromJson(json);

  Map<String, dynamic> toJson() => _$DataToJson(this);
}
