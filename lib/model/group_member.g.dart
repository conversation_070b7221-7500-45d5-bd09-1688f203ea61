// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'group_member.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GroupMember _$GroupMemberFromJson(Map<String, dynamic> json) => GroupMember(
      uid: json['uid'] as String? ?? '',
      groupNo: json['group_no'] as String? ?? '',
      name: json['name'] as String? ?? '',
      remark: json['remark'] as String? ?? '',
      role: (json['role'] as num?)?.toInt() ?? 0,
      version: (json['version'] as num?)?.toInt() ?? 0,
      isDeleted: (json['is_deleted'] as num?)?.toInt() ?? 0,
      status: (json['status'] as num?)?.toInt() ?? 0,
      vercode: json['vercode'] as String? ?? '',
      inviteUid: json['invite_uid'] as String? ?? '',
      robot: (json['robot'] as num?)?.toInt() ?? 0,
      anonymousAvatar: (json['anonymous_avatar'] as num?)?.toInt() ?? 0,
      forbiddenExpirTime: (json['forbidden_expir_time'] as num?)?.toInt() ?? 0,
      updatedAt: json['updated_at'] as String? ?? '',
      createdAt: json['created_at'] as String? ?? '',
    );

Map<String, dynamic> _$GroupMemberToJson(GroupMember instance) =>
    <String, dynamic>{
      'uid': instance.uid,
      'group_no': instance.groupNo,
      'name': instance.name,
      'remark': instance.remark,
      'role': instance.role,
      'anonymous_avatar': instance.anonymousAvatar,
      'version': instance.version,
      'is_deleted': instance.isDeleted,
      'status': instance.status,
      'vercode': instance.vercode,
      'invite_uid': instance.inviteUid,
      'robot': instance.robot,
      'forbidden_expir_time': instance.forbiddenExpirTime,
      'updated_at': instance.updatedAt,
      'created_at': instance.createdAt,
    };
