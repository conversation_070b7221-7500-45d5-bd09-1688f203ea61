import 'package:wukongimfluttersdk/manager/message_manager.dart';
import 'package:wukongimfluttersdk/model/wk_media_message_content.dart';
import 'package:wukongimfluttersdk/model/wk_message_content.dart';
import 'package:wukongimfluttersdk/type/const.dart';

class WKVoiceContent extends WKMediaMessageContent {
  int timeTrad; // 语音秒长
  String? waveform = "/////////////w=="; // 语音波纹 base64编码
  String? recognizeResult;
  WKVoiceContent(this.timeTrad) {
    contentType = WkMessageContentType.voice;
  }

  @override
  Map<String, dynamic> encodeJson() {
    return {
      'timeTrad': timeTrad,
      'url': url,
      'waveform': waveform,
      'localPath': localPath,
      'recognizeResult': recognizeResult,
    };
  }

  @override
  WKMessageContent decodeJson(Map<String, dynamic> json) {
    timeTrad = readInt(json, 'timeTrad');
    url = readString(json, 'url');
    localPath = readString(json, 'localPath');
    waveform = readString(json, 'waveform');
    recognizeResult = readString(json, 'recognizeResult');
    return this;
  }

  @override
  String displayText() {
    return WKMessageManager.shared.messageTextProvider
            ?.displayText(contentType) ??
        '[语音]';
  }

  @override
  String searchableWord() {
    return WKMessageManager.shared.messageTextProvider
            ?.searchableWord(contentType) ??
        '[语音]';
  }
}
