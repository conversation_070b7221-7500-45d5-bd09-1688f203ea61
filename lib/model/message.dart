import 'package:json_annotation/json_annotation.dart';

import 'message_extra.dart';
import 'sync_reaction.dart';

part 'message.g.dart';

@JsonSerializable()
class Message {
  @Json<PERSON>ey(name: 'message_id', defaultValue: 0)
  final int messageId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'message_seq', defaultValue: 0)
  final int messageSeq;
  @<PERSON>son<PERSON><PERSON>(name: 'channel_id', defaultValue: '')
  final String channelId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'channel_type', defaultValue: 0)
  final int channelType;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'client_msg_no', defaultValue: '')
  final String clientMsgNo;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'from_uid', defaultValue: '')
  final String fromUid;
  @Json<PERSON>ey(defaultValue: 0)
  final int timestamp;
  @<PERSON>son<PERSON><PERSON>(name: 'voice_status', defaultValue: 0)
  final int voiceStatus;
  @<PERSON>son<PERSON>ey(name: 'is_deleted', defaultValue: 0)
  final int isDeleted;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'voice_recognize_result')
  final String? voiceRecognizeResult;
  @Json<PERSON>ey(defaultValue: 0)
  final int revoke;
  @<PERSON>son<PERSON><PERSON>(defaultValue: '')
  final String revoker;
  @JsonKey(name: 'extra_version', defaultValue: 0)
  final int extraVersion;
  @JsonKey(name: 'unread_count', defaultValue: 0)
  final int unreadCount;
  @JsonKey(name: 'readed_count', defaultValue: 0)
  final int readedCount;
  @JsonKey(defaultValue: 0)
  final int readed;
  @JsonKey(defaultValue: 0)
  final int receipt;
  @JsonKey(defaultValue: 0)
  final int setting;
  final dynamic payload;
  @JsonKey(name: 'signal_payload', defaultValue: '')
  final String signalPayload;
  @JsonKey(name: 'message_extra')
  final MessageExtra? messageExtra;
  @JsonKey(defaultValue: [])
  final List<SyncReaction> reactions;

  const Message({
    required this.messageId,
    required this.messageSeq,
    required this.channelId,
    required this.channelType,
    required this.clientMsgNo,
    required this.fromUid,
    required this.timestamp,
    required this.voiceStatus,
    required this.isDeleted,
    this.voiceRecognizeResult,
    required this.revoke,
    required this.revoker,
    required this.extraVersion,
    required this.unreadCount,
    required this.readedCount,
    required this.readed,
    required this.receipt,
    required this.setting,
    this.payload,
    required this.signalPayload,
    this.messageExtra,
    required this.reactions,
  });

  factory Message.fromJson(Map<String, dynamic> json) =>
      _$MessageFromJson(json);

  Map<String, dynamic> toJson() => _$MessageToJson(this);
}