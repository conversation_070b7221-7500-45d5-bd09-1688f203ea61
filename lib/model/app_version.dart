import 'package:json_annotation/json_annotation.dart';

part 'app_version.g.dart';

@JsonSerializable()
class AppVersion {
  @Json<PERSON>ey(name: 'app_version', defaultValue: '')
  final String appVersion;
  @Json<PERSON>ey(defaultValue: '')
  final String os;
  @J<PERSON><PERSON><PERSON>(name: 'is_force', defaultValue: 0)
  final int isForce;
  @<PERSON>son<PERSON>ey(name: 'update_desc', defaultValue: '')
  final String updateDesc;
  @Json<PERSON>ey(name: 'download_url', defaultValue: '')
  final String downloadUrl;
  @JsonKey(name: 'created_at', defaultValue: '')
  final String createdAt;

  const AppVersion({
    required this.appVersion,
    required this.os,
    required this.isForce,
    required this.updateDesc,
    required this.downloadUrl,
    required this.createdAt,
  });

  factory AppVersion.fromJson(Map<String, dynamic> json) =>
      _$AppVersionFromJson(json);

  Map<String, dynamic> toJson() => _$AppVersionToJson(this);
}
