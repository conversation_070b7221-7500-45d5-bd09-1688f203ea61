
import 'package:and/model/pinned_status_message.dart';
import 'package:json_annotation/json_annotation.dart';

import 'message.dart';

part 'sync_pinned_message.g.dart';

@JsonSerializable()
class SyncPinnedMessage {
  @Json<PERSON>ey(name: 'pinned_messages', defaultValue: [])
  final List<PinnedStatusMessage> pinnedMessages;
  @JsonKey(name: 'messages', defaultValue: [])
  final List<Message> messages;

  const SyncPinnedMessage(
      this.pinnedMessages, this.messages);

  factory SyncPinnedMessage.fromJson(Map<String, dynamic> json) =>
      _$SyncPinnedMessageFromJson(json);

  Map<String, dynamic> toJson() => _$SyncPinnedMessageToJson(this);
}