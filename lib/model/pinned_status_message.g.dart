// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'pinned_status_message.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PinnedStatusMessage _$PinnedStatusMessageFromJson(Map<String, dynamic> json) =>
    PinnedStatusMessage(
      messageId: json['message_id'] as String? ?? '',
      messageSeq: (json['message_seq'] as num?)?.toInt() ?? 0,
      channelId: json['channel_id'] as String? ?? '',
      channelType: (json['channel_type'] as num?)?.toInt() ?? 0,
      isDeleted: (json['is_deleted'] as num?)?.toInt() ?? 0,
      version: (json['version'] as num?)?.toInt() ?? 0,
      createdBy: json['created_by'] as String? ?? '',
      updatedAt: json['updated_at'] as String? ?? '',
      createdAt: json['created_at'] as String? ?? '',
    );

Map<String, dynamic> _$PinnedStatusMessageToJson(
        PinnedStatusMessage instance) =>
    <String, dynamic>{
      'message_id': instance.messageId,
      'message_seq': instance.messageSeq,
      'channel_id': instance.channelId,
      'channel_type': instance.channelType,
      'is_deleted': instance.isDeleted,
      'version': instance.version,
      'created_by': instance.createdBy,
      'updated_at': instance.updatedAt,
      'created_at': instance.createdAt,
    };
