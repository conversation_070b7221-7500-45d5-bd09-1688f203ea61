import 'package:json_annotation/json_annotation.dart';

part 'group_qr.g.dart';

@JsonSerializable()
class GroupQr {
  @JsonKey(defaultValue: 0)
  final int day;
  @JsonKey(defaultValue: '')
  final String qrcode;
  @<PERSON><PERSON><PERSON>ey(defaultValue: '')
  final String expire;

  const GroupQr({
    required this.day,
    required this.qrcode,
    required this.expire,
  });

  factory GroupQr.fromJson(Map<String, dynamic> json) =>
      _$GroupQrFromJson(json);

  Map<String, dynamic> toJson() => _$GroupQrToJson(this);
}
