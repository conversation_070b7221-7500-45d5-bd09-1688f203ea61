// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'message_extra_sync_request.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MessageExtraSyncRequest _$MessageExtraSyncRequestFromJson(
        Map<String, dynamic> json) =>
    MessageExtraSyncRequest(
      channelId: json['channel_id'] as String? ?? '',
      channelType: (json['channel_type'] as num?)?.toInt() ?? 0,
      extraVersion: (json['extra_version'] as num?)?.toInt() ?? 0,
      source: json['source'] as String? ?? '',
      limit: (json['limit'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$MessageExtraSyncRequestToJson(
        MessageExtraSyncRequest instance) =>
    <String, dynamic>{
      'channel_id': instance.channelId,
      'channel_type': instance.channelType,
      'extra_version': instance.extraVersion,
      'source': instance.source,
      'limit': instance.limit,
    };
