import 'package:json_annotation/json_annotation.dart';

part 'clear_unread_request.g.dart';

@JsonSerializable()
class ClearUnreadRequest {
  @Json<PERSON>ey(name: 'channel_id', defaultValue: '')
  final String channelId;
  @Json<PERSON>ey(name: 'channel_type',defaultValue: 0)
  final int channelType;
  @Json<PERSON>ey(defaultValue: 0)
  final int unread;

  const ClearUnreadRequest({
    required this.channelId,
    required this.channelType,
    required this.unread,
  });

  factory ClearUnreadRequest.fromJson(Map<String, dynamic> json) =>
      _$ClearUnreadRequestFromJson(json);

  Map<String, dynamic> toJson() => _$ClearUnreadRequestToJson(this);
}
