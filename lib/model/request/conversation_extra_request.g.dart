// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'conversation_extra_request.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ConversationExtraRequest _$ConversationExtraRequestFromJson(
        Map<String, dynamic> json) =>
    ConversationExtraRequest(
      browseTo: (json['browse_to'] as num?)?.toInt() ?? 0,
      keepMessageSeq: (json['keep_message_seq'] as num?)?.toInt() ?? 0,
      keepOffsetY: (json['keep_offset_y'] as num?)?.toInt() ?? 0,
      draft: json['draft'] as String? ?? '',
    );

Map<String, dynamic> _$ConversationExtraRequestToJson(
        ConversationExtraRequest instance) =>
    <String, dynamic>{
      'browse_to': instance.browseTo,
      'keep_message_seq': instance.keepMessageSeq,
      'keep_offset_y': instance.keepOffsetY,
      'draft': instance.draft,
    };
