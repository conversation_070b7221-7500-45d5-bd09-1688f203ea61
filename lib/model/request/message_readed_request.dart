import 'package:json_annotation/json_annotation.dart';

part 'message_readed_request.g.dart';

@JsonSerializable()
class MessageReadedRequest {
  @J<PERSON><PERSON><PERSON>(name: 'channel_id')
  final String? channelId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'channel_type')
  final int? channelType;
  @J<PERSON><PERSON><PERSON>(name: 'message_ids')
  final List<String>? messageIds;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'read_channel_id')
  final String? readChannelId;

  const MessageReadedRequest({
    this.channelId,
    this.channelType,
    this.messageIds,
    this.readChannelId,
  });

  factory MessageReadedRequest.fromJson(Map<String, dynamic> json) =>
      _$MessageReadedRequestFromJson(json);

  Map<String, dynamic> toJson() => _$MessageReadedRequestToJson(this);
}
