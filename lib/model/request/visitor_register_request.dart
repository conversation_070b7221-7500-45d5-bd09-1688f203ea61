import 'package:json_annotation/json_annotation.dart';

part 'visitor_register_request.g.dart';

@JsonSerializable()
class VisitorRegisterRequest {
  @Json<PERSON>ey(name: 'verify_code', defaultValue: '')
  final String verifyCode;
  @J<PERSON><PERSON>ey(name: 'area_code')
  final String? areaCode;
  final String? phone;
  final String? email;
  @Json<PERSON>ey(defaultValue: '')
  final String password;

  const VisitorRegisterRequest({
    required this.verifyCode,
    this.areaCode,
    this.phone,
    this.email,
    required this.password,
  });

  factory VisitorRegisterRequest.fromJson(Map<String, dynamic> json) =>
      _$VisitorRegisterRequestFromJson(json);

  Map<String, dynamic> toJson() => _$VisitorRegisterRequestToJson(this);
}
