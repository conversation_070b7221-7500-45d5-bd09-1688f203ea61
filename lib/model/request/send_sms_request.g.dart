// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'send_sms_request.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SendSMSRequest _$SendSMSRequestFromJson(Map<String, dynamic> json) =>
    SendSMSRequest(
      zone: json['zone'] as String? ?? '',
      phone: json['phone'] as String,
      codeType: $enumDecodeNullable(_$SendCodeTypeEnumMap, json['code_type']) ??
          SendCodeType.register,
    );

Map<String, dynamic> _$SendSMSRequestToJson(SendSMSRequest instance) =>
    <String, dynamic>{
      'zone': instance.zone,
      'phone': instance.phone,
      'code_type': _$SendCodeTypeEnumMap[instance.codeType]!,
    };

const _$SendCodeTypeEnumMap = {
  SendCodeType.register: 'REGISTER',
  SendCodeType.payPwd: 'PAY_PWD',
  SendCodeType.forgetLoginPwd: 'FORGET_LOGIN_PWD',
  SendCodeType.checkMobile: 'CHECK_MOBILE',
  SendCodeType.destroyAccount: 'DESTROY_ACCOUNT',
  SendCodeType.updatePhone: 'UPDATE_PHONE',
};
