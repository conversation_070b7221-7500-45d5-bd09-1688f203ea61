import 'package:json_annotation/json_annotation.dart';

part 'sync_reaction_request.g.dart';

@JsonSerializable()
class SyncReactionRequest {
  @J<PERSON><PERSON><PERSON>(defaultValue: 0)
  final int seq;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'channel_id', defaultValue: '')
  final String channelId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'channel_type', defaultValue: 0)
  final int channelType;
  @<PERSON>son<PERSON>ey(defaultValue: 0)
  final int limit;

  const SyncReactionRequest({
    required this.seq,
    required this.channelId,
    required this.channelType,
    required this.limit,
  });

  factory SyncReactionRequest.fromJson(Map<String, dynamic> json) =>
      _$SyncReactionRequestFromJson(json);

  Map<String, dynamic> toJson() => _$SyncReactionRequestToJson(this);
}
