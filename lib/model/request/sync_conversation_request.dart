import 'package:json_annotation/json_annotation.dart';

part 'sync_conversation_request.g.dart';

@JsonSerializable()
class SyncConversationRequest {
  @<PERSON><PERSON><PERSON><PERSON>(name: 'version', defaultValue: 0)
  final int version;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'last_msg_seqs', defaultValue: '')
  final String lastMsgSeqs;
  @Json<PERSON><PERSON>(name: 'msg_count', defaultValue: 10)
  final int msgCount;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'device_uuid', defaultValue: '')
  final String deviceUuid;

  const SyncConversationRequest({
    required this.version,
    required this.lastMsgSeqs,
    required this.msgCount,
    required this.deviceUuid,
  });

  factory SyncConversationRequest.fromJson(Map<String, dynamic> json) =>
      _$SyncConversationRequestFromJson(json);

  Map<String, dynamic> toJson() => _$SyncConversationRequestToJson(this);
}