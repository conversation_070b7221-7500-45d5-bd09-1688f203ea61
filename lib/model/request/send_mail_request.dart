import 'package:json_annotation/json_annotation.dart';

part 'send_mail_request.g.dart';
@JsonEnum(valueField: "type")
enum SendMailType {
  register(0),
  resetPwd(2),
  updateEmail(6);

  final int type;

  const SendMailType(this.type);
}

@JsonSerializable()
class SendMailRequest {
  @JsonKey(defaultValue: '')
  final String email;
  @JsonKey(defaultValue: SendMailType.register)
  final SendMailType type; //0 注册，2重置密码

  const SendMailRequest({
    required this.email,
    required this.type,
  });

  factory SendMailRequest.fromJson(Map<String, dynamic> json) =>
      _$SendMailRequestFromJson(json);

  Map<String, dynamic> toJson() => _$SendMailRequestToJson(this);
}
