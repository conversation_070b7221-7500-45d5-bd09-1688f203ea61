import 'package:json_annotation/json_annotation.dart';

part 'message_pinned_request.g.dart';

@JsonSerializable()
class MessagePinnedRequest {
  @J<PERSON><PERSON><PERSON>(name: 'channel_id')
  final String channelId;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'channel_type')
  final int channelType;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'message_id')
  final String messageId;

  @Json<PERSON>ey(name: 'message_seq')
  final int messageSeq;

  @JsonKey(name: 'type')
  final int type;

  MessagePinnedRequest({
    required this.channelId,
    required this.channelType,
    required this.messageId,
    required this.messageSeq,
    this.type = 2,
  });

  factory MessagePinnedRequest.fromJson(Map<String, dynamic> json) =>
      _$MessagePinnedRequestFromJson(json);

  Map<String, dynamic> toJson() => _$MessagePinnedRequestToJson(this);
}