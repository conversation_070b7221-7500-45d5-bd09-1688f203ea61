import 'package:json_annotation/json_annotation.dart';

part 'sync_channel_msg_request.g.dart';

@JsonSerializable()
class SyncChannelMsgRequest {
  @Json<PERSON>ey(name: 'channel_id', defaultValue: '')
  final String channelId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'channel_type', defaultValue: 0)
  final int channelType;
  @<PERSON>son<PERSON>ey(name: 'start_message_seq', defaultValue: 0)
  final int startMessageSeq;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'end_message_seq', defaultValue: 0)
  final int endMessageSeq;
  @<PERSON>son<PERSON>ey(defaultValue: 0)
  final int limit;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'pull_mode', defaultValue: 0)
  final int pullMode;

  const SyncChannelMsgRequest({
    required this.channelId,
    required this.channelType,
    required this.startMessageSeq,
    required this.endMessageSeq,
    required this.limit,
    required this.pullMode,
  });

  factory SyncChannelMsgRequest.fromJson(Map<String, dynamic> json) =>
      _$SyncChannelMsgRequestFromJson(json);

  Map<String, dynamic> toJson() => _$SyncChannelMsgRequestToJson(this);
}