import 'package:and/model/device.dart';
import 'package:json_annotation/json_annotation.dart';

part 'phone_register_request.g.dart';

@JsonSerializable()
class PhoneRegisterRequest {
  @JsonKey(defaultValue: '')
  final String zone;
  @<PERSON><PERSON><PERSON><PERSON>(defaultValue: '')
  final String phone;
  @<PERSON><PERSON><PERSON><PERSON>(defaultValue: '')
  final String code;
  @<PERSON><PERSON><PERSON><PERSON>(defaultValue: '')
  final String password;
  @J<PERSON><PERSON><PERSON>(name: 'invite_code', defaultValue: '')
  final String inviteCode;
  @Json<PERSON>ey(defaultValue: 0)
  final int flag;
  final Device? device;

  const PhoneRegisterRequest({
    required this.zone,
    required this.phone,
    required this.code,
    required this.password,
    required this.inviteCode,
    required this.flag,
    this.device,
  });

  factory PhoneRegisterRequest.fromJson(Map<String, dynamic> json) =>
      _$PhoneRegisterRequestFromJson(json);

  Map<String, dynamic> toJson() => _$PhoneRegisterRequestToJson(this);
}