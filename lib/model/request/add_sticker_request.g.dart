// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'add_sticker_request.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AddStickerRequest _$AddStickerRequestFromJson(Map<String, dynamic> json) =>
    AddStickerRequest(
      filePath: json['file_path'] as String? ?? '',
      width: json['width'] as String? ?? '',
      height: json['height'] as String? ?? '',
    );

Map<String, dynamic> _$AddStickerRequestToJson(AddStickerRequest instance) =>
    <String, dynamic>{
      'file_path': instance.filePath,
      'width': instance.width,
      'height': instance.height,
    };
