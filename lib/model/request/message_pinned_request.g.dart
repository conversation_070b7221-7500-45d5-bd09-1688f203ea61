// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'message_pinned_request.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MessagePinnedRequest _$MessagePinnedRequestFromJson(
        Map<String, dynamic> json) =>
    MessagePinnedRequest(
      channelId: json['channel_id'] as String,
      channelType: (json['channel_type'] as num).toInt(),
      messageId: json['message_id'] as String,
      messageSeq: (json['message_seq'] as num).toInt(),
      type: (json['type'] as num?)?.toInt() ?? 2,
    );

Map<String, dynamic> _$MessagePinnedRequestToJson(
        MessagePinnedRequest instance) =>
    <String, dynamic>{
      'channel_id': instance.channelId,
      'channel_type': instance.channelType,
      'message_id': instance.messageId,
      'message_seq': instance.messageSeq,
      'type': instance.type,
    };
