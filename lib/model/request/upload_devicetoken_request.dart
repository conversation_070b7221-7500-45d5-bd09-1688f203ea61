import 'package:json_annotation/json_annotation.dart';

part 'upload_devicetoken_request.g.dart';

@JsonSerializable()
class UploadDevicetokenRequest {
  @JsonKey(defaultValue: '')
  final String device_token;
  @JsonKey(defaultValue: 'FIREBASE')
  final String device_type;
  @J<PERSON><PERSON>ey(defaultValue: '')
  final String bundle_id;

  const UploadDevicetokenRequest({
    required this.device_token,
    required this.device_type,
    required this.bundle_id,
  });

  factory UploadDevicetokenRequest.fromJson(Map<String, dynamic> json) =>
      _$UploadDevicetokenRequestFromJson(json);

  Map<String, dynamic> toJson() => _$UploadDevicetokenRequestToJson(this);
}