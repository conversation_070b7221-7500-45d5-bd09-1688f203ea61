import 'package:json_annotation/json_annotation.dart';

part 'voice_recognize_request.g.dart';

@JsonSerializable()
class VoiceRecognizeRequest {
  @Json<PERSON>ey(name: 'message_id', defaultValue: '')
  final String messageId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'channel_id', defaultValue: '')
  final String channelId;
  @<PERSON>son<PERSON>ey(name: 'channel_type', defaultValue: 0)
  final int channelType;
  @JsonKey(name: 'message_seq', defaultValue: 0)
  final int messageSeq;

  const VoiceRecognizeRequest({
    required this.messageId,
    required this.channelId,
    required this.channelType,
    required this.messageSeq,
  });

  factory VoiceRecognizeRequest.fromJson(Map<String, dynamic> json) =>
      _$VoiceRecognizeRequestFromJson(json);

  Map<String, dynamic> toJson() => _$VoiceRecognizeRequestToJson(this);
}
