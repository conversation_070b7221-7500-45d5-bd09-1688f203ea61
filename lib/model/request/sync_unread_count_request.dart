import 'package:json_annotation/json_annotation.dart';

part 'sync_unread_count_request.g.dart';

@JsonSerializable()
class SyncUnreadCountRequest {
  @JsonKey(name: 'channel_id', defaultValue: '')
  final String channelId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'channel_type', defaultValue: 0)
  final int channelType;

  const SyncUnreadCountRequest({
    required this.channelId,
    required this.channelType
  });

  factory SyncUnreadCountRequest.fromJson(Map<String, dynamic> json) =>
      _$SyncUnreadCountRequestFromJson(json);

  Map<String, dynamic> toJson() => _$SyncUnreadCountRequestToJson(this);
}
