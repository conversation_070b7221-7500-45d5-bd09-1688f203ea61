// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'device_report_request.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DeviceReportRequest _$DeviceReportRequestFromJson(Map<String, dynamic> json) =>
    DeviceReportRequest(
      referrerUrl: json['referrer_url'] as String? ?? '',
      referrerClickTime: (json['referrer_click_time'] as num?)?.toInt() ?? 0,
      appInstallTime: (json['app_install_time'] as num?)?.toInt() ?? 0,
      instantExperienceLaunched:
          (json['instant_experience_launched'] as num?)?.toInt() ?? 0,
      deviceId: json['device_id'] as String? ?? '',
      deviceModel: json['device_model'] as String? ?? '',
      deviceBrand: json['device_brand'] as String? ?? '',
      deviceManufacturer: json['device_manufacturer'] as String? ?? '',
      osName: json['os_name'] as String? ?? '',
      osVersion: json['os_version'] as String? ?? '',
      screenWidth: json['screen_width'] as String? ?? '',
      screenHeight: json['screen_height'] as String? ?? '',
      screenDensity: json['screen_density'] as String? ?? '',
      cpuAbi: json['cpu_abi'] as String? ?? '',
    );

Map<String, dynamic> _$DeviceReportRequestToJson(
        DeviceReportRequest instance) =>
    <String, dynamic>{
      'referrer_url': instance.referrerUrl,
      'referrer_click_time': instance.referrerClickTime,
      'app_install_time': instance.appInstallTime,
      'instant_experience_launched': instance.instantExperienceLaunched,
      'device_id': instance.deviceId,
      'device_model': instance.deviceModel,
      'device_brand': instance.deviceBrand,
      'device_manufacturer': instance.deviceManufacturer,
      'os_name': instance.osName,
      'os_version': instance.osVersion,
      'screen_width': instance.screenWidth,
      'screen_height': instance.screenHeight,
      'screen_density': instance.screenDensity,
      'cpu_abi': instance.cpuAbi,
    };
