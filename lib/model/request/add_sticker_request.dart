import 'package:json_annotation/json_annotation.dart';

part 'add_sticker_request.g.dart';

@JsonSerializable()
class AddStickerRequest {
  @Json<PERSON>ey(name: 'file_path', defaultValue: '')
  final String filePath;
  @Json<PERSON>ey(defaultValue: '')
  final String width;
  @Json<PERSON>ey(defaultValue: '')
  final String height;

  const AddStickerRequest({
    required this.filePath,
    required this.width,
    required this.height,
  });

  factory AddStickerRequest.fromJson(Map<String, dynamic> json) =>
      _$AddStickerRequestFromJson(json);

  Map<String, dynamic> toJson() => _$AddStickerRequestToJson(this);
}
