import 'package:json_annotation/json_annotation.dart';

part 'send_forget_pwd_sms_request.g.dart';

@JsonSerializable()
class SendForgetPwdSMSRequest {
  @JsonKey(defaultValue: '')
  final String zone;
  @Json<PERSON>ey(defaultValue: '')
  final String phone;

  const SendForgetPwdSMSRequest({
    required this.zone,
    required this.phone,
  });

  factory SendForgetPwdSMSRequest.fromJson(Map<String, dynamic> json) =>
      _$SendForgetPwdSMSRequestFromJson(json);

  Map<String, dynamic> toJson() => _$SendForgetPwdSMSRequestToJson(this);
}
