import 'package:json_annotation/json_annotation.dart';

part 'sync_msg_request.g.dart';

@JsonSerializable()
class SyncMsgRequest {
  @Json<PERSON>ey(defaultValue: 0)
  final int limit;
  @Json<PERSON>ey(name:"max_message_seq", defaultValue: 0)
  final int maxMessageSeq;

  const SyncMsgRequest({
    required this.limit,
    required this.maxMessageSeq
  });

  factory SyncMsgRequest.fromJson(Map<String, dynamic> json) =>
      _$SyncMsgRequestFromJson(json);

  Map<String, dynamic> toJson() => _$SyncMsgRequestToJson(this);
}
