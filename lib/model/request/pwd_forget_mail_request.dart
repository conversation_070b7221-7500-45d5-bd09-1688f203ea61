import 'package:json_annotation/json_annotation.dart';

part 'pwd_forget_mail_request.g.dart';

@JsonSerializable()
class PwdForgetMailRequest {
  @JsonKey(defaultValue: '')
  final String email;
  @Json<PERSON>ey(defaultValue: '')
  final String code;
  @Json<PERSON>ey(defaultValue: '')
  final String pwd;

  const PwdForgetMailRequest({
    required this.email,
    required this.code,
    required this.pwd,
  });

  factory PwdForgetMailRequest.fromJson(Map<String, dynamic> json) =>
      _$PwdForgetMailRequestFromJson(json);

  Map<String, dynamic> toJson() => _$PwdForgetMailRequestToJson(this);
}
