import 'package:json_annotation/json_annotation.dart';

part 'sure_friend_request.g.dart';

@JsonSerializable()
class SureFriendRequest {
  @JsonKey(defaultValue: '')
  final String token;

  const SureFriendRequest({
    required this.token,
  });

  factory SureFriendRequest.fromJson(Map<String, dynamic> json) =>
      _$SureFriendRequestFromJson(json);

  Map<String, dynamic> toJson() => _$SureFriendRequestToJson(this);
}
