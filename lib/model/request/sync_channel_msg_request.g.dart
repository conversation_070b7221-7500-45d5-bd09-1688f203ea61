// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'sync_channel_msg_request.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SyncChannelMsgRequest _$SyncChannelMsgRequestFromJson(
        Map<String, dynamic> json) =>
    SyncChannelMsgRequest(
      channelId: json['channel_id'] as String? ?? '',
      channelType: (json['channel_type'] as num?)?.toInt() ?? 0,
      startMessageSeq: (json['start_message_seq'] as num?)?.toInt() ?? 0,
      endMessageSeq: (json['end_message_seq'] as num?)?.toInt() ?? 0,
      limit: (json['limit'] as num?)?.toInt() ?? 0,
      pullMode: (json['pull_mode'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$SyncChannelMsgRequestToJson(
        SyncChannelMsgRequest instance) =>
    <String, dynamic>{
      'channel_id': instance.channelId,
      'channel_type': instance.channelType,
      'start_message_seq': instance.startMessageSeq,
      'end_message_seq': instance.endMessageSeq,
      'limit': instance.limit,
      'pull_mode': instance.pullMode,
    };
