import 'package:json_annotation/json_annotation.dart';

part 'conversation_extra_request.g.dart';

@JsonSerializable()
class ConversationExtraRequest {
  @<PERSON><PERSON><PERSON><PERSON>(name: 'browse_to', defaultValue: 0)
  final int browseTo;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'keep_message_seq', defaultValue: 0)
  final int keepMessageSeq;
  @<PERSON>son<PERSON>ey(name: 'keep_offset_y', defaultValue: 0)
  final int keepOffsetY;
  @<PERSON><PERSON><PERSON><PERSON>(defaultValue: '')
  final String draft;

  const ConversationExtraRequest({
    required this.browseTo,
    required this.keepMessageSeq,
    required this.keepOffsetY,
    required this.draft,
  });

  factory ConversationExtraRequest.fromJson(Map<String, dynamic> json) =>
      _$ConversationExtraRequestFromJson(json);

  Map<String, dynamic> toJson() => _$ConversationExtraRequestToJson(this);
}
