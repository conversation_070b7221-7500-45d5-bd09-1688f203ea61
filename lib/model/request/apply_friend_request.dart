import 'package:json_annotation/json_annotation.dart';

part 'apply_friend_request.g.dart';

@JsonSerializable()
class ApplyFriendRequest {
  @J<PERSON><PERSON>ey(name: 'to_uid', defaultValue: '')
  final String toUid;
  @J<PERSON><PERSON>ey(defaultValue: '')
  final String remark;
  @J<PERSON><PERSON>ey(defaultValue: '')
  final String vercode;

  const ApplyFriendRequest({
    required this.toUid,
    required this.remark,
    required this.vercode,
  });

  factory ApplyFriendRequest.fromJson(Map<String, dynamic> json) =>
      _$ApplyFriendRequestFromJson(json);

  Map<String, dynamic> toJson() => _$ApplyFriendRequestToJson(this);
}
