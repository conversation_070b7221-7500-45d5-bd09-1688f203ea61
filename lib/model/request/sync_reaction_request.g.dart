// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'sync_reaction_request.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SyncReactionRequest _$SyncReactionRequestFromJson(Map<String, dynamic> json) =>
    SyncReactionRequest(
      seq: (json['seq'] as num?)?.toInt() ?? 0,
      channelId: json['channel_id'] as String? ?? '',
      channelType: (json['channel_type'] as num?)?.toInt() ?? 0,
      limit: (json['limit'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$SyncReactionRequestToJson(
        SyncReactionRequest instance) =>
    <String, dynamic>{
      'seq': instance.seq,
      'channel_id': instance.channelId,
      'channel_type': instance.channelType,
      'limit': instance.limit,
    };
