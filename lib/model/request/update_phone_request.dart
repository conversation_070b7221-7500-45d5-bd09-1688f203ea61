import 'package:json_annotation/json_annotation.dart';

part 'update_phone_request.g.dart';

@JsonSerializable()
class UpdatePhoneRequest {
  @JsonKey(defaultValue: '')
  final String zone;
  final String phone;
  final String code;

  const UpdatePhoneRequest({
    required this.zone,
    required this.phone,
    required this.code
  });

  factory UpdatePhoneRequest.fromJson(Map<String, dynamic> json) =>
      _$UpdatePhoneRequestFromJson(json);

  Map<String, dynamic> toJson() => _$UpdatePhoneRequestToJson(this);
}