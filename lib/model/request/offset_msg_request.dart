import 'package:json_annotation/json_annotation.dart';

part 'offset_msg_request.g.dart';

@JsonSerializable()
class OffsetMsgRequest {
  @Json<PERSON>ey(name: 'channel_id', defaultValue: '')
  final String channelId;
  @<PERSON>son<PERSON>ey(name: 'channel_type',defaultValue: 0)
  final int channelType;
  @JsonKey(name: 'message_seq',defaultValue: 0)
  final int messageSeq;

  const OffsetMsgRequest({
    required this.channelId,
    required this.channelType,
    required this.messageSeq,
  });

  factory OffsetMsgRequest.fromJson(Map<String, dynamic> json) =>
      _$OffsetMsgRequestFromJson(json);

  Map<String, dynamic> toJson() => _$OffsetMsgRequestToJson(this);
}
