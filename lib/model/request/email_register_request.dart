import 'package:and/model/device.dart';
import 'package:json_annotation/json_annotation.dart';

part 'email_register_request.g.dart';

@JsonSerializable()
class EmailRegisterRequest {
  @Json<PERSON>ey(defaultValue: '')
  final String email;
  @<PERSON><PERSON><PERSON><PERSON>(defaultValue: '')
  final String code;
  @<PERSON><PERSON><PERSON><PERSON>(defaultValue: '')
  final String password;
  @J<PERSON><PERSON>ey(defaultValue: 0)
  final int flag;
  final Device? device;

  const EmailRegisterRequest({
    required this.email,
    required this.code,
    required this.password,
    required this.flag,
    this.device,
  });

  factory EmailRegisterRequest.fromJson(Map<String, dynamic> json) =>
      _$EmailRegisterRequestFromJson(json);

  Map<String, dynamic> toJson() => _$EmailRegisterRequestToJson(this);
}