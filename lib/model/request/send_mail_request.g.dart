// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'send_mail_request.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SendMailRequest _$SendMailRequestFromJson(Map<String, dynamic> json) =>
    SendMailRequest(
      email: json['email'] as String? ?? '',
      type: $enumDecodeNullable(_$SendMailTypeEnumMap, json['type']) ??
          SendMailType.register,
    );

Map<String, dynamic> _$SendMailRequestToJson(SendMailRequest instance) =>
    <String, dynamic>{
      'email': instance.email,
      'type': _$SendMailTypeEnumMap[instance.type]!,
    };

const _$SendMailTypeEnumMap = {
  SendMailType.register: 0,
  SendMailType.resetPwd: 2,
  SendMailType.updateEmail: 6,
};
