// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'offset_msg_request.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

OffsetMsgRequest _$OffsetMsgRequestFromJson(Map<String, dynamic> json) =>
    OffsetMsgRequest(
      channelId: json['channel_id'] as String? ?? '',
      channelType: (json['channel_type'] as num?)?.toInt() ?? 0,
      messageSeq: (json['message_seq'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$OffsetMsgRequestToJson(OffsetMsgRequest instance) =>
    <String, dynamic>{
      'channel_id': instance.channelId,
      'channel_type': instance.channelType,
      'message_seq': instance.messageSeq,
    };
