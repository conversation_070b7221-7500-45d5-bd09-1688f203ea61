// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'sync_conversation_request.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SyncConversationRequest _$SyncConversationRequestFromJson(
        Map<String, dynamic> json) =>
    SyncConversationRequest(
      version: (json['version'] as num?)?.toInt() ?? 0,
      lastMsgSeqs: json['last_msg_seqs'] as String? ?? '',
      msgCount: (json['msg_count'] as num?)?.toInt() ?? 10,
      deviceUuid: json['device_uuid'] as String? ?? '',
    );

Map<String, dynamic> _$SyncConversationRequestToJson(
        SyncConversationRequest instance) =>
    <String, dynamic>{
      'version': instance.version,
      'last_msg_seqs': instance.lastMsgSeqs,
      'msg_count': instance.msgCount,
      'device_uuid': instance.deviceUuid,
    };
