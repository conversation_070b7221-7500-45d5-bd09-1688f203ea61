import 'package:json_annotation/json_annotation.dart';

part 'delete_msg_request.g.dart';

@JsonSerializable()
class DeleteMsgRequest {
  @J<PERSON><PERSON><PERSON>(name: 'channel_id', defaultValue: '')
  final String channelId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'channel_type', defaultValue: 0)
  final int channelType;
  @JsonKey(name: 'message_seq', defaultValue: 0)
  final int messageSeq;
  @JsonKey(name: 'message_id', defaultValue: '')
  final String messageId;

  const DeleteMsgRequest({
    required this.channelId,
    required this.channelType,
    required this.messageSeq,
    required this.messageId,
  });

  factory DeleteMsgRequest.fromJson(Map<String, dynamic> json) =>
      _$DeleteMsgRequestFromJson(json);

  Map<String, dynamic> toJson() => _$DeleteMsgRequestToJson(this);
}