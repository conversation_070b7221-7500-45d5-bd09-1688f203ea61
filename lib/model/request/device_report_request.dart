import 'package:json_annotation/json_annotation.dart';

part 'device_report_request.g.dart';

@JsonSerializable()
class DeviceReportRequest {
  @Json<PERSON>ey(name: 'referrer_url', defaultValue: '')
  final String referrerUrl;
  @J<PERSON><PERSON><PERSON>(name: 'referrer_click_time', defaultValue: 0)
  final int referrerClickTime;
  @Json<PERSON>ey(name: 'app_install_time', defaultValue: 0)
  final int appInstallTime;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'instant_experience_launched', defaultValue: 0)
  final int instantExperienceLaunched;
  @Json<PERSON>ey(name: 'device_id', defaultValue: '')
  final String deviceId;
  @JsonKey(name: 'device_model', defaultValue: '')
  final String deviceModel;
  @JsonKey(name: 'device_brand', defaultValue: '')
  final String deviceBrand;
  @<PERSON>son<PERSON>ey(name: 'device_manufacturer', defaultValue: '')
  final String deviceManufacturer;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'os_name', defaultValue: '')
  final String osName;
  @JsonKey(name: 'os_version', defaultValue: '')
  final String osVersion;
  @<PERSON>son<PERSON><PERSON>(name: 'screen_width', defaultValue: '')
  final String screenWidth;
  @JsonKey(name: 'screen_height', defaultValue: '')
  final String screenHeight;
  @JsonKey(name: 'screen_density', defaultValue: '')
  final String screenDensity;
  @JsonKey(name: 'cpu_abi', defaultValue: '')
  final String cpuAbi;

  const DeviceReportRequest({
    required this.referrerUrl,
    required this.referrerClickTime,
    required this.appInstallTime,
    required this.instantExperienceLaunched,
    required this.deviceId,
    required this.deviceModel,
    required this.deviceBrand,
    required this.deviceManufacturer,
    required this.osName,
    required this.osVersion,
    required this.screenWidth,
    required this.screenHeight,
    required this.screenDensity,
    required this.cpuAbi,
  });

  factory DeviceReportRequest.fromJson(Map<String, dynamic> json) =>
      _$DeviceReportRequestFromJson(json);

  Map<String, dynamic> toJson() => _$DeviceReportRequestToJson(this);
}
