import 'package:json_annotation/json_annotation.dart';

part 'message_extra_sync_request.g.dart';

@JsonSerializable()
class MessageExtraSyncRequest {
  @Json<PERSON>ey(name: 'channel_id', defaultValue: '')
  final String channelId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'channel_type', defaultValue: 0)
  final int channelType;
  @Json<PERSON>ey(name: 'extra_version', defaultValue: 0)
  final int extraVersion;
  @JsonKey(defaultValue: '')
  final String source;
  @JsonKey(defaultValue: 0)
  final int limit;

  const MessageExtraSyncRequest({
    required this.channelId,
    required this.channelType,
    required this.extraVersion,
    required this.source,
    required this.limit,
  });

  factory MessageExtraSyncRequest.fromJson(Map<String, dynamic> json) =>
      _$MessageExtraSyncRequestFromJson(json);

  Map<String, dynamic> toJson() => _$MessageExtraSyncRequestToJson(this);
}
