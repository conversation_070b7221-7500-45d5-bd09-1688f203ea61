import 'package:json_annotation/json_annotation.dart';

part 'sync_conversation_extra_request.g.dart';

@JsonSerializable()
class SyncConversationExtraRequest {
  @JsonKey(defaultValue: 0)
  final int version;

  const SyncConversationExtraRequest({
    required this.version,
  });

  factory SyncConversationExtraRequest.fromJson(Map<String, dynamic> json) =>
      _$SyncConversationExtraRequestFromJson(json);

  Map<String, dynamic> toJson() => _$SyncConversationExtraRequestToJson(this);
}
