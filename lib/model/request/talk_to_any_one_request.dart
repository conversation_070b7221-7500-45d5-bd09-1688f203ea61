import 'package:json_annotation/json_annotation.dart';

part 'talk_to_any_one_request.g.dart';

@JsonSerializable()
class TalkToAnyOneRequest {
  @Json<PERSON>ey(name: 'received_uid', defaultValue: '')
  final String receivedUid;
  @Json<PERSON>ey(defaultValue: '')
  final String content;

  const TalkToAnyOneRequest({
    required this.receivedUid,
    required this.content,
  });

  factory TalkToAnyOneRequest.fromJson(Map<String, dynamic> json) =>
      _$TalkToAnyOneRequestFromJson(json);

  Map<String, dynamic> toJson() => _$TalkToAnyOneRequestToJson(this);
}
