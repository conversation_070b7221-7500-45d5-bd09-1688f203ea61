import 'package:json_annotation/json_annotation.dart';

part 'update_remark_request.g.dart';

@JsonSerializable()
class UpdateRemarkRequest {
  @JsonKey(defaultValue: '')
  final String uid;
  @<PERSON><PERSON><PERSON>ey(defaultValue: '')
  final String remark;

  const UpdateRemarkRequest({
    required this.uid,
    required this.remark,
  });

  factory UpdateRemarkRequest.fromJson(Map<String, dynamic> json) =>
      _$UpdateRemarkRequestFromJson(json);

  Map<String, dynamic> toJson() => _$UpdateRemarkRequestToJson(this);
}
