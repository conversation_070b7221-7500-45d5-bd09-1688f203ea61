import 'package:json_annotation/json_annotation.dart';

part 'call_request.g.dart';

@JsonSerializable()
class CallRequest {
  @JsonKey(name: 'room_id')
  final String roomId;
  @J<PERSON><PERSON>ey(name: 'call_type')
  final int callType;

  const CallRequest({
    required this.roomId,
    required this.callType,
  });

  factory CallRequest.fromJson(Map<String, dynamic> json) =>
      _$CallRequestFromJson(json);

  Map<String, dynamic> toJson() => _$CallRequestToJson(this);
}