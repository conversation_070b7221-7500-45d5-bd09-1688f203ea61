import 'package:json_annotation/json_annotation.dart';

part 'send_sms_request.g.dart';

@JsonEnum(valueField: "type")
enum SendCodeType {
  register("REGISTER"),//注册
  payPwd("PAY_PWD"),//支付密码
  forgetLoginPwd("FORGET_LOGIN_PWD"),//忘记登录密码
  checkMobile("CHECK_MOBILE"),//校验指定手机号是否正确
  destroyAccount("DESTROY_ACCOUNT"),//注销账号
  updatePhone("UPDATE_PHONE");//更改手机号

  final String type;

  const SendCodeType(this.type);
}

@JsonSerializable()
class SendSMSRequest {
  @JsonKey(defaultValue: '')
  final String zone;
  final String phone;
  @JsonKey(name: 'code_type', defaultValue: SendCodeType.register)
  final SendCodeType codeType; // 验证码类型（0：注册 1：支付密码 2：忘记登录密码 3：校验指定手机号是否正确 4：注销账号 5：更改手机号）没传默认是0

  const SendSMSRequest({
    required this.zone,
    required this.phone,
    required this.codeType,
  });

  factory SendSMSRequest.fromJson(Map<String, dynamic> json) =>
      _$SendSMSRequestFromJson(json);

  Map<String, dynamic> toJson() => _$SendSMSRequestToJson(this);
}