import 'package:json_annotation/json_annotation.dart';

part 'update_email_request.g.dart';

@JsonSerializable()
class UpdateEmailRequest {
  final String email;
  final String code;
  final String? pwd;//首次绑定不需要，之后必传，判断密码是否正确

  const UpdateEmailRequest({
    required this.email,
    required this.code,
    this.pwd,
  });

  factory UpdateEmailRequest.fromJson(Map<String, dynamic> json) =>
      _$UpdateEmailRequestFromJson(json);

  Map<String, dynamic> toJson() => _$UpdateEmailRequestToJson(this);
}