import 'package:json_annotation/json_annotation.dart';

part 'syncack_request.g.dart';

@JsonSerializable()
class SyncackRequest {
  @JsonKey(name:"device_uuid", defaultValue: '=')
  final String deviceUuid;

  const SyncackRequest({
    required this.deviceUuid,
  });

  factory SyncackRequest.fromJson(Map<String, dynamic> json) =>
      _$SyncackRequestFromJson(json);

  Map<String, dynamic> toJson() => _$SyncackRequestToJson(this);
}
