import 'package:json_annotation/json_annotation.dart';

part 'pwd_forget_sms_request.g.dart';

@JsonSerializable()
class PwdForgetSmsRequest {
  @JsonKey(defaultValue: '')
  final String zone;
  @Json<PERSON>ey(defaultValue: '')
  final String phone;
  @J<PERSON><PERSON>ey(defaultValue: '')
  final String code;
  @JsonKey(defaultValue: '')
  final String pwd;

  const PwdForgetSmsRequest({
    required this.zone,
    required this.phone,
    required this.code,
    required this.pwd,
  });

  factory PwdForgetSmsRequest.fromJson(Map<String, dynamic> json) =>
      _$PwdForgetSmsRequestFromJson(json);

  Map<String, dynamic> toJson() => _$PwdForgetSmsRequestToJson(this);
}
