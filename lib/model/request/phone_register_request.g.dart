// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'phone_register_request.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PhoneRegisterRequest _$PhoneRegisterRequestFromJson(
        Map<String, dynamic> json) =>
    PhoneRegisterRequest(
      zone: json['zone'] as String? ?? '',
      phone: json['phone'] as String? ?? '',
      code: json['code'] as String? ?? '',
      password: json['password'] as String? ?? '',
      inviteCode: json['invite_code'] as String? ?? '',
      flag: (json['flag'] as num?)?.toInt() ?? 0,
      device: json['device'] == null
          ? null
          : Device.fromJson(json['device'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$PhoneRegisterRequestToJson(
        PhoneRegisterRequest instance) =>
    <String, dynamic>{
      'zone': instance.zone,
      'phone': instance.phone,
      'code': instance.code,
      'password': instance.password,
      'invite_code': instance.inviteCode,
      'flag': instance.flag,
      'device': instance.device,
    };
