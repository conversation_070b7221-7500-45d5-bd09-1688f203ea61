import 'package:json_annotation/json_annotation.dart';

import 'message.dart';

part 'channel_msg_sync.g.dart';

@JsonSerializable()
class ChannelMsgSync {
  @Json<PERSON>ey(name: 'start_message_seq', defaultValue: 0)
  final int startMessageSeq;
  @<PERSON>son<PERSON>ey(name: 'end_message_seq', defaultValue: 0)
  final int endMessageSeq;
  @<PERSON>sonKey(name: 'pull_mode', defaultValue: 0)
  final int pullMode;
  @Json<PERSON>ey(defaultValue: 0)
  final int more;
  @<PERSON>son<PERSON>ey(defaultValue: [])
  final List<Message> messages;

  const ChannelMsgSync({
    required this.startMessageSeq,
    required this.endMessageSeq,
    required this.pullMode,
    required this.more,
    required this.messages,
  });

  factory ChannelMsgSync.fromJson(Map<String, dynamic> json) =>
      _$ChannelMsgSyncFromJson(json);

  Map<String, dynamic> toJson() => _$ChannelMsgSyncToJson(this);
}

