import 'package:json_annotation/json_annotation.dart';

part 'channel_state.g.dart';

@JsonSerializable()
class ChannelState {
  @JsonKey(name: 'signal_on',defaultValue: 0)
  final int signalOn;
  @JsonKey(name: 'online_count', defaultValue: 0)
  final int onlineCount;

  const ChannelState({
    required this.signalOn,
    required this.onlineCount,
  });

  factory ChannelState.fromJson(Map<String, dynamic> json) =>
      _$ChannelStateFromJson(json);

  Map<String, dynamic> toJson() => _$ChannelStateToJson(this);
}
