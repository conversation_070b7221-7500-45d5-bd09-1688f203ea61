import 'package:json_annotation/json_annotation.dart';

part 'sync_unread_count.g.dart';

@JsonSerializable()
class SyncUnreadCount {
  @JsonKey(defaultValue: 0)
  final int unread;

  const SyncUnreadCount({
    required this.unread,
  });

  factory SyncUnreadCount.fromJson(Map<String, dynamic> json) =>
      _$SyncUnreadCountFromJson(json);

  Map<String, dynamic> toJson() => _$SyncUnreadCountToJson(this);
}
