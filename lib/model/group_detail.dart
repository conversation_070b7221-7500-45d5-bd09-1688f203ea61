import 'package:json_annotation/json_annotation.dart';

part 'group_detail.g.dart';

@JsonSerializable()
class GroupDetail {
  @Json<PERSON>ey(name: 'group_no', defaultValue: '')
  final String groupNo;
  @Json<PERSON>ey(defaultValue: '')
  final String name;
  @Json<PERSON>ey(defaultValue: '')
  final String notice;
  @Json<PERSON>ey(defaultValue: 0)
  final int forbidden;
  @<PERSON>son<PERSON>ey(name: 'created_at', defaultValue: '')
  final String createdAt;
  @<PERSON>son<PERSON>ey(name: 'updated_at', defaultValue: '')
  final String updatedAt;
  @Json<PERSON>ey(name: 'member_count', defaultValue: 0)
  final int memberCount;
  @JsonKey(defaultValue: 0)
  final int version;

  const GroupDetail({
    required this.groupNo,
    required this.name,
    required this.notice,
    required this.forbidden,
    required this.createdAt,
    required this.updatedAt,
    required this.memberCount,
    required this.version,
  });

  factory GroupDetail.fromJson(Map<String, dynamic> json) =>
      _$GroupDetailFromJson(json);

  Map<String, dynamic> toJson() => _$GroupDetailToJson(this);
}
