import 'package:json_annotation/json_annotation.dart';

import 'message_extra.dart';

part 'group.g.dart';

@JsonSerializable()
class Group {
  @Json<PERSON>ey(name: 'group_no', defaultValue: '')
  final String groupNo;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'group_type', defaultValue: 0)
  final int groupType;
  @<PERSON><PERSON><PERSON><PERSON>(defaultValue: '')
  final String name;
  @<PERSON><PERSON><PERSON><PERSON>(defaultValue: '')
  final String remark;
  @<PERSON><PERSON><PERSON><PERSON>(defaultValue: '')
  final String notice;
  @Json<PERSON>ey(defaultValue: 0)
  final int mute;
  @<PERSON><PERSON><PERSON><PERSON>(defaultValue: 0)
  final int top;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'show_nick', defaultValue: 0)
  final int showNick;
  @<PERSON><PERSON><PERSON><PERSON>(defaultValue: 0)
  final int save;
  @Json<PERSON>ey(defaultValue: 0)
  final int forbidden;
  @<PERSON>son<PERSON>ey(defaultValue: 0)
  final int invite;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'chat_pwd_on', defaultValue: 0)
  final int chatPwdOn;
  @<PERSON><PERSON><PERSON><PERSON>(defaultValue: 0)
  final int screenshot;
  @J<PERSON><PERSON><PERSON>(name: 'revoke_remind', defaultValue: 0)
  final int revokeRemind;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'join_group_remind', defaultValue: 0)
  final int joinGroupRemind;
  @JsonKey(name: 'forbidden_add_friend', defaultValue: 0)
  final int forbiddenAddFriend;
  @JsonKey(defaultValue: 0)
  final int status;
  @JsonKey(defaultValue: 0)
  final int receipt;
  @JsonKey(defaultValue: 0)
  final int flame;
  @JsonKey(name: 'flame_second', defaultValue: 0)
  final int flameSecond;
  @JsonKey(name: 'allow_view_history_msg', defaultValue: 0)
  final int allowViewHistoryMsg;
  @JsonKey(name: 'member_count', defaultValue: 0)
  final int memberCount;
  @JsonKey(name: 'online_count', defaultValue: 0)
  final int onlineCount;
  @JsonKey(defaultValue: 0)
  final int quit;
  @JsonKey(defaultValue: 0)
  final int role;
  @JsonKey(name: 'forbidden_expir_time', defaultValue: 0)
  final int forbiddenExpirTime;
  @JsonKey(defaultValue: 0)
  final int version;
  @JsonKey(name: 'updated_at', defaultValue: '')
  final String updatedAt;
  @JsonKey(name: 'created_at', defaultValue: '')
  final String createdAt;

  const Group({
    required this.groupNo,
    required this.groupType,
    required this.name,
    required this.remark,
    required this.notice,
    required this.mute,
    required this.top,
    required this.showNick,
    required this.save,
    required this.forbidden,
    required this.invite,
    required this.chatPwdOn,
    required this.screenshot,
    required this.revokeRemind,
    required this.joinGroupRemind,
    required this.forbiddenAddFriend,
    required this.status,
    required this.receipt,
    required this.flame,
    required this.flameSecond,
    required this.allowViewHistoryMsg,
    required this.memberCount,
    required this.onlineCount,
    required this.quit,
    required this.role,
    required this.forbiddenExpirTime,
    required this.version,
    required this.updatedAt,
    required this.createdAt,
  });

  factory Group.fromJson(Map<String, dynamic> json) =>
      _$GroupFromJson(json);

  Map<String, dynamic> toJson() => _$GroupToJson(this);
}

