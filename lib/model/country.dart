import 'package:copy_with_extension/copy_with_extension.dart';
import 'package:json_annotation/json_annotation.dart';

part 'country.g.dart';

@JsonSerializable()
@CopyWith()
class Country {
  @JsonKey(defaultValue: '')
  final String code;
  @J<PERSON><PERSON>ey(defaultValue: '')
  final String icon;
  @J<PERSON><PERSON>ey(defaultValue: '')
  final String name;

  Country({
    required this.code,
    required this.icon,
    required this.name,
  });

  factory Country.fromJson(Map<String, dynamic> json) =>
      _$CountryFromJson(json);

  Map<String, dynamic> toJson() => _$CountryToJson(this);
}
