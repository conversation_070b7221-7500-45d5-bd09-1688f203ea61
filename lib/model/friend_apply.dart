import 'package:json_annotation/json_annotation.dart';

part 'friend_apply.g.dart';

@JsonEnum(valueField: "status")
enum FriendApplyStatus {
  @JsonValue(0)
  pending,
  @JsonValue(1)
  agree,
  @JsonValue(2)
  refuse,
  @JsonValue(3)
  expired,
}

@JsonSerializable()
class FriendApply {
  @JsonKey(defaultValue: 0)
  final int id;
  @JsonKey(defaultValue: '')
  final String uid;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'to_uid', defaultValue: '')
  final String toUid;
  @Json<PERSON>ey(name: 'to_name', defaultValue: '')
  final String toName;
  @JsonKey(defaultValue: '')
  final String remark;
  final FriendApplyStatus status;
  @JsonKey(defaultValue: '')
  final String token;
  @Json<PERSON>ey(name: 'created_at', defaultValue: '')
  final String createdAt;

  const FriendApply({
    required this.id,
    required this.uid,
    required this.toUid,
    required this.toName,
    required this.remark,
    required this.status,
    required this.token,
    required this.createdAt,
  });

  factory FriendApply.fromJson(Map<String, dynamic> json) =>
      _$FriendApplyFromJson(json);

  Map<String, dynamic> toJson() => _$FriendApplyToJson(this);
}
