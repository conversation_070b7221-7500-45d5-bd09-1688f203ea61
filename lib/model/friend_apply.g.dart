// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'friend_apply.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

FriendApply _$FriendApplyFromJson(Map<String, dynamic> json) => FriendApply(
      id: (json['id'] as num?)?.toInt() ?? 0,
      uid: json['uid'] as String? ?? '',
      toUid: json['to_uid'] as String? ?? '',
      toName: json['to_name'] as String? ?? '',
      remark: json['remark'] as String? ?? '',
      status: $enumDecode(_$FriendApplyStatusEnumMap, json['status']),
      token: json['token'] as String? ?? '',
      createdAt: json['created_at'] as String? ?? '',
    );

Map<String, dynamic> _$FriendApplyToJson(FriendApply instance) =>
    <String, dynamic>{
      'id': instance.id,
      'uid': instance.uid,
      'to_uid': instance.toUid,
      'to_name': instance.toName,
      'remark': instance.remark,
      'status': _$FriendApplyStatusEnumMap[instance.status]!,
      'token': instance.token,
      'created_at': instance.createdAt,
    };

const _$FriendApplyStatusEnumMap = {
  FriendApplyStatus.pending: 0,
  FriendApplyStatus.agree: 1,
  FriendApplyStatus.refuse: 2,
  FriendApplyStatus.expired: 3,
};
