import 'package:json_annotation/json_annotation.dart';

part 'sticker_info.g.dart';

@JsonSerializable()
class StickerInfo {
  @Json<PERSON>ey(defaultValue: '')
  final String uid;
  @Json<PERSON>ey(name: 'file_path', defaultValue: '')
  final String filePath;
  @Json<PERSON>ey(defaultValue: 0)
  final int id;
  @JsonKey(defaultValue: 0)
  final int width;
  @JsonKey(defaultValue: 0)
  final int height;

  const StickerInfo({
    required this.filePath,
    this.uid = '',
    this.id = 0,
    this.width = 0,
    this.height = 0,
  });

  factory StickerInfo.fromJson(Map<String, dynamic> json) =>
      _$StickerInfoFromJson(json);

  Map<String, dynamic> toJson() => _$StickerInfoToJson(this);
}
