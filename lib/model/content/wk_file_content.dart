import 'package:and/l10n/l10n.dart';
import 'package:wukongimfluttersdk/model/wk_media_message_content.dart';
import 'package:wukongimfluttersdk/model/wk_message_content.dart';
import 'package:wukongimfluttersdk/type/const.dart';

import '../../app.dart';

class WKFileContent extends WKMediaMessageContent {
  int size = 0;
  String name = "";
  String extension = "";

  WKFileContent() {
    contentType = WkMessageContentType.file;
  }

  @override
  Map<String, dynamic> encodeJson() {
    return {'name': name, 'extension': extension, 'size': size, 'url': url, 'localPath': localPath};
  }

  @override
  WKMessageContent decodeJson(Map<String, dynamic> json) {
    name = readString(json, 'name');
    extension = readString(json, 'extension');
    size = readInt(json, 'size');
    url = readString(json, 'url');
    localPath = readString(json, 'localPath');
    return this;
  }

  @override
  String displayText() {
    return globalContext?.l10n.msgFile ?? '[文件]';
  }

  @override
  String searchableWord() {
    return globalContext?.l10n.msgFile ?? '[文件]';
  }
}
