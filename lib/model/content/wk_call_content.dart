import 'package:wukongimfluttersdk/model/wk_message_content.dart';

class WkCallContent extends WKMessageContent {
  late int type; // 消息类型 -102发起人取消通话 -103结束通话 -104邀请通话超时 -105拒绝通话 -106未接来电
  late int callType; // 通话类型 1:音频 2:视频
  late String content; // 通话状态文案
  int? duration; // 通话时长（秒）
  String roomId; // 房间ID
  String sourceUid; // 源用户ID

  WkCallContent({
    this.type = -103,
    this.callType = 1,
    this.content = '',
    this.duration,
    this.roomId = '',
    this.sourceUid = '',
  }) {
    contentType = type;
  }

  @override
  WKMessageContent decodeJson(Map<String, dynamic> jsonObject) {
    var rawCallType = jsonObject['callType'];
    callType =
        rawCallType is String ? int.parse(rawCallType) : rawCallType as int;
    content = jsonObject['content'] as String;
    duration = jsonObject['duration'] as int?;
    roomId = jsonObject['roomId'] as String? ?? '';
    sourceUid = jsonObject['sourceUid'] as String? ?? '';
    type = jsonObject['type'] as int;
    contentType = type;
    return this;
  }

  @override
  Map<String, dynamic> encodeJson() {
    return {
      'type': type,
      'callType': callType,
      'content': content,
      if (duration != null) 'duration': duration,
      'roomId': roomId,
      'sourceUid': sourceUid,
    };
  }

  @override
  String displayText() {
    return content;
  }

  @override
  String searchableWord() {
    return content;
  }
}
