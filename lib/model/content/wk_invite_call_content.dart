import 'package:wukongimfluttersdk/model/wk_message_content.dart';

class WkInviteCallContent extends WKMessageContent {
  String cmd = 'singleCall';
  late int type;
  late int callType; // 通话类型 1:音频 2:视频
  late String content; // 通话状态文案
  String roomId; // 房间ID
  String livekitToken; // LiveKit Token
  List<ExtraInfo> extra; // 额外信息，包含name和uid
  String channelId; // 频道ID
  int channelType; // 频道类型

  WkInviteCallContent({
    this.type = 99,
    this.callType = 1,
    this.content = '',
    this.roomId = '',
    this.livekitToken = '',
    this.extra = const [],
    this.channelId = '',
    this.channelType = 1
  }) {
    contentType = type;
  }

  @override
  WKMessageContent decodeJson(Map<String, dynamic> jsonObject) {
    cmd = jsonObject['cmd'] as String;
    type = jsonObject['type'] as int;
    
    final param = jsonObject['param'] as Map<String, dynamic>;
    content = param['content'] as String;
    callType = param['callType'] as int;
    roomId = param['roomId'] as String? ?? '';
    livekitToken = param['livekitToken'] as String? ?? '';
    channelId = param['channel_id'] as String? ?? '';
    channelType = param['channel_type'] as int? ?? 1;
    
    if (param['extra'] != null) {
      final extraList = param['extra'] as List;
      extra = extraList.map((item) => ExtraInfo.fromJson(item as Map<String, dynamic>)).toList();
    } else {
      extra = [];
    }
    
    contentType = type;
    return this;
  }

  @override
  Map<String, dynamic> encodeJson() {
    return {
      'cmd': cmd,
      'type': type,
      'param': {
        'content': content,
        'callType': callType,
        'roomId': roomId,
        'livekitToken': livekitToken,
        'extra': extra.map((e) => e.toJson()).toList(),
        'channel_id': channelId,
        'channel_type': channelType,
      }
    };
  }
}

class ExtraInfo {
  String name;
  String uid;

  ExtraInfo({required this.name, required this.uid});

  factory ExtraInfo.fromJson(Map<String, dynamic> json) {
    return ExtraInfo(
      name: json['name'] as String? ?? '',
      uid: json['uid'] as String? ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'uid': uid,
    };
  }
}