import 'dart:convert';

import 'package:and/app.dart';
import 'package:and/cache/cache_helper.dart';
import 'package:and/constant/wk_message_content_type_ext.dart';
import 'package:and/l10n/l10n.dart';
import 'package:wukongimfluttersdk/entity/channel.dart';
import 'package:wukongimfluttersdk/entity/msg.dart';
import 'package:wukongimfluttersdk/model/wk_message_content.dart';
import 'package:wukongimfluttersdk/type/const.dart';
import 'package:wukongimfluttersdk/wkim.dart';

class WKMultiForwardContent extends WKMessageContent {
  int channelType;
  String channelId;
  String uid;
  List<WKChannel> userList;
  List<WKMsg> msgList;

  WKMultiForwardContent({
    this.channelType = 0,
    this.channelId = "",
    this.uid = "",
    this.userList = const [],
    this.msgList = const [],
  }) {
    contentType = WkMessageContentTypeExt.multiForward;
  }

  @override
  WKMessageContent decodeJson(Map<String, dynamic> jsonObject) {
    channelType = jsonObject['channel_type'] ?? 0;
    channelId = jsonObject['channel_id'] ?? '';
    uid = jsonObject['uid'] ?? '';
    List<WKMsg> msgs = [];
    if (jsonObject['msgs'] != null && jsonObject['msgs'] is List) {
      for (var msgJson in jsonObject['msgs']) {
        WKMsg msg = WKMsg();
        if (msgJson['payload'] != null &&
            msgJson['payload'] is Map<String, dynamic>) {
          var contentJson = msgJson['payload'];
          if (contentJson != null && contentJson is Map<String, dynamic>) {
            var type = contentJson['type'] ?? WkMessageContentType.unknown;
            msg.content = jsonEncode(contentJson);
            msg.messageContent =
                WKIM.shared.messageManager.getMessageModel(type, contentJson);
            if (msg.messageContent != null) {
              msg.contentType = msg.messageContent!.contentType;
            }
          }
        }
        msg.timestamp = msgJson['timestamp'] ?? 0;
        msg.messageID = msgJson['message_id'] ?? '';
        if (msgJson.containsKey('from_uid')) {
          msg.fromUID = msgJson['from_uid'] ?? '';
        }
        msgs.add(msg);
      }
    }
    msgs.sort((a, b) {
      return a.timestamp.compareTo(b.timestamp);
    });
    msgList = msgs;

    List<WKChannel> users = [];
    if (jsonObject['users'] != null && jsonObject['users'] is List) {
      for (var userJson in jsonObject['users']) {
        WKChannel channel =
            WKChannel(userJson['uid'] ?? '', WKChannelType.personal);
        channel.channelName = userJson['name'] ?? '';
        channel.avatar = userJson['avatar'] ?? '';
        users.add(channel);
      }
    }
    userList = users;

    return this;
  }

  @override
  Map<String, dynamic> encodeJson() {
    Map<String, dynamic> jsonObject = {
      'channel_type': channelType,
      'channel_id': channelId,
      'uid': uid,
      'msgs': msgList.map((msg) {
        Map<String, dynamic> json = {};
        if (msg.content.isNotEmpty) {
          json['payload'] = jsonDecode(msg.content);
        }
        json['timestamp'] = msg.timestamp;
        json['message_id'] = msg.messageID;
        json['from_uid'] = msg.fromUID;
        return json;
      }).toList(),
    };

    if (userList.isNotEmpty) {
      jsonObject['users'] = userList.map((user) {
        return {
          'uid': user.channelID,
          'name': user.channelName,
          'avatar': user.avatar,
        };
      }).toList();
    }

    return jsonObject;
  }

  @override
  String displayText() {
    return globalContext?.l10n.lastMsgChatRecord ?? "[聊天记录]";
  }

  @override
  String searchableWord() {
    return globalContext?.l10n.lastMsgChatRecord ?? "[聊天记录]";
  }
}
