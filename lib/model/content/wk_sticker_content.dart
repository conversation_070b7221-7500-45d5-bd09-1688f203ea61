import 'package:and/constant/wk_message_content_type_ext.dart';
import 'package:and/l10n/l10n.dart';
import 'package:wukongimfluttersdk/model/wk_image_content.dart';
import 'package:wukongimfluttersdk/model/wk_message_content.dart';

import '../../app.dart';

class WKStickerContent extends WKImageContent {
  WKStickerContent(super.width, super.height) {
    contentType = WkMessageContentTypeExt.sticker;
  }

  @override
  Map<String, dynamic> encodeJson() {
    return {
      'width': width,
      'height': height,
      'url': url,
      'localPath': localPath
    };
  }

  @override
  WKMessageContent decodeJson(Map<String, dynamic> json) {
    width = readInt(json, 'width');
    height = readInt(json, 'height');
    url = readString(json, 'url');
    localPath = readString(json, 'localPath');
    return this;
  }

  @override
  String displayText() {
    return globalContext?.l10n.msgSticker ?? '[表情]';
  }

  @override
  String searchableWord() {
    return globalContext?.l10n.msgSticker ?? '[表情]';
  }
}
