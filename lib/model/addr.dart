import 'package:json_annotation/json_annotation.dart';

part 'addr.g.dart';

@JsonSerializable()
class Addr {
  @JsonKey(name: 'tcp_addr', defaultValue: '')
  final String tcpAddr;
  @JsonKey(name: 'ws_addr', defaultValue: '')
  final String wsAddr;
  @Json<PERSON>ey(name: 'wss_addr', defaultValue: '')
  final String wssAddr;

  const Addr({
    required this.tcpAddr,
    required this.wsAddr,
    required this.wssAddr,
  });

  factory Addr.fromJson(Map<String, dynamic> json) =>
      _$AddrFromJson(json);

  Map<String, dynamic> toJson() => _$AddrToJson(this);
}
