// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'message_extra.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MessageExtra _$MessageExtraFromJson(Map<String, dynamic> json) => MessageExtra(
      messageId: (json['message_id'] as num?)?.toInt() ?? 0,
      messageIdStr: json['message_id_str'] as String? ?? '',
      revoke: (json['revoke'] as num?)?.toInt() ?? 0,
      revoker: json['revoker'] as String? ?? '',
      voiceStatus: (json['voice_status'] as num?)?.toInt() ?? 0,
      readed: (json['readed'] as num?)?.toInt() ?? 0,
      readedCount: (json['readed_count'] as num?)?.toInt() ?? 0,
      readedAt: (json['readed_at'] as num?)?.toInt() ?? 0,
      isMutualDeleted: (json['is_mutual_deleted'] as num?)?.toInt() ?? 0,
      contentEdit: json['content_edit'],
      editedAt: (json['edited_at'] as num?)?.toInt() ?? 0,
      extraVersion: (json['extra_version'] as num?)?.toInt() ?? 0,
      isPinned: (json['is_pinned'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$MessageExtraToJson(MessageExtra instance) =>
    <String, dynamic>{
      'message_id': instance.messageId,
      'message_id_str': instance.messageIdStr,
      'revoke': instance.revoke,
      'revoker': instance.revoker,
      'voice_status': instance.voiceStatus,
      'readed': instance.readed,
      'readed_count': instance.readedCount,
      'readed_at': instance.readedAt,
      'is_mutual_deleted': instance.isMutualDeleted,
      'content_edit': instance.contentEdit,
      'edited_at': instance.editedAt,
      'extra_version': instance.extraVersion,
      'is_pinned': instance.isPinned,
    };
