import 'package:json_annotation/json_annotation.dart';

part 'message_extra.g.dart';

@JsonSerializable()
class MessageExtra {
  @J<PERSON><PERSON><PERSON>(name: 'message_id', defaultValue: 0)
  final int messageId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'message_id_str', defaultValue: '')
  final String messageIdStr;
  @<PERSON><PERSON><PERSON><PERSON>(defaultValue: 0)
  final int revoke;
  @<PERSON><PERSON><PERSON><PERSON>(defaultValue: '')
  final String revoker;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'voice_status', defaultValue: 0)
  final int voiceStatus;
  @<PERSON><PERSON><PERSON><PERSON>(defaultValue: 0)
  final int readed;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'readed_count', defaultValue: 0)
  final int readedCount;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'readed_at', defaultValue: 0)
  final int readedAt;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'is_mutual_deleted', defaultValue: 0)
  final int isMutualDeleted;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'content_edit')
  final dynamic contentEdit;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'edited_at', defaultValue: 0)
  final int editedAt;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'extra_version', defaultValue: 0)
  final int extraVersion;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'is_pinned', defaultValue: 0)
  final int isPinned;

  const MessageExtra({
    required this.messageId,
    required this.messageIdStr,
    required this.revoke,
    required this.revoker,
    required this.voiceStatus,
    required this.readed,
    required this.readedCount,
    required this.readedAt,
    required this.isMutualDeleted,
    this.contentEdit,
    required this.editedAt,
    required this.extraVersion,
    required this.isPinned
  });

  factory MessageExtra.fromJson(Map<String, dynamic> json) =>
      _$MessageExtraFromJson(json);

  Map<String, dynamic> toJson() => _$MessageExtraToJson(this);
}
