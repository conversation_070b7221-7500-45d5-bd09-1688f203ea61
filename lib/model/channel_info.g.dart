// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'channel_info.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ChannelInfo _$ChannelInfoFromJson(Map<String, dynamic> json) => ChannelInfo(
      channel: json['channel'] == null
          ? null
          : Channel.fromJson(json['channel'] as Map<String, dynamic>),
      parentChannel: json['parent_channel'] == null
          ? null
          : ParentChannel.fromJson(
              json['parent_channel'] as Map<String, dynamic>),
      name: json['name'] as String? ?? '',
      logo: json['logo'] as String? ?? '',
      remark: json['remark'] as String? ?? '',
      status: (json['status'] as num?)?.toInt() ?? 0,
      online: (json['online'] as num?)?.toInt() ?? 0,
      lastOffline: (json['last_offline'] as num?)?.toInt() ?? 0,
      receipt: (json['receipt'] as num?)?.toInt() ?? 0,
      robot: (json['robot'] as num?)?.toInt() ?? 0,
      category: json['category'] as String? ?? '',
      stick: (json['stick'] as num?)?.toInt() ?? 0,
      mute: (json['mute'] as num?)?.toInt() ?? 0,
      showNick: (json['show_nick'] as num?)?.toInt() ?? 0,
      follow: (json['follow'] as num?)?.toInt() ?? 0,
      anonymous: (json['anonymous'] as num?)?.toInt() ?? 0,
      beDeleted: (json['be_deleted'] as num?)?.toInt() ?? 0,
      beBlacklist: (json['be_blacklist'] as num?)?.toInt() ?? 0,
      notice: json['notice'] as String? ?? '',
      save: (json['save'] as num?)?.toInt() ?? 0,
      forbidden: (json['forbidden'] as num?)?.toInt() ?? 0,
      invite: (json['invite'] as num?)?.toInt() ?? 0,
      deviceFlag: (json['device_flag'] as num?)?.toInt() ?? 0,
      extra: json['extra'],
      avChatDisabled: (json['av_chat_disabled'] as num?)?.toInt() ?? 0,
      phone: json['phone'] as String? ?? '',
      signature: json['signature'] as String? ?? '',
    );

Map<String, dynamic> _$ChannelInfoToJson(ChannelInfo instance) =>
    <String, dynamic>{
      'channel': instance.channel,
      'parent_channel': instance.parentChannel,
      'name': instance.name,
      'logo': instance.logo,
      'remark': instance.remark,
      'status': instance.status,
      'online': instance.online,
      'last_offline': instance.lastOffline,
      'receipt': instance.receipt,
      'robot': instance.robot,
      'category': instance.category,
      'stick': instance.stick,
      'mute': instance.mute,
      'show_nick': instance.showNick,
      'follow': instance.follow,
      'anonymous': instance.anonymous,
      'be_deleted': instance.beDeleted,
      'be_blacklist': instance.beBlacklist,
      'notice': instance.notice,
      'save': instance.save,
      'forbidden': instance.forbidden,
      'invite': instance.invite,
      'device_flag': instance.deviceFlag,
      'extra': instance.extra,
      'av_chat_disabled': instance.avChatDisabled,
      'phone': instance.phone,
      'signature': instance.signature,
    };

Channel _$ChannelFromJson(Map<String, dynamic> json) => Channel(
      channelId: json['channel_id'] as String? ?? '',
      channelType: (json['channel_type'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$ChannelToJson(Channel instance) => <String, dynamic>{
      'channel_id': instance.channelId,
      'channel_type': instance.channelType,
    };

ParentChannel _$ParentChannelFromJson(Map<String, dynamic> json) =>
    ParentChannel(
      channelId: json['channel_id'] as String? ?? '',
      channelType: (json['channel_type'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$ParentChannelToJson(ParentChannel instance) =>
    <String, dynamic>{
      'channel_id': instance.channelId,
      'channel_type': instance.channelType,
    };
