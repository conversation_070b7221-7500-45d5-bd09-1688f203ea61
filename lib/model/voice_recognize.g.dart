// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'voice_recognize.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

VoiceRecognize _$VoiceRecognizeFromJson(Map<String, dynamic> json) =>
    VoiceRecognize(
      event: $enumDecodeNullable(_$SteamEventEnumMap, json['event']) ??
          SteamEvent.begin,
      data: json['data'] == null
          ? null
          : Data.fromJson(json['data'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$VoiceRecognizeToJson(VoiceRecognize instance) =>
    <String, dynamic>{
      'event': _$SteamEventEnumMap[instance.event]!,
      'data': instance.data,
    };

const _$SteamEventEnumMap = {
  SteamEvent.begin: 'stream_begin',
  SteamEvent.text: 'stream_text',
  SteamEvent.end: 'stream_end',
};

Data _$DataFromJson(Map<String, dynamic> json) => Data(
      text: json['text'] as String? ?? '',
    );

Map<String, dynamic> _$DataToJson(Data instance) => <String, dynamic>{
      'text': instance.text,
    };
