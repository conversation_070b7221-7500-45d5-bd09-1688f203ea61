// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'friend_apply_count.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

FriendApplyCount _$FriendApplyCountFromJson(Map<String, dynamic> json) =>
    FriendApplyCount(
      count: (json['count'] as num?)?.toInt() ?? 0,
      isDot: (json['is_dot'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$FriendApplyCountToJson(FriendApplyCount instance) =>
    <String, dynamic>{
      'count': instance.count,
      'is_dot': instance.isDot,
    };
