import 'package:json_annotation/json_annotation.dart';

part 'channel_info.g.dart';

@JsonSerializable()
class ChannelInfo {
  final Channel? channel;
  @<PERSON>son<PERSON>ey(name: 'parent_channel')
  final ParentChannel? parentChannel;
  @J<PERSON><PERSON><PERSON>(defaultValue: '')
  final String name;
  @J<PERSON><PERSON><PERSON>(defaultValue: '')
  final String logo;
  @Json<PERSON><PERSON>(defaultValue: '')
  final String remark;
  @<PERSON>son<PERSON>ey(defaultValue: 0)
  final int status;
  @<PERSON><PERSON><PERSON><PERSON>(defaultValue: 0)
  final int online;
  @<PERSON>son<PERSON>ey(name: 'last_offline', defaultValue: 0)
  final int lastOffline;
  @<PERSON>son<PERSON><PERSON>(defaultValue: 0)
  final int receipt;
  @Json<PERSON>ey(defaultValue: 0)
  final int robot;
  @Json<PERSON>ey(defaultValue: '')
  final String category;
  @JsonKey(defaultValue: 0)
  final int stick;
  @<PERSON>son<PERSON><PERSON>(defaultValue: 0)
  final int mute;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'show_nick', defaultValue: 0)
  final int showNick;
  @<PERSON><PERSON><PERSON><PERSON>(defaultValue: 0)
  final int follow;
  @J<PERSON><PERSON><PERSON>(defaultValue: 0)
  final int anonymous;
  @J<PERSON><PERSON><PERSON>(name: 'be_deleted', defaultValue: 0)
  final int beDeleted;
  @JsonKey(name: 'be_blacklist', defaultValue: 0)
  final int beBlacklist;
  @JsonKey(defaultValue: '')
  final String notice;
  @JsonKey(defaultValue: 0)
  final int save;
  @JsonKey(defaultValue: 0)
  final int forbidden;
  @JsonKey(defaultValue: 0)
  final int invite;
  @JsonKey(name: 'device_flag', defaultValue: 0)
  final int deviceFlag;
  final dynamic extra;
  @JsonKey(name: 'av_chat_disabled', defaultValue: 0)
  final int avChatDisabled;
  @JsonKey(defaultValue: '')
  final String? phone;
  @JsonKey(defaultValue: '')
  final String signature;

  const ChannelInfo({
    this.channel,
    this.parentChannel,
    required this.name,
    required this.logo,
    required this.remark,
    required this.status,
    required this.online,
    required this.lastOffline,
    required this.receipt,
    required this.robot,
    required this.category,
    required this.stick,
    required this.mute,
    required this.showNick,
    required this.follow,
    required this.anonymous,
    required this.beDeleted,
    required this.beBlacklist,
    required this.notice,
    required this.save,
    required this.forbidden,
    required this.invite,
    required this.deviceFlag,
    this.extra,
    required this.avChatDisabled,
    this.phone,
    required this.signature,
  });

  factory ChannelInfo.fromJson(Map<String, dynamic> json) =>
      _$ChannelInfoFromJson(json);

  Map<String, dynamic> toJson() => _$ChannelInfoToJson(this);
}

@JsonSerializable()
class Channel {
  @JsonKey(name: 'channel_id', defaultValue: '')
  final String channelId;
  @JsonKey(name: 'channel_type', defaultValue: 0)
  final int channelType;

  const Channel({
    required this.channelId,
    required this.channelType,
  });

  factory Channel.fromJson(Map<String, dynamic> json) =>
      _$ChannelFromJson(json);

  Map<String, dynamic> toJson() => _$ChannelToJson(this);
}

@JsonSerializable()
class ParentChannel {
  @JsonKey(name: 'channel_id', defaultValue: '')
  final String channelId;
  @JsonKey(name: 'channel_type', defaultValue: 0)
  final int channelType;

  const ParentChannel({
    required this.channelId,
    required this.channelType,
  });

  factory ParentChannel.fromJson(Map<String, dynamic> json) =>
      _$ParentChannelFromJson(json);

  Map<String, dynamic> toJson() => _$ParentChannelToJson(this);
}
