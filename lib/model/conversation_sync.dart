import 'package:and/model/group.dart';
import 'package:and/model/user_info.dart';
import 'package:json_annotation/json_annotation.dart';

import 'conversation.dart';

part 'conversation_sync.g.dart';

@JsonSerializable()
class ConversationSync {
  @J<PERSON><PERSON>ey(defaultValue: '')
  final String uid;
  @Json<PERSON>ey(defaultValue: [])
  final List<Conversation> conversations;
  @Json<PERSON>ey(defaultValue: [])
  final List<UserInfo> users;
  @Json<PERSON>ey(defaultValue: [])
  final List<Group> groups;

  const ConversationSync({
    required this.uid,
    required this.conversations,
    required this.users,
    required this.groups,
  });

  factory ConversationSync.fromJson(Map<String, dynamic> json) =>
      _$ConversationSyncFromJson(json);

  Map<String, dynamic> toJson() => _$ConversationSyncToJson(this);

}

