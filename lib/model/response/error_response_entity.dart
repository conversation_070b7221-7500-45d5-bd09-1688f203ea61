import 'dart:convert';

import 'package:json_annotation/json_annotation.dart';

part 'error_response_entity.g.dart';

@JsonSerializable()
class ErrorResponseEntity    {
  @JsonKey(name: 'bizCode')
  int? code;
  int? status;
  @J<PERSON><PERSON>ey(name: 'msg')
  String? message;

  ErrorResponseEntity({this.code, this.status, this.message});

  bool get success => code == 200;

  bool get failed => !success;

  factory ErrorResponseEntity.fromJson(Map<String, dynamic> json) =>
      _$ErrorResponseEntityFromJson(json);

  Map<String, dynamic> toJson() => _$ErrorResponseEntityToJson(this);
}
