// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'search_user.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SearchUser _$SearchUserFromJson(Map<String, dynamic> json) => SearchUser(
      data: json['data'] == null
          ? null
          : Data.fromJson(json['data'] as Map<String, dynamic>),
      exist: (json['exist'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$SearchUserToJson(SearchUser instance) =>
    <String, dynamic>{
      'data': instance.data,
      'exist': instance.exist,
    };

Data _$DataFromJson(Map<String, dynamic> json) => Data(
      uid: json['uid'] as String? ?? '',
      name: json['name'] as String? ?? '',
      vercode: json['vercode'] as String? ?? '',
    );

Map<String, dynamic> _$DataToJson(Data instance) => <String, dynamic>{
      'uid': instance.uid,
      'name': instance.name,
      'vercode': instance.vercode,
    };
