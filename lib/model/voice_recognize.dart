import 'package:json_annotation/json_annotation.dart';

part 'voice_recognize.g.dart';

@JsonEnum(valueField: "type")
enum SteamEvent {
  begin("stream_begin"),
  text("stream_text"),
  end("stream_end");

  final String type;

  const SteamEvent(this.type);
}

@JsonSerializable()
class VoiceRecognize {
  @JsonKey(defaultValue: SteamEvent.begin)
  final SteamEvent event;
  final Data? data;

  const VoiceRecognize({
    required this.event,
    this.data,
  });

  factory VoiceRecognize.fromJson(Map<String, dynamic> json) =>
      _$VoiceRecognizeFromJson(json);

  Map<String, dynamic> toJson() => _$VoiceRecognizeToJson(this);
}

@JsonSerializable()
class Data {
  @JsonKey(defaultValue: '')
  final String text;

  const Data({
    required this.text,
  });

  factory Data.fromJson(Map<String, dynamic> json) =>
      _$DataFromJson(json);

  Map<String, dynamic> toJson() => _$DataTo<PERSON>son(this);
}
