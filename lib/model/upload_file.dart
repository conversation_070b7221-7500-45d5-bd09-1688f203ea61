import 'package:json_annotation/json_annotation.dart';

part 'upload_file.g.dart';

@JsonSerializable()
class UploadFile {
  @JsonKey(defaultValue: '')
  final String path;
  @JsonKey(defaultValue: '')
  final String sha512;

  const UploadFile({
    required this.path,
    required this.sha512,
  });

  factory UploadFile.fromJson(Map<String, dynamic> json) =>
      _$UploadFileFromJson(json);

  Map<String, dynamic> toJson() => _$UploadFileToJson(this);
}
