import 'package:json_annotation/json_annotation.dart';

part 'friend_apply_count.g.dart';

@JsonSerializable()
class FriendApplyCount {
  @JsonKey(defaultValue: 0)
  final int count;
  @<PERSON><PERSON><PERSON>ey(name: 'is_dot', defaultValue: 0)
  final int isDot;

  const FriendApplyCount({
    required this.count,
    required this.isDot,
  });

  factory FriendApplyCount.fromJson(Map<String, dynamic> json) =>
      _$FriendApplyCountFromJson(json);

  Map<String, dynamic> toJson() => _$FriendApplyCountToJson(this);
}
