import 'package:and/cache/cache_helper.dart';

class HttpConfig {
  static const String baseURL = 'https://api.gleezy.org';
  static const String baseTestURL = 'https://im-api-dev.testaibox.com';

  static const String baseApiURL = '$baseURL/v1';
  static const String baseTestApiURL = '$baseTestURL/v1';

  static getWebUrl() {
    return getBaseUrl() + "/web";
  }

  static getApiUrl() {
    return getBaseUrl() + "/v1";
  }

  static getBaseUrl() {
    return CacheHelper.devTestMode ? baseTestURL : baseURL;
  }
}
