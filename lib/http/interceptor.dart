import 'package:and/model/response/error_response_entity.dart';
import 'package:dio/dio.dart';
import 'package:oktoast/oktoast.dart';

class ErrorMessageInterceptor extends Interceptor {
  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    super.onResponse(response, handler);
    //获取请求的extra参数，根据参数判断是否需要toast异常信息
    final bool showError = response.extra['showErrorMsg'] ?? false;
    //自定义的业务错误
    if (response.statusCode != 200 && showError) {
      _onError(response, showError);
    }
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    super.onError(err, handler);
    //获取请求的extra参数，根据参数判断是否需要toast异常信息
    final bool showError = err.requestOptions.extra['showErrorMsg'] ?? false;
    _onError(err.response, showError);
  }

  void _onError(Response? response, bool showError) {
    if (response != null) {
      ErrorResponseEntity errorResponseEntity =
          ErrorResponseEntity.fromJson(response.data);
      if (errorResponseEntity.status != 200 && showError) {
        showToast(errorResponseEntity.message ?? "");
      }
    }
  }
}
