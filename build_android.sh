#!/bin/bash

# 显示帮助信息
show_help() {
  echo "用法: ./build.sh [打包类型]"
  echo ""
  echo "打包类型:"
  echo "  apk        构建 APK 包 (默认)"
  echo "  aab        构建 App Bundle 包"
  echo "  appbundle  构建 App Bundle 包 (aab 的别名)"
  echo "  all        同时构建 APK 和 App Bundle"
  echo "  help       显示帮助信息"
  echo ""
  echo "示例:"
  echo "  ./build.sh        # 构建 APK"
  echo "  ./build.sh apk    # 构建 APK"
  echo "  ./build.sh aab    # 构建 App Bundle"
  echo "  ./build.sh all    # 同时构建 APK 和 App Bundle"
  exit 0
}

# 默认打包类型为 apk
BUILD_TYPE="apk"

# 处理命令行参数
if [[ $# -gt 0 ]]; then
  case "$1" in
    help|--help|-h)
      show_help
      ;;
    *)
      # 直接使用第一个参数作为构建类型
      BUILD_TYPE="$1"
      ;;
  esac
fi

# 提取版本名称和构建号
VERSION_NAME=$(grep 'version:' pubspec.yaml | sed 's/version: //' | cut -d+ -f1 | tr -d ' ')
BUILD_NUMBER=$(grep 'version:' pubspec.yaml | sed 's/version: //' | cut -d+ -f2 | tr -d ' ')

# 根据打包类型执行不同的构建命令
case $BUILD_TYPE in
  "apk")
    echo "正在构建 APK..."
    flutter build apk --build-name=$VERSION_NAME --build-number=$BUILD_NUMBER

    # 重命名输出文件以包含版本信息
    mv build/app/outputs/apk/release/app-release.apk "build/app/outputs/apk/release/gleezy-${VERSION_NAME}-${BUILD_NUMBER}.apk"
    echo "APK 构建完成: build/app/outputs/apk/release/gleezy-${VERSION_NAME}-${BUILD_NUMBER}.apk"
    ;;

  "appbundle"|"aab")
    echo "正在构建 App Bundle..."
    flutter build appbundle --build-name=$VERSION_NAME --build-number=$BUILD_NUMBER

    # 重命名输出文件以包含版本信息
    mv build/app/outputs/bundle/release/app-release.aab "build/app/outputs/bundle/release/gleezy-${VERSION_NAME}-${BUILD_NUMBER}.aab"
    echo "App Bundle 构建完成: build/app/outputs/bundle/release/gleezy-${VERSION_NAME}-${BUILD_NUMBER}.aab"
    ;;

  "all")
    echo "正在构建 APK..."
    flutter build apk --build-name=$VERSION_NAME --build-number=$BUILD_NUMBER

    # 重命名 APK 输出文件
    mv build/app/outputs/apk/release/app-release.apk "build/app/outputs/apk/release/gleezy-${VERSION_NAME}-${BUILD_NUMBER}.apk"
    echo "APK 构建完成: build/app/outputs/apk/release/gleezy-${VERSION_NAME}-${BUILD_NUMBER}.apk"

    echo "正在构建 App Bundle..."
    flutter build appbundle --build-name=$VERSION_NAME --build-number=$BUILD_NUMBER

    # 重命名 App Bundle 输出文件
    mv build/app/outputs/bundle/release/app-release.aab "build/app/outputs/bundle/release/gleezy-${VERSION_NAME}-${BUILD_NUMBER}.aab"
    echo "App Bundle 构建完成: build/app/outputs/bundle/release/gleezy-${VERSION_NAME}-${BUILD_NUMBER}.aab"
    ;;

  *)
    echo "错误: 不支持的构建类型 '$BUILD_TYPE'"
    echo "请使用 './build.sh help' 查看支持的打包类型"
    exit 1
    ;;
esac