//
//  NotificationService.swift
//  NotificationServiceExtension
//
//  Created by 张飞 on 2025-04-30.
//

import UserNotifications

class NotificationService: UNNotificationServiceExtension {

    var contentHandler: ((UNNotificationContent) -> Void)?
    var bestAttemptContent: UNMutableNotificationContent?

    override func didReceive(_ request: UNNotificationRequest, withContentHandler contentHandler: @escaping (UNNotificationContent) -> Void) {
        self.contentHandler = contentHandler
        bestAttemptContent = (request.content.mutableCopy() as? UNMutableNotificationContent)
        
        if let bestAttemptContent = bestAttemptContent {
            UNUserNotificationCenter.current().getDeliveredNotifications { notifies in
                for notify in notifies {
                    if notify.request.identifier == request.identifier {
                        UNUserNotificationCenter.current().removeDeliveredNotifications(withIdentifiers: [notify.request.identifier])
                    }
                }
            }
            DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
                contentHandler(bestAttemptContent)
            }
        }
    }
    
    override func serviceExtensionTimeWillExpire() {
        // Called just before the extension will be terminated by the system.
        // Use this as an opportunity to deliver your "best attempt" at modified content, otherwise the original push payload will be used.
        if let contentHandler = contentHandler, let bestAttemptContent =  bestAttemptContent {
            contentHandler(bestAttemptContent)
        }
    }

}
